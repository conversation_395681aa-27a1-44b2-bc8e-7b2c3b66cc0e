SET NAMES utf8mb4;


# 转储表 account_integrates
# ------------------------------------------------------------

DROP TABLE IF EXISTS `account_integrates`;

CREATE TABLE `account_integrates` (
  `id` varchar(36) NOT NULL,
  `account_id` varchar(36) NOT NULL,
  `provider` varchar(16) NOT NULL,
  `open_id` varchar(255) NOT NULL,
  `encrypted_token` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_account_provider` (`account_id`,`provider`),
  UNIQUE KEY `unique_provider_open_id` (`provider`,`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 account_plugin_permissions
# ------------------------------------------------------------

DROP TABLE IF EXISTS `account_plugin_permissions`;

CREATE TABLE `account_plugin_permissions` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `install_permission` varchar(16) NOT NULL DEFAULT 'everyone',
  `debug_permission` varchar(16) NOT NULL DEFAULT 'noone',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_tenant_plugin` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 accounts
# ------------------------------------------------------------

DROP TABLE IF EXISTS `accounts`;

CREATE TABLE `accounts` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) DEFAULT NULL,
  `password_salt` varchar(255) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `interface_language` varchar(255) DEFAULT NULL,
  `interface_theme` varchar(255) DEFAULT NULL,
  `timezone` varchar(255) DEFAULT NULL,
  `last_login_at` datetime DEFAULT NULL,
  `last_login_ip` varchar(255) DEFAULT NULL,
  `status` varchar(16) NOT NULL DEFAULT 'active',
  `initialized_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_active_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `account_email_idx` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 alembic_version
# ------------------------------------------------------------

DROP TABLE IF EXISTS `alembic_version`;

CREATE TABLE `alembic_version` (
  `version_num` varchar(32) NOT NULL,
  PRIMARY KEY (`version_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


# 转储表 api_based_extensions
# ------------------------------------------------------------

DROP TABLE IF EXISTS `api_based_extensions`;

CREATE TABLE `api_based_extensions` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `api_endpoint` varchar(255) NOT NULL,
  `api_key` text NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `api_based_extension_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 api_requests
# ------------------------------------------------------------

DROP TABLE IF EXISTS `api_requests`;

CREATE TABLE `api_requests` (
  `id` varchar(36) NOT NULL,
  `partition_key` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '日期 分区键 例如********',
  `tenant_id` varchar(36) NOT NULL,
  `api_token_id` varchar(36) DEFAULT NULL,
  `path` varchar(255) NOT NULL,
  `request_id` varchar(512) NOT NULL,
  `biz_name` varchar(255) NOT NULL,
  `asyn` varchar(4) NOT NULL DEFAULT '0',
  `run_id` varchar(36) DEFAULT NULL,
  `task_name` varchar(255) DEFAULT NULL,
  `status` varchar(16) NOT NULL DEFAULT 'finished',
  `error_code` varchar(32) DEFAULT '',
  `request` longtext,
  `response` longtext,
  `ip` varchar(255) NOT NULL,
  `async_task_start_time` datetime DEFAULT NULL,
  `async_task_finished_time` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`, `partition_key`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_run_id` (`run_id`),
  KEY `idx_task_name_status` (`task_name`,`status`,`error_code`)
) CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
PARTITION BY RANGE (`partition_key`)
(PARTITION p202506 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202507 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202508 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202509 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202510 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202511 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202512 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202601 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202602 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202603 VALUES LESS THAN (20260401) ENGINE = InnoDB,
PARTITION p202604 VALUES LESS THAN (20260501) ENGINE = InnoDB,
PARTITION p202605 VALUES LESS THAN (20260601) ENGINE = InnoDB,
PARTITION p202606 VALUES LESS THAN (20260701) ENGINE = InnoDB,
PARTITION p202607 VALUES LESS THAN (20260801) ENGINE = InnoDB,
PARTITION p202608 VALUES LESS THAN (20260901) ENGINE = InnoDB,
PARTITION p202609 VALUES LESS THAN (20261001) ENGINE = InnoDB,
PARTITION p202610 VALUES LESS THAN (20261101) ENGINE = InnoDB,
PARTITION p202611 VALUES LESS THAN (20261201) ENGINE = InnoDB,
PARTITION p202612 VALUES LESS THAN (20270101) ENGINE = InnoDB,
PARTITION p202701 VALUES LESS THAN (20270201) ENGINE = InnoDB,
PARTITION p202702 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202703 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202704 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202705 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION pmax VALUES LESS THAN MAXVALUE ENGINE = InnoDB);



# 转储表 api_tokens
# ------------------------------------------------------------

DROP TABLE IF EXISTS `api_tokens`;

CREATE TABLE `api_tokens` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) DEFAULT NULL,
  `type` varchar(16) NOT NULL,
  `token` varchar(255) NOT NULL,
  `last_used_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` varchar(36) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `api_token_app_id_type_idx` (`app_id`,`type`),
  KEY `api_token_token_idx` (`token`,`type`),
  KEY `api_token_tenant_idx` (`tenant_id`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 app_annotation_hit_histories
# ------------------------------------------------------------

DROP TABLE IF EXISTS `app_annotation_hit_histories`;

CREATE TABLE `app_annotation_hit_histories` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `annotation_id` varchar(36) NOT NULL,
  `source` text NOT NULL,
  `question` text NOT NULL,
  `account_id` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `score` float NOT NULL DEFAULT '0',
  `message_id` varchar(36) NOT NULL,
  `annotation_question` text NOT NULL,
  `annotation_content` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `app_annotation_hit_histories_account_idx` (`account_id`),
  KEY `app_annotation_hit_histories_annotation_idx` (`annotation_id`),
  KEY `app_annotation_hit_histories_app_idx` (`app_id`),
  KEY `app_annotation_hit_histories_message_idx` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 app_annotation_settings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `app_annotation_settings`;

CREATE TABLE `app_annotation_settings` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `score_threshold` float NOT NULL DEFAULT '0',
  `collection_binding_id` varchar(36) NOT NULL,
  `created_user_id` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` varchar(36) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `app_annotation_settings_app_idx` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 app_dataset_joins
# ------------------------------------------------------------

DROP TABLE IF EXISTS `app_dataset_joins`;

CREATE TABLE `app_dataset_joins` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `dataset_id` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `app_dataset_join_app_dataset_idx` (`dataset_id`,`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 app_model_configs
# ------------------------------------------------------------

DROP TABLE IF EXISTS `app_model_configs`;

CREATE TABLE `app_model_configs` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `provider` varchar(255) DEFAULT NULL,
  `model_id` varchar(255) DEFAULT NULL,
  `configs` json DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `opening_statement` text,
  `suggested_questions` text,
  `suggested_questions_after_answer` text,
  `more_like_this` text,
  `model` text,
  `user_input_form` text,
  `pre_prompt` text,
  `agent_mode` text,
  `speech_to_text` text,
  `sensitive_word_avoidance` text,
  `retriever_resource` text,
  `dataset_query_variable` varchar(255) DEFAULT NULL,
  `prompt_type` varchar(255) NOT NULL DEFAULT 'simple',
  `chat_prompt_config` text,
  `completion_prompt_config` text,
  `dataset_configs` text,
  `external_data_tools` text,
  `file_upload` text,
  `text_to_speech` text,
  `created_by` char(36) DEFAULT NULL,
  `updated_by` char(36) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `app_app_id_idx` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 apps
# ------------------------------------------------------------

DROP TABLE IF EXISTS `apps`;

CREATE TABLE `apps` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `task_name` varchar(255) NOT NULL,
  `mode` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `icon_background` varchar(255) DEFAULT NULL,
  `app_model_config_id` varchar(36) DEFAULT NULL,
  `status` varchar(255) NOT NULL DEFAULT 'normal',
  `enable_site` tinyint(1) NOT NULL,
  `enable_api` tinyint(1) NOT NULL,
  `api_rpm` int NOT NULL DEFAULT '0',
  `api_rph` int NOT NULL DEFAULT '0',
  `is_demo` tinyint(1) NOT NULL DEFAULT '0',
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_universal` tinyint(1) NOT NULL DEFAULT '0',
  `workflow_id` varchar(36) DEFAULT NULL,
  `description` text NOT NULL,
  `tracing` text,
  `max_active_requests` int DEFAULT NULL,
  `icon_type` varchar(255) DEFAULT NULL,
  `created_by` char(36) DEFAULT NULL,
  `updated_by` char(36) DEFAULT NULL,
  `use_icon_as_answer_icon` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `app_task_name` (`task_name`),
  KEY `app_tenant_id_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 celery_taskmeta
# ------------------------------------------------------------

DROP TABLE IF EXISTS `celery_taskmeta`;

CREATE TABLE `celery_taskmeta` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` varchar(155) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `result` blob,
  `date_done` datetime DEFAULT NULL,
  `traceback` text,
  `name` varchar(155) DEFAULT NULL,
  `args` blob,
  `kwargs` blob,
  `worker` varchar(155) DEFAULT NULL,
  `retries` int DEFAULT NULL,
  `queue` varchar(155) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `celery_taskmeta_task_id_key` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 celery_tasksetmeta
# ------------------------------------------------------------

DROP TABLE IF EXISTS `celery_tasksetmeta`;

CREATE TABLE `celery_tasksetmeta` (
  `id` int NOT NULL AUTO_INCREMENT,
  `taskset_id` varchar(155) DEFAULT NULL,
  `result` blob,
  `date_done` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `celery_tasksetmeta_taskset_id_key` (`taskset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 child_chunks
# ------------------------------------------------------------

DROP TABLE IF EXISTS `child_chunks`;

CREATE TABLE `child_chunks` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `dataset_id` char(36) NOT NULL,
  `document_id` char(36) NOT NULL,
  `segment_id` char(36) NOT NULL,
  `position` int NOT NULL,
  `content` text NOT NULL,
  `word_count` int NOT NULL,
  `index_node_id` varchar(255) DEFAULT NULL,
  `index_node_hash` varchar(255) DEFAULT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'automatic',
  `created_by` char(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` char(36) DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `indexing_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `error` text,
  PRIMARY KEY (`id`),
  KEY `child_chunk_dataset_id_idx` (`tenant_id`,`dataset_id`,`document_id`,`segment_id`,`index_node_id`),
  KEY `child_chunks_node_idx` (`index_node_id`,`dataset_id`),
  KEY `child_chunks_segment_idx` (`segment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 conversations
# ------------------------------------------------------------

DROP TABLE IF EXISTS `conversations`;

CREATE TABLE `conversations` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `app_model_config_id` varchar(36) DEFAULT NULL,
  `model_provider` varchar(255) DEFAULT NULL,
  `override_model_configs` text,
  `model_id` varchar(255) DEFAULT NULL,
  `mode` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `summary` text,
  `inputs` json NOT NULL,
  `introduction` text,
  `system_instruction` text,
  `system_instruction_tokens` int NOT NULL DEFAULT '0',
  `status` varchar(255) NOT NULL,
  `from_source` varchar(255) NOT NULL,
  `from_end_user_id` varchar(36) DEFAULT NULL,
  `from_account_id` varchar(36) DEFAULT NULL,
  `read_at` datetime DEFAULT NULL,
  `read_account_id` varchar(36) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  `invoke_from` varchar(255) DEFAULT NULL,
  `dialogue_count` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `conversation_app_from_user_idx` (`app_id`,`from_source`,`from_end_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 data_source_api_key_auth_bindings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `data_source_api_key_auth_bindings`;

CREATE TABLE `data_source_api_key_auth_bindings` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `category` varchar(255) NOT NULL,
  `provider` varchar(255) NOT NULL,
  `credentials` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `disabled` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `data_source_api_key_auth_binding_provider_idx` (`provider`),
  KEY `data_source_api_key_auth_binding_tenant_id_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 data_source_oauth_bindings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `data_source_oauth_bindings`;

CREATE TABLE `data_source_oauth_bindings` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `access_token` varchar(255) NOT NULL,
  `provider` varchar(255) NOT NULL,
  `source_info` json NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `disabled` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `source_binding_tenant_id_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dataset_auto_disable_logs
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dataset_auto_disable_logs`;

CREATE TABLE `dataset_auto_disable_logs` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `dataset_id` char(36) NOT NULL,
  `document_id` char(36) NOT NULL,
  `notified` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `dataset_auto_disable_log_created_atx` (`created_at`),
  KEY `dataset_auto_disable_log_dataset_idx` (`dataset_id`),
  KEY `dataset_auto_disable_log_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dataset_collection_bindings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dataset_collection_bindings`;

CREATE TABLE `dataset_collection_bindings` (
  `id` varchar(36) NOT NULL,
  `provider_name` varchar(255) NOT NULL,
  `model_name` varchar(255) NOT NULL,
  `collection_name` varchar(64) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `type` varchar(40) NOT NULL DEFAULT 'dataset',
  PRIMARY KEY (`id`),
  KEY `provider_model_name_idx` (`provider_name`,`model_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dataset_keyword_tables
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dataset_keyword_tables`;

CREATE TABLE `dataset_keyword_tables` (
  `id` varchar(36) NOT NULL,
  `dataset_id` varchar(36) NOT NULL,
  `keyword_table` text NOT NULL,
  `data_source_type` varchar(255) NOT NULL DEFAULT 'database',
  PRIMARY KEY (`id`),
  UNIQUE KEY `dataset_keyword_tables_dataset_id_key` (`dataset_id`),
  KEY `dataset_keyword_table_dataset_id_idx` (`dataset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dataset_metadata_bindings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dataset_metadata_bindings`;

CREATE TABLE `dataset_metadata_bindings` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `dataset_id` char(36) NOT NULL,
  `metadata_id` char(36) NOT NULL,
  `document_id` char(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` char(36) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `dataset_metadata_binding_dataset_idx` (`dataset_id`),
  KEY `dataset_metadata_binding_document_idx` (`document_id`),
  KEY `dataset_metadata_binding_metadata_idx` (`metadata_id`),
  KEY `dataset_metadata_binding_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dataset_metadatas
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dataset_metadatas`;

CREATE TABLE `dataset_metadatas` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `dataset_id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` char(36) NOT NULL,
  `updated_by` char(36) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dataset_metadata_dataset_idx` (`dataset_id`),
  KEY `dataset_metadata_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dataset_permissions
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dataset_permissions`;

CREATE TABLE `dataset_permissions` (
  `id` char(36) NOT NULL,
  `dataset_id` char(36) NOT NULL,
  `account_id` char(36) NOT NULL,
  `has_permission` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` varchar(36) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_dataset_permissions_account_id` (`account_id`),
  KEY `idx_dataset_permissions_dataset_id` (`dataset_id`),
  KEY `idx_dataset_permissions_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dataset_process_rules
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dataset_process_rules`;

CREATE TABLE `dataset_process_rules` (
  `id` varchar(36) NOT NULL,
  `dataset_id` varchar(36) NOT NULL,
  `mode` varchar(255) NOT NULL DEFAULT 'automatic',
  `rules` text,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `dataset_process_rule_dataset_id_idx` (`dataset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dataset_queries
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dataset_queries`;

CREATE TABLE `dataset_queries` (
  `id` varchar(36) NOT NULL,
  `dataset_id` varchar(36) NOT NULL,
  `content` text NOT NULL,
  `source` varchar(255) NOT NULL,
  `source_app_id` varchar(36) DEFAULT NULL,
  `created_by_role` varchar(255) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `dataset_query_dataset_id_idx` (`dataset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dataset_retriever_resources
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dataset_retriever_resources`;

CREATE TABLE `dataset_retriever_resources` (
  `id` varchar(36) NOT NULL,
  `message_id` varchar(36) NOT NULL,
  `position` int NOT NULL,
  `dataset_id` varchar(36) NOT NULL,
  `dataset_name` text NOT NULL,
  `document_id` varchar(36) DEFAULT NULL,
  `document_name` text NOT NULL,
  `data_source_type` text,
  `segment_id` varchar(36) DEFAULT NULL,
  `score` float DEFAULT NULL,
  `content` text NOT NULL,
  `hit_count` int DEFAULT NULL,
  `word_count` int DEFAULT NULL,
  `segment_position` int DEFAULT NULL,
  `index_node_hash` text,
  `retriever_from` text NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `dataset_retriever_resource_message_id_idx` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 datasets
# ------------------------------------------------------------

DROP TABLE IF EXISTS `datasets`;

CREATE TABLE `datasets` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `provider` varchar(255) NOT NULL DEFAULT 'vendor',
  `permission` varchar(255) NOT NULL DEFAULT 'only_me',
  `data_source_type` varchar(255) DEFAULT NULL,
  `indexing_technique` varchar(255) DEFAULT NULL,
  `index_struct` text,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` varchar(36) DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `embedding_model` varchar(255) DEFAULT 'text-embedding-ada-002',
  `embedding_model_provider` varchar(255) DEFAULT 'openai',
  `collection_binding_id` varchar(36) DEFAULT NULL,
  `retrieval_model` json DEFAULT NULL,
  `built_in_field_enabled` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `dataset_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 dify_setups
# ------------------------------------------------------------

DROP TABLE IF EXISTS `dify_setups`;

CREATE TABLE `dify_setups` (
  `version` varchar(255) NOT NULL,
  `setup_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 document_segments
# ------------------------------------------------------------

DROP TABLE IF EXISTS `document_segments`;

CREATE TABLE `document_segments` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `dataset_id` varchar(36) NOT NULL,
  `document_id` varchar(36) NOT NULL,
  `position` int NOT NULL,
  `content` text NOT NULL,
  `word_count` int NOT NULL,
  `tokens` int NOT NULL,
  `keywords` json DEFAULT NULL,
  `index_node_id` varchar(255) DEFAULT NULL,
  `index_node_hash` varchar(255) DEFAULT NULL,
  `hit_count` int NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `disabled_at` datetime DEFAULT NULL,
  `disabled_by` varchar(36) DEFAULT NULL,
  `status` varchar(255) NOT NULL DEFAULT 'waiting',
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `indexing_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `error` text,
  `stopped_at` datetime DEFAULT NULL,
  `answer` text,
  `updated_by` varchar(36) DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `document_segment_dataset_id_idx` (`dataset_id`),
  KEY `document_segment_document_id_idx` (`document_id`),
  KEY `document_segment_tenant_dataset_idx` (`dataset_id`,`tenant_id`),
  KEY `document_segment_tenant_document_idx` (`document_id`,`tenant_id`),
  KEY `document_segment_tenant_idx` (`tenant_id`),
  KEY `document_segment_node_dataset_idx` (`index_node_id`,`dataset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 documents
# ------------------------------------------------------------

DROP TABLE IF EXISTS `documents`;

CREATE TABLE `documents` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `dataset_id` varchar(36) NOT NULL,
  `position` int NOT NULL,
  `data_source_type` varchar(255) NOT NULL,
  `data_source_info` text,
  `dataset_process_rule_id` varchar(36) DEFAULT NULL,
  `batch` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_from` varchar(255) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_api_request_id` varchar(36) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `processing_started_at` datetime DEFAULT NULL,
  `file_id` text,
  `word_count` int DEFAULT NULL,
  `parsing_completed_at` datetime DEFAULT NULL,
  `cleaning_completed_at` datetime DEFAULT NULL,
  `splitting_completed_at` datetime DEFAULT NULL,
  `tokens` int DEFAULT NULL,
  `indexing_latency` float DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `is_paused` tinyint(1) DEFAULT '0',
  `paused_by` varchar(36) DEFAULT NULL,
  `paused_at` datetime DEFAULT NULL,
  `error` text,
  `stopped_at` datetime DEFAULT NULL,
  `indexing_status` varchar(255) NOT NULL DEFAULT 'waiting',
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `disabled_at` datetime DEFAULT NULL,
  `disabled_by` varchar(36) DEFAULT NULL,
  `archived` tinyint(1) NOT NULL DEFAULT '0',
  `archived_reason` varchar(255) DEFAULT NULL,
  `archived_by` varchar(36) DEFAULT NULL,
  `archived_at` datetime DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `doc_type` varchar(40) DEFAULT NULL,
  `doc_metadata` json DEFAULT NULL,
  `doc_form` varchar(255) NOT NULL DEFAULT 'text_model',
  `doc_language` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `document_dataset_id_idx` (`dataset_id`),
  KEY `document_is_paused_idx` (`is_paused`),
  KEY `document_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 embeddings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `embeddings`;

CREATE TABLE `embeddings` (
  `id` varchar(36) NOT NULL,
  `hash` varchar(64) NOT NULL,
  `embedding` blob NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `model_name` varchar(255) NOT NULL DEFAULT 'text-embedding-ada-002',
  `provider_name` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `embedding_hash_idx` (`model_name`,`hash`,`provider_name`),
  KEY `created_at_idx` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 end_users
# ------------------------------------------------------------

DROP TABLE IF EXISTS `end_users`;

CREATE TABLE `end_users` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `app_id` varchar(36) DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `external_user_id` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `is_anonymous` tinyint(1) NOT NULL DEFAULT '1',
  `session_id` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `end_user_session_id_idx` (`session_id`,`type`),
  KEY `end_user_tenant_session_id_idx` (`tenant_id`,`session_id`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 external_knowledge_apis
# ------------------------------------------------------------

DROP TABLE IF EXISTS `external_knowledge_apis`;

CREATE TABLE `external_knowledge_apis` (
  `id` char(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `settings` text,
  `created_by` char(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` char(36) DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `external_knowledge_apis_name_idx` (`name`),
  KEY `external_knowledge_apis_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 external_knowledge_bindings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `external_knowledge_bindings`;

CREATE TABLE `external_knowledge_bindings` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `external_knowledge_api_id` char(36) NOT NULL,
  `dataset_id` char(36) NOT NULL,
  `external_knowledge_id` text NOT NULL,
  `created_by` char(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` char(36) DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `external_knowledge_bindings_dataset_idx` (`dataset_id`),
  KEY `external_knowledge_bindings_external_knowledge_api_idx` (`external_knowledge_api_id`),
  KEY `external_knowledge_bindings_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 installed_apps
# ------------------------------------------------------------

DROP TABLE IF EXISTS `installed_apps`;

CREATE TABLE `installed_apps` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `app_owner_tenant_id` varchar(36) NOT NULL,
  `position` int NOT NULL,
  `is_pinned` tinyint(1) NOT NULL DEFAULT '0',
  `last_used_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_tenant_app` (`tenant_id`,`app_id`),
  KEY `installed_app_app_id_idx` (`app_id`),
  KEY `installed_app_tenant_id_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 invitation_codes
# ------------------------------------------------------------

DROP TABLE IF EXISTS `invitation_codes`;

CREATE TABLE `invitation_codes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch` varchar(255) NOT NULL,
  `code` varchar(32) NOT NULL,
  `status` varchar(16) NOT NULL DEFAULT 'unused',
  `used_at` datetime DEFAULT NULL,
  `used_by_tenant_id` varchar(36) DEFAULT NULL,
  `used_by_account_id` varchar(36) DEFAULT NULL,
  `deprecated_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `invitation_codes_batch_idx` (`batch`),
  KEY `invitation_codes_code_idx` (`code`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 load_balancing_model_configs
# ------------------------------------------------------------

DROP TABLE IF EXISTS `load_balancing_model_configs`;

CREATE TABLE `load_balancing_model_configs` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `provider_name` varchar(255) NOT NULL,
  `model_name` varchar(255) NOT NULL,
  `model_type` varchar(40) NOT NULL,
  `name` varchar(255) NOT NULL,
  `encrypted_config` text,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `load_balancing_model_config_tenant_provider_model_idx` (`tenant_id`,`provider_name`,`model_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 message_agent_thoughts
# ------------------------------------------------------------

DROP TABLE IF EXISTS `message_agent_thoughts`;

CREATE TABLE `message_agent_thoughts` (
  `id` varchar(36) NOT NULL,
  `message_id` varchar(36) NOT NULL,
  `message_chain_id` varchar(36) DEFAULT NULL,
  `position` int NOT NULL,
  `thought` text,
  `tool` text,
  `tool_input` text,
  `observation` text,
  `tool_process_data` text,
  `message` text,
  `message_token` int DEFAULT NULL,
  `message_unit_price` decimal(10,0) DEFAULT NULL,
  `answer` text,
  `answer_token` int DEFAULT NULL,
  `answer_unit_price` decimal(10,0) DEFAULT NULL,
  `tokens` int DEFAULT NULL,
  `total_price` decimal(10,0) DEFAULT NULL,
  `currency` varchar(255) DEFAULT NULL,
  `latency` float DEFAULT NULL,
  `created_by_role` varchar(255) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `message_price_unit` decimal(10,7) NOT NULL DEFAULT (0.001),
  `answer_price_unit` decimal(10,7) NOT NULL DEFAULT (0.001),
  `message_files` text,
  `tool_labels_str` text NOT NULL,
  `tool_meta_str` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `message_agent_thought_message_chain_id_idx` (`message_chain_id`),
  KEY `message_agent_thought_message_id_idx` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 message_annotations
# ------------------------------------------------------------

DROP TABLE IF EXISTS `message_annotations`;

CREATE TABLE `message_annotations` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `conversation_id` varchar(36) DEFAULT NULL,
  `message_id` varchar(36) DEFAULT NULL,
  `content` text NOT NULL,
  `account_id` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `question` text,
  `hit_count` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `message_annotation_app_idx` (`app_id`),
  KEY `message_annotation_conversation_idx` (`conversation_id`),
  KEY `message_annotation_message_idx` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 message_chains
# ------------------------------------------------------------

DROP TABLE IF EXISTS `message_chains`;

CREATE TABLE `message_chains` (
  `id` varchar(36) NOT NULL,
  `message_id` varchar(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `input` text,
  `output` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `message_chain_message_id_idx` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 message_feedbacks
# ------------------------------------------------------------

DROP TABLE IF EXISTS `message_feedbacks`;

CREATE TABLE `message_feedbacks` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `conversation_id` varchar(36) NOT NULL,
  `message_id` varchar(36) NOT NULL,
  `rating` varchar(255) NOT NULL,
  `content` text,
  `from_source` varchar(255) NOT NULL,
  `from_end_user_id` varchar(36) DEFAULT NULL,
  `from_account_id` varchar(36) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `message_feedback_app_idx` (`app_id`),
  KEY `message_feedback_conversation_idx` (`conversation_id`,`from_source`,`rating`),
  KEY `message_feedback_message_idx` (`message_id`,`from_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 message_files
# ------------------------------------------------------------

DROP TABLE IF EXISTS `message_files`;

CREATE TABLE `message_files` (
  `id` varchar(36) NOT NULL,
  `message_id` varchar(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `transfer_method` varchar(255) NOT NULL,
  `url` text,
  `upload_file_id` varchar(36) DEFAULT NULL,
  `created_by_role` varchar(255) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `belongs_to` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `message_file_created_by_idx` (`created_by`),
  KEY `message_file_message_idx` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 messages
# ------------------------------------------------------------

DROP TABLE IF EXISTS `messages`;

CREATE TABLE `messages` (
  `id` varchar(36) NOT NULL,
  `partition_key` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '日期 分区键 例如********',
  `app_id` varchar(36) NOT NULL,
  `model_provider` varchar(255) DEFAULT NULL,
  `model_id` varchar(255) DEFAULT NULL,
  `override_model_configs` text,
  `conversation_id` varchar(36) NOT NULL,
  `inputs` json NOT NULL,
  `query` longtext NOT NULL,
  `message` json NOT NULL,
  `message_tokens` int NOT NULL DEFAULT '0',
  `message_unit_price` decimal(10,4) NOT NULL,
  `answer` longtext NOT NULL,
  `answer_tokens` int NOT NULL DEFAULT '0',
  `answer_unit_price` decimal(10,4) NOT NULL,
  `provider_response_latency` float NOT NULL DEFAULT '0',
  `total_price` decimal(10,7) DEFAULT NULL,
  `currency` varchar(255) NOT NULL,
  `from_source` varchar(255) NOT NULL,
  `from_end_user_id` varchar(36) DEFAULT NULL,
  `from_account_id` varchar(36) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `agent_based` tinyint(1) NOT NULL DEFAULT '0',
  `message_price_unit` decimal(10,7) NOT NULL DEFAULT (0.001),
  `answer_price_unit` decimal(10,7) NOT NULL DEFAULT (0.001),
  `workflow_run_id` varchar(36) DEFAULT NULL,
  `status` varchar(255) NOT NULL DEFAULT 'normal',
  `error` text,
  `message_metadata` text,
  `invoke_from` varchar(255) DEFAULT NULL,
  `parent_message_id` char(36) DEFAULT NULL,
  PRIMARY KEY (`id`, `partition_key`),
  KEY `message_account_idx` (`app_id`,`from_source`,`from_account_id`),
  KEY `message_app_id_idx` (`app_id`,`created_at`),
  KEY `message_conversation_id_idx` (`conversation_id`),
  KEY `message_end_user_idx` (`app_id`,`from_source`,`from_end_user_id`),
  KEY `message_workflow_run_id_idx` (`conversation_id`,`workflow_run_id`),
  KEY `message_created_at_idx` (`created_at`)
) CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
PARTITION BY RANGE (`partition_key`)
(PARTITION p202506 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202507 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202508 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202509 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202510 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202511 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202512 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202601 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202602 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202603 VALUES LESS THAN (20260401) ENGINE = InnoDB,
PARTITION p202604 VALUES LESS THAN (20260501) ENGINE = InnoDB,
PARTITION p202605 VALUES LESS THAN (20260601) ENGINE = InnoDB,
PARTITION p202606 VALUES LESS THAN (20260701) ENGINE = InnoDB,
PARTITION p202607 VALUES LESS THAN (20260801) ENGINE = InnoDB,
PARTITION p202608 VALUES LESS THAN (20260901) ENGINE = InnoDB,
PARTITION p202609 VALUES LESS THAN (20261001) ENGINE = InnoDB,
PARTITION p202610 VALUES LESS THAN (20261101) ENGINE = InnoDB,
PARTITION p202611 VALUES LESS THAN (20261201) ENGINE = InnoDB,
PARTITION p202612 VALUES LESS THAN (20270101) ENGINE = InnoDB,
PARTITION p202701 VALUES LESS THAN (20270201) ENGINE = InnoDB,
PARTITION p202702 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202703 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202704 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202705 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION pmax VALUES LESS THAN MAXVALUE ENGINE = InnoDB);

# 转储表 operation_logs
# ------------------------------------------------------------

DROP TABLE IF EXISTS `operation_logs`;

CREATE TABLE `operation_logs` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `account_id` varchar(36) NOT NULL,
  `action` varchar(255) NOT NULL,
  `content` json DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_ip` varchar(255) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `operation_log_account_action_idx` (`tenant_id`,`account_id`,`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 pinned_conversations
# ------------------------------------------------------------

DROP TABLE IF EXISTS `pinned_conversations`;

CREATE TABLE `pinned_conversations` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `conversation_id` varchar(36) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by_role` varchar(255) NOT NULL DEFAULT 'end_user',
  PRIMARY KEY (`id`),
  KEY `pinned_conversation_conversation_idx` (`app_id`,`conversation_id`,`created_by_role`,`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 provider_model_settings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `provider_model_settings`;

CREATE TABLE `provider_model_settings` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `provider_name` varchar(255) NOT NULL,
  `model_name` varchar(255) NOT NULL,
  `model_type` varchar(40) NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `load_balancing_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `provider_model_setting_tenant_provider_model_idx` (`tenant_id`,`provider_name`,`model_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 provider_models
# ------------------------------------------------------------

DROP TABLE IF EXISTS `provider_models`;

CREATE TABLE `provider_models` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `provider_name` varchar(255) NOT NULL,
  `model_name` varchar(255) NOT NULL,
  `model_type` varchar(40) NOT NULL,
  `encrypted_config` text,
  `is_valid` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_provider_model_name` (`tenant_id`,`provider_name`,`model_name`,`model_type`),
  KEY `provider_model_tenant_id_provider_idx` (`tenant_id`,`provider_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 provider_orders
# ------------------------------------------------------------

DROP TABLE IF EXISTS `provider_orders`;

CREATE TABLE `provider_orders` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `provider_name` varchar(255) NOT NULL,
  `account_id` varchar(36) NOT NULL,
  `payment_product_id` varchar(191) NOT NULL,
  `payment_id` varchar(191) DEFAULT NULL,
  `transaction_id` varchar(191) DEFAULT NULL,
  `quantity` int NOT NULL DEFAULT '1',
  `currency` varchar(40) DEFAULT NULL,
  `total_amount` int DEFAULT NULL,
  `payment_status` varchar(40) NOT NULL DEFAULT 'wait_pay',
  `paid_at` datetime DEFAULT NULL,
  `pay_failed_at` datetime DEFAULT NULL,
  `refunded_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `provider_order_tenant_provider_idx` (`tenant_id`,`provider_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 providers
# ------------------------------------------------------------

DROP TABLE IF EXISTS `providers`;

CREATE TABLE `providers` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `provider_name` varchar(255) NOT NULL,
  `provider_type` varchar(40) NOT NULL DEFAULT 'custom',
  `encrypted_config` text,
  `is_valid` tinyint(1) NOT NULL DEFAULT '0',
  `last_used` datetime DEFAULT NULL,
  `quota_type` varchar(40) DEFAULT '',
  `quota_limit` bigint DEFAULT NULL,
  `quota_used` bigint DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_provider_name_type_quota` (`tenant_id`,`provider_name`,`provider_type`,`quota_type`),
  KEY `provider_tenant_id_provider_idx` (`tenant_id`,`provider_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 rate_limit_logs
# ------------------------------------------------------------

DROP TABLE IF EXISTS `rate_limit_logs`;

CREATE TABLE `rate_limit_logs` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `subscription_plan` varchar(255) NOT NULL,
  `operation` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `rate_limit_log_operation_idx` (`operation`),
  KEY `rate_limit_log_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 recommended_apps
# ------------------------------------------------------------

DROP TABLE IF EXISTS `recommended_apps`;

CREATE TABLE `recommended_apps` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `description` json NOT NULL,
  `copyright` varchar(255) NOT NULL,
  `privacy_policy` varchar(255) NOT NULL,
  `category` varchar(255) NOT NULL,
  `position` int NOT NULL,
  `is_listed` tinyint(1) NOT NULL,
  `install_count` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `language` varchar(255) NOT NULL DEFAULT 'en-US',
  `custom_disclaimer` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `recommended_app_app_id_idx` (`app_id`),
  KEY `recommended_app_is_listed_idx` (`is_listed`,`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 saved_messages
# ------------------------------------------------------------

DROP TABLE IF EXISTS `saved_messages`;

CREATE TABLE `saved_messages` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `message_id` varchar(36) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by_role` varchar(255) NOT NULL DEFAULT 'end_user',
  PRIMARY KEY (`id`),
  KEY `saved_message_message_idx` (`app_id`,`message_id`,`created_by_role`,`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 sites
# ------------------------------------------------------------

DROP TABLE IF EXISTS `sites`;

CREATE TABLE `sites` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `icon_background` varchar(255) DEFAULT NULL,
  `description` text,
  `default_language` varchar(255) NOT NULL,
  `copyright` varchar(255) DEFAULT NULL,
  `privacy_policy` varchar(255) DEFAULT NULL,
  `customize_domain` varchar(255) DEFAULT NULL,
  `customize_token_strategy` varchar(255) NOT NULL,
  `prompt_public` tinyint(1) NOT NULL DEFAULT '0',
  `status` varchar(255) NOT NULL DEFAULT 'normal',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `code` varchar(255) DEFAULT NULL,
  `custom_disclaimer` text NOT NULL,
  `show_workflow_steps` tinyint(1) NOT NULL DEFAULT '1',
  `chat_color_theme` varchar(255) DEFAULT NULL,
  `chat_color_theme_inverted` tinyint(1) NOT NULL DEFAULT '0',
  `icon_type` varchar(255) DEFAULT NULL,
  `created_by` char(36) DEFAULT NULL,
  `updated_by` char(36) DEFAULT NULL,
  `use_icon_as_answer_icon` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `site_app_id_idx` (`app_id`),
  KEY `site_code_idx` (`code`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tag_bindings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tag_bindings`;

CREATE TABLE `tag_bindings` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) DEFAULT NULL,
  `tag_id` varchar(36) DEFAULT NULL,
  `target_id` varchar(36) DEFAULT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tag_bind_tag_id_idx` (`tag_id`),
  KEY `tag_bind_target_id_idx` (`target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tags
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tags`;

CREATE TABLE `tags` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) DEFAULT NULL,
  `type` varchar(16) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tag_name_idx` (`name`),
  KEY `tag_type_idx` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tenant_account_joins
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tenant_account_joins`;

CREATE TABLE `tenant_account_joins` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `account_id` varchar(36) NOT NULL,
  `role` varchar(16) NOT NULL DEFAULT 'normal',
  `invited_by` varchar(36) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `current` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_tenant_account_join` (`tenant_id`,`account_id`),
  KEY `tenant_account_join_account_id_idx` (`account_id`),
  KEY `tenant_account_join_tenant_id_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tenant_default_models
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tenant_default_models`;

CREATE TABLE `tenant_default_models` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `provider_name` varchar(255) NOT NULL,
  `model_name` varchar(255) NOT NULL,
  `model_type` varchar(40) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tenant_default_model_tenant_id_provider_type_idx` (`tenant_id`,`provider_name`,`model_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tenant_preferred_model_providers
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tenant_preferred_model_providers`;

CREATE TABLE `tenant_preferred_model_providers` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `provider_name` varchar(255) NOT NULL,
  `preferred_provider_type` varchar(40) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tenant_preferred_model_provider_tenant_provider_idx` (`tenant_id`,`provider_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tenants
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tenants`;

CREATE TABLE `tenants` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `encrypt_public_key` text,
  `plan` varchar(255) NOT NULL DEFAULT 'basic',
  `status` varchar(255) NOT NULL DEFAULT 'normal',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `custom_config` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tidb_auth_bindings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tidb_auth_bindings`;

CREATE TABLE `tidb_auth_bindings` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) DEFAULT NULL,
  `cluster_id` varchar(255) NOT NULL,
  `cluster_name` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '0',
  `status` varchar(255) NOT NULL DEFAULT 'CREATING',
  `account` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tidb_auth_bindings_active_idx` (`active`),
  KEY `tidb_auth_bindings_status_idx` (`status`),
  KEY `tidb_auth_bindings_created_at_idx` (`created_at`),
  KEY `tidb_auth_bindings_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tool_api_providers
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tool_api_providers`;

CREATE TABLE `tool_api_providers` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `schema` text NOT NULL,
  `schema_type_str` varchar(40) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `tools_str` text NOT NULL,
  `icon` varchar(255) NOT NULL,
  `credentials_str` text NOT NULL,
  `description` text NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `privacy_policy` varchar(255) DEFAULT NULL,
  `custom_disclaimer` text NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_api_tool_provider` (`name`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tool_builtin_providers
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tool_builtin_providers`;

CREATE TABLE `tool_builtin_providers` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) DEFAULT NULL,
  `user_id` varchar(36) NOT NULL,
  `provider` varchar(256) NOT NULL,
  `encrypted_credentials` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_builtin_tool_provider` (`tenant_id`,`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tool_conversation_variables
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tool_conversation_variables`;

CREATE TABLE `tool_conversation_variables` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `conversation_id` varchar(36) NOT NULL,
  `variables_str` text NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `conversation_id_idx` (`conversation_id`),
  KEY `user_id_idx` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tool_files
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tool_files`;

CREATE TABLE `tool_files` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `conversation_id` varchar(36) DEFAULT NULL,
  `file_key` varchar(255) NOT NULL,
  `mimetype` varchar(255) NOT NULL,
  `original_url` varchar(2048) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `size` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tool_file_conversation_id_idx` (`conversation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tool_label_bindings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tool_label_bindings`;

CREATE TABLE `tool_label_bindings` (
  `id` char(36) NOT NULL,
  `tool_id` varchar(64) NOT NULL,
  `tool_type` varchar(40) NOT NULL,
  `label_name` varchar(40) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_tool_label_bind` (`tool_id`,`label_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tool_model_invokes
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tool_model_invokes`;

CREATE TABLE `tool_model_invokes` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `provider` varchar(255) NOT NULL,
  `tool_type` varchar(40) NOT NULL,
  `tool_name` varchar(40) NOT NULL,
  `model_parameters` text NOT NULL,
  `prompt_messages` text NOT NULL,
  `model_response` text NOT NULL,
  `prompt_tokens` int NOT NULL DEFAULT '0',
  `answer_tokens` int NOT NULL DEFAULT '0',
  `answer_unit_price` decimal(10,4) NOT NULL,
  `answer_price_unit` decimal(10,7) NOT NULL DEFAULT (0.001),
  `provider_response_latency` float NOT NULL DEFAULT '0',
  `total_price` decimal(10,7) DEFAULT NULL,
  `currency` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tool_published_apps
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tool_published_apps`;

CREATE TABLE `tool_published_apps` (
  `id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `description` text NOT NULL,
  `llm_description` text NOT NULL,
  `query_description` text NOT NULL,
  `query_name` varchar(40) NOT NULL,
  `tool_name` varchar(40) NOT NULL,
  `author` varchar(40) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_published_app_tool` (`app_id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 tool_workflow_providers
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tool_workflow_providers`;

CREATE TABLE `tool_workflow_providers` (
  `id` char(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) NOT NULL,
  `app_id` char(36) NOT NULL,
  `user_id` char(36) NOT NULL,
  `tenant_id` char(36) NOT NULL,
  `description` text NOT NULL,
  `parameter_configuration` text NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `privacy_policy` varchar(255) DEFAULT '',
  `version` varchar(255) NOT NULL DEFAULT '',
  `label` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_workflow_tool_provider` (`name`,`tenant_id`),
  UNIQUE KEY `unique_workflow_tool_provider_app_id` (`tenant_id`,`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 trace_app_config
# ------------------------------------------------------------

DROP TABLE IF EXISTS `trace_app_config`;

CREATE TABLE `trace_app_config` (
  `id` char(36) NOT NULL,
  `app_id` char(36) NOT NULL,
  `tracing_provider` varchar(255) DEFAULT NULL,
  `tracing_config` json DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `trace_app_config_app_id_idx` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 upload_files
# ------------------------------------------------------------

DROP TABLE IF EXISTS `upload_files`;

CREATE TABLE `upload_files` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `storage_type` varchar(255) NOT NULL,
  `key` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `size` int NOT NULL,
  `extension` varchar(255) NOT NULL,
  `mime_type` varchar(255) DEFAULT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `used` tinyint(1) NOT NULL DEFAULT '0',
  `used_by` varchar(36) DEFAULT NULL,
  `used_at` datetime DEFAULT NULL,
  `hash` varchar(255) DEFAULT NULL,
  `created_by_role` varchar(255) NOT NULL DEFAULT 'account',
  `source_url` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `upload_file_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 whitelists
# ------------------------------------------------------------

DROP TABLE IF EXISTS `whitelists`;

CREATE TABLE `whitelists` (
  `id` char(36) NOT NULL,
  `tenant_id` char(36) DEFAULT NULL,
  `category` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `whitelists_tenant_idx` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 workflow_app_logs
# ------------------------------------------------------------

DROP TABLE IF EXISTS `workflow_app_logs`;

CREATE TABLE `workflow_app_logs` (
  `id` varchar(36) NOT NULL,
  `partition_key` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '日期 分区键 例如********',
  `tenant_id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `workflow_id` varchar(36) NOT NULL,
  `workflow_run_id` varchar(36) NOT NULL,
  `created_from` varchar(255) NOT NULL,
  `created_by_role` varchar(255) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`, `partition_key`),
  KEY `workflow_app_log_app_idx` (`tenant_id`,`app_id`)
) CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
PARTITION BY RANGE (`partition_key`)
(PARTITION p202506 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202507 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202508 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202509 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202510 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202511 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202512 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202601 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202602 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202603 VALUES LESS THAN (20260401) ENGINE = InnoDB,
PARTITION p202604 VALUES LESS THAN (20260501) ENGINE = InnoDB,
PARTITION p202605 VALUES LESS THAN (20260601) ENGINE = InnoDB,
PARTITION p202606 VALUES LESS THAN (20260701) ENGINE = InnoDB,
PARTITION p202607 VALUES LESS THAN (20260801) ENGINE = InnoDB,
PARTITION p202608 VALUES LESS THAN (20260901) ENGINE = InnoDB,
PARTITION p202609 VALUES LESS THAN (20261001) ENGINE = InnoDB,
PARTITION p202610 VALUES LESS THAN (20261101) ENGINE = InnoDB,
PARTITION p202611 VALUES LESS THAN (20261201) ENGINE = InnoDB,
PARTITION p202612 VALUES LESS THAN (20270101) ENGINE = InnoDB,
PARTITION p202701 VALUES LESS THAN (20270201) ENGINE = InnoDB,
PARTITION p202702 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202703 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202704 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202705 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION pmax VALUES LESS THAN MAXVALUE ENGINE = InnoDB);


# 转储表 workflow_conversation_variables
# ------------------------------------------------------------

DROP TABLE IF EXISTS `workflow_conversation_variables`;

CREATE TABLE `workflow_conversation_variables` (
  `id` char(36) NOT NULL,
  `conversation_id` char(36) NOT NULL,
  `app_id` char(36) NOT NULL,
  `data` text NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`,`conversation_id`),
  KEY `workflow_conversation_variables_app_id_idx` (`app_id`),
  KEY `workflow_conversation_variables_created_at_idx` (`created_at`),
  KEY `workflow_conversation_variables_conversation_id_idx` (`conversation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



# 转储表 workflow_node_executions
# ------------------------------------------------------------

DROP TABLE IF EXISTS `workflow_node_executions`;

CREATE TABLE `workflow_node_executions` (
  `id` varchar(36) NOT NULL,
  `partition_key` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '日期 分区键 例如********',
  `tenant_id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `workflow_id` varchar(36) NOT NULL,
  `triggered_from` varchar(255) NOT NULL,
  `workflow_run_id` varchar(36) DEFAULT NULL,
  `index` int NOT NULL,
  `predecessor_node_id` varchar(255) DEFAULT NULL,
  `node_id` varchar(255) NOT NULL,
  `node_type` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `inputs` longtext,
  `process_data` longtext,
  `outputs` longtext,
  `status` varchar(255) NOT NULL,
  `error` text,
  `elapsed_time` float NOT NULL DEFAULT '0',
  `execution_metadata` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by_role` varchar(255) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `finished_at` datetime DEFAULT NULL,
  `node_execution_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`, `partition_key`),
  KEY `workflow_node_execution_node_run_idx` (`tenant_id`,`app_id`,`workflow_id`,`triggered_from`,`node_id`),
  KEY `workflow_node_execution_workflow_run_idx` (`tenant_id`,`app_id`,`workflow_id`,`triggered_from`,`workflow_run_id`),
  KEY `workflow_node_execution_id_idx` (`tenant_id`,`app_id`,`workflow_id`,`triggered_from`,`node_execution_id`),
  KEY `workflow_node_execution_run_idx` (`workflow_run_id`)
) CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
PARTITION BY RANGE (`partition_key`)
(PARTITION p202506 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202507 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202508 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202509 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202510 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202511 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202512 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202601 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202602 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202603 VALUES LESS THAN (20260401) ENGINE = InnoDB,
PARTITION p202604 VALUES LESS THAN (20260501) ENGINE = InnoDB,
PARTITION p202605 VALUES LESS THAN (20260601) ENGINE = InnoDB,
PARTITION p202606 VALUES LESS THAN (20260701) ENGINE = InnoDB,
PARTITION p202607 VALUES LESS THAN (20260801) ENGINE = InnoDB,
PARTITION p202608 VALUES LESS THAN (20260901) ENGINE = InnoDB,
PARTITION p202609 VALUES LESS THAN (20261001) ENGINE = InnoDB,
PARTITION p202610 VALUES LESS THAN (20261101) ENGINE = InnoDB,
PARTITION p202611 VALUES LESS THAN (20261201) ENGINE = InnoDB,
PARTITION p202612 VALUES LESS THAN (20270101) ENGINE = InnoDB,
PARTITION p202701 VALUES LESS THAN (20270201) ENGINE = InnoDB,
PARTITION p202702 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202703 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202704 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202705 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION pmax VALUES LESS THAN MAXVALUE ENGINE = InnoDB);


# 转储表 workflow_runs
# ------------------------------------------------------------

DROP TABLE IF EXISTS `workflow_runs`;

CREATE TABLE `workflow_runs` (
  `id` varchar(36) NOT NULL,
  `partition_key` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '日期 分区键 例如********',
  `tenant_id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `sequence_number` int NOT NULL,
  `workflow_id` varchar(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `triggered_from` varchar(255) NOT NULL,
  `version` varchar(255) NOT NULL,
  `graph` longtext,
  `inputs` longtext,
  `status` varchar(255) NOT NULL,
  `outputs` longtext,
  `error` text,
  `elapsed_time` float NOT NULL DEFAULT '0',
  `total_tokens` bigint NOT NULL DEFAULT '0',
  `total_steps` int DEFAULT '0',
  `created_by_role` varchar(255) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `finished_at` datetime DEFAULT NULL,
  `exceptions_count` int DEFAULT '0',
  PRIMARY KEY (`id`, `partition_key`),
  KEY `workflow_run_triggerd_from_idx` (`tenant_id`,`app_id`,`triggered_from`),
  KEY `workflow_run_tenant_app_sequence_idx` (`tenant_id`,`app_id`,`sequence_number`)
) CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
PARTITION BY RANGE (`partition_key`)
(PARTITION p202506 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202507 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202508 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202509 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202510 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202511 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202512 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202601 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202602 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202603 VALUES LESS THAN (20260401) ENGINE = InnoDB,
PARTITION p202604 VALUES LESS THAN (20260501) ENGINE = InnoDB,
PARTITION p202605 VALUES LESS THAN (20260601) ENGINE = InnoDB,
PARTITION p202606 VALUES LESS THAN (20260701) ENGINE = InnoDB,
PARTITION p202607 VALUES LESS THAN (20260801) ENGINE = InnoDB,
PARTITION p202608 VALUES LESS THAN (20260901) ENGINE = InnoDB,
PARTITION p202609 VALUES LESS THAN (20261001) ENGINE = InnoDB,
PARTITION p202610 VALUES LESS THAN (20261101) ENGINE = InnoDB,
PARTITION p202611 VALUES LESS THAN (20261201) ENGINE = InnoDB,
PARTITION p202612 VALUES LESS THAN (20270101) ENGINE = InnoDB,
PARTITION p202701 VALUES LESS THAN (20270201) ENGINE = InnoDB,
PARTITION p202702 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202703 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202704 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION p202705 VALUES LESS THAN (********) ENGINE = InnoDB,
PARTITION pmax VALUES LESS THAN MAXVALUE ENGINE = InnoDB);



# 转储表 workflows
# ------------------------------------------------------------

DROP TABLE IF EXISTS `workflows`;

CREATE TABLE `workflows` (
  `id` varchar(36) NOT NULL,
  `tenant_id` varchar(36) NOT NULL,
  `app_id` varchar(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `version` varchar(255) NOT NULL,
  `graph` longtext NOT NULL,
  `features` text NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` varchar(36) DEFAULT NULL,
  `updated_at` timestamp NOT NULL,
  `environment_variables` text NOT NULL,
  `conversation_variables` text NOT NULL,
  `marked_name` varchar(255) NOT NULL DEFAULT '',
  `marked_comment` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `workflow_version_idx` (`tenant_id`,`app_id`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
