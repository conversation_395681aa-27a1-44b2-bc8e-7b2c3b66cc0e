import logging
import datetime

from flask import Flask
from atta_ai_common.configs.package_config import common_config
from atta_ai_common.utils.service_deployment import is_celery
from atta_ai_prometheus_python import CollectorRegistry, multiprocess
from atta_ai_prometheus_python import generate_latest
from atta_ai_common.utils.service_deployment import LOCAL_DEV_MODE

if not LOCAL_DEV_MODE:
    registry = CollectorRegistry()
    multiprocess.MultiProcessCollector(registry=registry)

def init_app(app: Flask):
    # json provider返回中文
    app.json.ensure_ascii = False
    
    # 健康检查
    @app.route("/", methods=["GET"])
    def sys_health():
        return {"status": "Ok"}
    
    # prometheus指标
    @app.route("/actuator/prometheus", methods=["GET"])
    def prometheus_actuator_metrics():
        now = datetime.datetime.now()
        check_hour, check_minute = common_config.prometheus_log_time.split(":")
        if str(now.hour) == check_hour and str(now.minute) == check_minute:
            log_extra = {
                "trace_id": str(now.timestamp()), 
                "span_id": "prometheus", 
            }
            logging.info("metrics api request", extra=log_extra)
        if LOCAL_DEV_MODE:
            return generate_latest()
        else:
            return generate_latest(registry)
    
    # 防止celery启动时, 重复初始化tracing
    if not is_celery() and common_config.skywalking_enable:
        import traceback
        from atta_ai_common.utils.tracing import ensure_tracing
        try:
            ensure_tracing()
        except Exception as e:
            logging.error(f"初始化skywalking失败, 错误信息: {e} {traceback.format_exc()}")