apiVersion: apps/v1
kind: Deployment
metadata:
  generation: 1
  labels:
    app: atta-ai-oneapi
  name: atta-ai-oneapi
  namespace: sitxtai
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: atta-ai-oneapi
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: atta-ai-oneapi
    spec:
      containers:
      - env:
        - name: SQL_DSN
          value: root:sdjsdjk#1122@tcp(192.168.3.225:3306)/oneapi
        - name: TIKTOKEN_CACHE_DIR
          value: /tiktokencache
        image: justsong/one-api:latest
        imagePullPolicy: Always
        resources:
          limits:
            cpu: "2"
            memory: 4Gi
          requests:
            cpu: 100m
            memory: 4Gi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /data
          name: logs
        - mountPath: /tiktoken_cache
          name: tiktoken-cache
        name: oneapi
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - hostPath:
          path: /logs/sitxtai/atta-ai-oneapi/
          type: DirectoryOrCreate
        name: logs
      - hostPath:
          path: /logs/atta-ai-data/code-model/atta-ai-oneapi/
          type: DirectoryOrCreate
        name: tiktoken-cache

apiVersion: v1
kind: Service
metadata:
  name: atta-ai-oneapi
  namespace: sitxtai
spec:
  ports:
  - name: atta-ai-oneapi
    port: 80
    protocol: TCP
    targetPort: 3000
  selector:
    app: atta-ai-oneapi
  sessionAffinity: None
  type: ClusterIP
