from RestrictedPython import compile_restricted
from RestrictedPython.PrintCollector import PrintCollector
from RestrictedPython import safe_builtins

ALLOWED_MODULES = ['math', 'datetime', 're', '']

def safe_import(name, globals=None, locals=None, fromlist=(), level=0):
    if name in ALLOWED_MODULES:
        return __import__(name, globals, locals, fromlist, level)
    raise ImportError(f"模块 '{name}' 不允许导入")

safe_builtins['__import__'] = safe_import

source_code = """
from sys import stdout
def main():
    r = 1 + 1
    print("计算结果:", r)
    return r
result = main()
"""

restricted_globals = {
    '__builtins__': safe_builtins,
    '_print_': PrintCollector,  # 使用PrintCollector类
    '_write_': lambda x: x,
}

try:
    byte_code = compile_restricted(source_code, '<string>', 'exec')
    exec(byte_code, restricted_globals)
    
    # 获取执行结果
    print("=== 变量结果 ===")
    print("result:", restricted_globals.get('result'))
    
except Exception as e:
    print("执行出错:", str(e))