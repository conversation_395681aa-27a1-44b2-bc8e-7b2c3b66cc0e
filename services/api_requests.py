import json
import time
import logging
from collections.abc import Callable, Generator, Sequence
from datetime import UTC, datetime
from typing import Any, Optional
from uuid import uuid4

from sqlalchemy import select
from sqlalchemy.orm import Session

from core.app.apps.advanced_chat.app_config_manager import AdvancedChatAppConfigManager
from core.app.apps.workflow.app_config_manager import WorkflowAppConfigManager
from core.model_runtime.utils.encoders import jsonable_encoder
from core.repositories import SQLAlchemyWorkflowNodeExecutionRepository
from core.variables import Variable
from core.workflow.entities.node_entities import NodeRunResult
from core.workflow.errors import WorkflowNodeRunFailedError
from core.workflow.graph_engine.entities.event import InNodeEvent
from core.workflow.nodes import NodeType
from core.workflow.nodes.base.node import BaseNode
from core.workflow.nodes.enums import ErrorStrategy
from core.workflow.nodes.event import RunCompletedEvent
from core.workflow.nodes.event.types import NodeEvent
from core.workflow.nodes.node_mapping import LATEST_VERSION, NODE_TYPE_CLASSES_MAPPING
from core.workflow.workflow_entry import WorkflowEntry
from events.app_event import app_draft_workflow_was_synced, app_published_workflow_was_updated
from extensions.ext_database import db
from models.account import Account
from models.enums import CreatedByRole
from models.model import App, AppMode
from models.tools import WorkflowToolProvider
from models.model import (
    ApiRequest
)
from services.errors.app import WorkflowHashNotEqualError
from services.workflow.workflow_converter import WorkflowConverter

from .errors.workflow_service import DraftWorkflowDeletionError, WorkflowInUseError

logger = logging.getLogger(__name__)

class ApiRequestService:
    """
    ApiRequest Service
    """

    def get_api_requests_by_request_id(self, request_id) -> Optional[ApiRequest]:
        """
        通过request_id获取api请求
        """
        api_req = (
            db.session.query(ApiRequest)
            .filter(ApiRequest.request_id == request_id)
            .first()
        )
        return api_req

    def add_api_requests(
        self,
        *,
        tenant_id: str,
        path: str,
        task_name: str,
        request_id: str,
        biz_name: str,
        asyn: str,
        run_id: str,
        request: dict,
        response: dict,
        status: str,
        error_code: str = "",
        ip: str = "",
        api_token_id: str = "",
    ) -> ApiRequest:
        """
        同步接口添加api请求
        """
        try:
            request_text = json.dumps(request, ensure_ascii=False)
            response_text = json.dumps(response, ensure_ascii=False)
            api_req = ApiRequest(
                tenant_id=tenant_id,
                path=path,
                task_name=task_name,
                request_id=request_id,
                biz_name=biz_name,
                asyn=asyn,
                run_id=run_id,
                request=request_text,
                response=response_text,
                status=status,
                error_code=error_code,
                ip=ip,
                api_token_id=api_token_id,
            )
            db.session.add(api_req)
            # update draft workflow if found

            # commit db session changes
            db.session.commit()
            return api_req
        except Exception as e:
            logger.error(f"添加api请求失败: {e}")
        return None
    
    
    def async_task_update_api_requests(
        self,
        *,
        run_id: str,
        response: dict,
        status: str,
        async_task_start_time: datetime,
        error_code: str = ""
    ) -> ApiRequest:
        """
        异步接口更新api请求
        """
        try:
            api_req = (
                db.session.query(ApiRequest)
                .filter(ApiRequest.run_id == run_id)
                .first()
            )
            if not api_req:
                logging.warning(f"异步任务更新api请求失败，未找到api请求 run_id: {run_id}")
                return None 
            api_req.response = json.dumps(response, ensure_ascii=False)
            api_req.status = status
            api_req.error_code = error_code
            api_req.async_task_start_time = async_task_start_time
            api_req.async_task_finished_time = datetime.now()
            db.session.commit()
            return api_req
        except Exception as e:
            logger.warning(f"异步任务更新api请求 {run_id} 失败: {e}")
        return None 
