import time
import logging
import re
import json

from collections.abc import Callable
from datetime import UTC, datetime, timedelta
from enum import Enum
from functools import wraps
from typing import Optional

from click import echo
from flask import current_app, request, g
from flask_login import user_logged_in  # type: ignore
from flask_restful import Resource
from flask import Response, stream_with_context
from pydantic import BaseModel
from sqlalchemy import select, update
from sqlalchemy.orm import Session
from werkzeug.exceptions import Forbidden, Unauthorized, BadRequest

from controllers.service_api.app.error import AppUnavailableError
from extensions.ext_database import db
from extensions.ext_redis import redis_client
from libs.login import _get_user
from models.account import Account, Tenant, TenantAccountJoin, TenantStatus
from models.dataset import RateLimitLog
from models.model import ApiToken, App, EndUser
from services.feature_service import FeatureService
from configs import dify_config
from services.api_requests import ApiRequestService
from urllib.parse import urljoin
from core.base.common.utils import bool_asyn, handler_error
from contexts.svc_ctx import get_svc_ctx
from atta_ai_common.log.common_log.decorator import report_sync_prometheus
from core.base.common.metrics import accept_request_metrics, record_task_run_metrics
from libs import helper
from core.entities.service_api_entities import AsyncResponseEntity


class WhereisUserArg(Enum):
    """
    Enum for whereis_user_arg.
    """

    QUERY = "query"
    JSON = "json"
    FORM = "form"


class FetchUserArg(BaseModel):
    fetch_from: WhereisUserArg
    required: bool = False


def validate_app_token(view: Optional[Callable] = None, *, fetch_user_arg: Optional[FetchUserArg] = None):
    def decorator(view_func):
        @wraps(view_func)
        def decorated_view(*args, **kwargs):
            api_token = validate_and_get_api_token("app")

            app_model = db.session.query(App).filter(App.id == api_token.app_id).first()
            if not app_model:
                raise Forbidden("The app no longer exists.")

            if app_model.status != "normal":
                raise Forbidden("The app's status is abnormal.")

            if not app_model.enable_api:
                raise Forbidden("The app's API service has been disabled.")

            tenant = db.session.query(Tenant).filter(Tenant.id == app_model.tenant_id).first()
            if tenant is None:
                raise ValueError("Tenant does not exist.")
            if tenant.status == TenantStatus.ARCHIVE:
                raise Forbidden("The workspace's status is archived.")

            tenant_account_join = (
                db.session.query(Tenant, TenantAccountJoin)
                .filter(Tenant.id == api_token.tenant_id)
                .filter(TenantAccountJoin.tenant_id == Tenant.id)
                .filter(TenantAccountJoin.role.in_(["owner"]))
                .filter(Tenant.status == TenantStatus.NORMAL)
                .one_or_none()
            )  # TODO: only owner information is required, so only one is returned.
            if tenant_account_join:
                tenant, ta = tenant_account_join
                account = db.session.query(Account).filter(Account.id == ta.account_id).first()
                # Login admin
                if account:
                    account.current_tenant = tenant
                    current_app.login_manager._update_request_context_with_user(account)  # type: ignore
                    user_logged_in.send(current_app._get_current_object(), user=_get_user())  # type: ignore
                else:
                    raise Unauthorized("Tenant owner account does not exist.")
            else:
                raise Unauthorized("Tenant does not exist.")

            kwargs["app_model"] = app_model

            if fetch_user_arg:
                if fetch_user_arg.fetch_from == WhereisUserArg.QUERY:
                    user_id = request.args.get("user")
                elif fetch_user_arg.fetch_from == WhereisUserArg.JSON:
                    user_id = request.get_json().get("user")
                elif fetch_user_arg.fetch_from == WhereisUserArg.FORM:
                    user_id = request.form.get("user")
                else:
                    # use default-user
                    user_id = None

                if not user_id and fetch_user_arg.required:
                    raise ValueError("Arg user must be provided.")

                if user_id:
                    user_id = str(user_id)

                kwargs["end_user"] = create_or_update_end_user_for_user_id(app_model, user_id)

            return view_func(*args, **kwargs)

        return decorated_view

    if view is None:
        return decorator
    else:
        return decorator(view)


def cloud_edition_billing_resource_check(resource: str, api_token_type: str):
    def interceptor(view):
        def decorated(*args, **kwargs):
            api_token = validate_and_get_api_token(api_token_type)
            features = FeatureService.get_features(api_token.tenant_id)

            if features.billing.enabled:
                members = features.members
                apps = features.apps
                vector_space = features.vector_space
                documents_upload_quota = features.documents_upload_quota

                if resource == "members" and 0 < members.limit <= members.size:
                    raise Forbidden("The number of members has reached the limit of your subscription.")
                elif resource == "apps" and 0 < apps.limit <= apps.size:
                    raise Forbidden("The number of apps has reached the limit of your subscription.")
                elif resource == "vector_space" and 0 < vector_space.limit <= vector_space.size:
                    raise Forbidden("The capacity of the vector space has reached the limit of your subscription.")
                elif resource == "documents" and 0 < documents_upload_quota.limit <= documents_upload_quota.size:
                    raise Forbidden("The number of documents has reached the limit of your subscription.")
                else:
                    return view(*args, **kwargs)

            return view(*args, **kwargs)

        return decorated

    return interceptor


def cloud_edition_billing_knowledge_limit_check(resource: str, api_token_type: str):
    def interceptor(view):
        @wraps(view)
        def decorated(*args, **kwargs):
            api_token = validate_and_get_api_token(api_token_type)
            features = FeatureService.get_features(api_token.tenant_id)
            if features.billing.enabled:
                if resource == "add_segment":
                    if features.billing.subscription.plan == "sandbox":
                        raise Forbidden(
                            "To unlock this feature and elevate your Dify experience, please upgrade to a paid plan."
                        )
                else:
                    return view(*args, **kwargs)

            return view(*args, **kwargs)

        return decorated

    return interceptor


def cloud_edition_billing_rate_limit_check(resource: str, api_token_type: str):
    def interceptor(view):
        @wraps(view)
        def decorated(*args, **kwargs):
            api_token = validate_and_get_api_token(api_token_type)

            if resource == "knowledge":
                knowledge_rate_limit = FeatureService.get_knowledge_rate_limit(api_token.tenant_id)
                if knowledge_rate_limit.enabled:
                    current_time = int(time.time() * 1000)
                    key = f"rate_limit_{api_token.tenant_id}"

                    redis_client.zadd(key, {current_time: current_time})

                    redis_client.zremrangebyscore(key, 0, current_time - 60000)

                    request_count = redis_client.zcard(key)

                    if request_count > knowledge_rate_limit.limit:
                        # add ratelimit record
                        rate_limit_log = RateLimitLog(
                            tenant_id=api_token.tenant_id,
                            subscription_plan=knowledge_rate_limit.subscription_plan,
                            operation="knowledge",
                        )
                        db.session.add(rate_limit_log)
                        db.session.commit()
                        raise Forbidden(
                            "Sorry, you have reached the knowledge base request rate limit of your subscription."
                        )
            return view(*args, **kwargs)

        return decorated

    return interceptor


def validate_dataset_token(view=None):
    def decorator(view):
        @wraps(view)
        def decorated(*args, **kwargs):
            api_token = validate_and_get_api_token("dataset")
            tenant_account_join = (
                db.session.query(Tenant, TenantAccountJoin)
                .filter(Tenant.id == api_token.tenant_id)
                .filter(TenantAccountJoin.tenant_id == Tenant.id)
                .filter(TenantAccountJoin.role.in_(["owner"]))
                .filter(Tenant.status == TenantStatus.NORMAL)
                .one_or_none()
            )  # TODO: only owner information is required, so only one is returned.
            if tenant_account_join:
                tenant, ta = tenant_account_join
                account = db.session.query(Account).filter(Account.id == ta.account_id).first()
                # Login admin
                if account:
                    account.current_tenant = tenant
                    current_app.login_manager._update_request_context_with_user(account)  # type: ignore
                    user_logged_in.send(current_app._get_current_object(), user=_get_user())  # type: ignore
                else:
                    raise Unauthorized("Tenant owner account does not exist.")
            else:
                raise Unauthorized("Tenant does not exist.")
            return view(api_token.tenant_id, *args, **kwargs)

        return decorated

    if view:
        return decorator(view)

    # if view is None, it means that the decorator is used without parentheses
    # use the decorator as a function for method_decorators
    return decorator


def validate_and_get_api_token(scope: str | None = None):
    """
    Validate and get API token.
    """
    auth_header = request.headers.get("Authorization")
    if auth_header is None or " " not in auth_header:
        raise Unauthorized("Authorization header must be provided and start with 'Bearer'")

    auth_scheme, auth_token = auth_header.split(None, 1)
    auth_scheme = auth_scheme.lower()

    if auth_scheme != "bearer":
        raise Unauthorized("Authorization scheme must be 'Bearer'")

    current_time = datetime.now()
    cutoff_time = current_time - timedelta(minutes=1)
    with Session(db.engine, expire_on_commit=False) as session:
        update_stmt = (
            update(ApiToken)
            .where(
                ApiToken.token == auth_token,
                (ApiToken.last_used_at.is_(None) | (ApiToken.last_used_at < cutoff_time)),
                ApiToken.type == scope,
            )
            .values(last_used_at=current_time)
        )
        result = session.execute(update_stmt)
        if result.rowcount > 0:
            api_token = session.query(ApiToken).filter(
                ApiToken.token == auth_token,
                ApiToken.type == scope
            ).first()
        else:
            api_token = None

        if not api_token:
            stmt = select(ApiToken).where(ApiToken.token == auth_token, ApiToken.type == scope)
            api_token = session.scalar(stmt)
            if not api_token:
                raise Unauthorized("Access token is invalid")
        else:
            session.commit()

    return api_token


def create_or_update_end_user_for_user_id(app_model: App, user_id: Optional[str] = None) -> EndUser:
    """
    Create or update session terminal based on user ID.
    """
    if not user_id:
        user_id = "DEFAULT-USER"

    end_user = (
        db.session.query(EndUser)
        .filter(
            EndUser.tenant_id == app_model.tenant_id,
            EndUser.app_id == app_model.id,
            EndUser.session_id == user_id,
            EndUser.type == "service_api",
        )
        .first()
    )

    if end_user is None:
        end_user = EndUser(
            tenant_id=app_model.tenant_id,
            app_id=app_model.id,
            type="service_api",
            is_anonymous=user_id == "DEFAULT-USER",
            session_id=user_id,
        )
        db.session.add(end_user)
        db.session.commit()

    return end_user


class DatasetApiResource(Resource):
    method_decorators = [validate_dataset_token]


def get_app_by_task_name():
    """根据task_name获取app"""
    data = request.get_json()
    task_name = data.get("task_name")
    if task_name:
        return db.session.query(App).filter(App.task_name == task_name).first()
    else:
        logging.warning("当前请求中没有提供task_name")
        
        
def studio_task_validate_decorator(view: Optional[Callable] = None, *, fetch_user_arg: Optional[FetchUserArg] = None):
    def decorator(view_func):
        @wraps(view_func)
        def decorated_view(*args, **kwargs):
            api_request_service = ApiRequestService()
            start_time = time.time()
            # Get request parameters
            data = request.get_json()
            request_id = request.args.get("request_id")
            biz_name = request.args.get("biz_name")
            asyn = request.args.get('asyn')
            response_mode = data.get('response_mode')
            task_name = data.get('task_name')
            version = data.get('version')
            routing_key = request.args.get('routing_key')
            request_url: str = urljoin(request.host_url, request.full_path)
            logging.info(f"收到请求: {request.method} {request_url} {task_name} {version}")
            bool_asyn_value = bool_asyn(value=asyn)
            
            accept_request_metrics(task_name, version, asyn, response_mode)
            
            # Track API request before validation
            api_request_dict = {
                "run_id": "",
                "tenant_id": "",
                "path": request.path,
                "task_name": task_name,
                "request_id": request_id,
                "biz_name": biz_name,
                "asyn": asyn,
                "request": data,
                "response": {},
                "status": "finished",
                "ip": request.remote_addr,
                "api_token_id": "",
            }

            try:
                if bool_asyn_value is None:
                    raise ValueError("asyn参数必须是0或1")
                    
                if bool_asyn_value and not routing_key:
                    raise ValueError("异步接口缺少routing_key参数")
                
                if not task_name:
                    raise ValueError("缺少task_name参数")
                
                if not request_id:
                    raise ValueError("缺少request_id参数")
                
                if not biz_name:
                    raise ValueError("缺少biz_name参数")
                
                app_model = get_app_by_task_name()
                api_token_id = ""
                # if not dify_config.IS_PROD and not app_model:
                #     api_token = validate_and_get_api_token("app")
                #     api_token_id = api_token.id
                #     app_model = db.session.query(App).filter(App.id == api_token.app_id).first()
                api_request_dict["api_token_id"] = api_token_id
                
                if not app_model:
                    raise AppUnavailableError()

                if app_model.status != "normal":
                    raise Forbidden("The app's status is abnormal.")

                if not app_model.enable_api:
                    raise Forbidden("The app's API service has been disabled.")

                kwargs["app_model"] = app_model
                api_request_dict["tenant_id"] = app_model.tenant_id

                if fetch_user_arg:
                    if fetch_user_arg.fetch_from == WhereisUserArg.QUERY:
                        user_id = request.args.get("user")
                    elif fetch_user_arg.fetch_from == WhereisUserArg.JSON:
                        user_id = request.get_json().get("user")
                    elif fetch_user_arg.fetch_from == WhereisUserArg.FORM:
                        user_id = request.form.get("user")
                    else:
                        # use default-user
                        user_id = None

                    if not user_id and fetch_user_arg.required:
                        raise ValueError("Arg user must be provided.")

                    if user_id:
                        user_id = str(user_id)

                    kwargs["end_user"] = create_or_update_end_user_for_user_id(app_model, user_id)

                response = view_func(*args, **kwargs)
                
                # 从上下文g对象获取run_id
                _, _, _, _, run_id, _ = get_svc_ctx()
                
                # 非流式接口
                if response.mimetype == "application/json":
                    # 异步任务提交阶段返回
                    api_request_dict["run_id"] = run_id
                    api_request_dict["response"] = response.json
                    api_request_dict["status"] = "accept" if bool_asyn(asyn) else "finished"
                    
                elif response.is_streamed:
                    def wrapped_stream():
                        start_time = datetime.now()
                        streamed_data = []
                        try:
                            for chunk in response.response:
                                streamed_data.append(chunk)
                                yield chunk
                        except Exception as e:
                            status = "error"
                            response_data = handler_error(e)
                            error_code = response_data.get("code")
                            logging.error(f"异步任务流式接口返回异常: {e}")
                            raise e
                        else:
                            status = "finished"
                            response_data = {"streamed_data": streamed_data}
                            error_code = ""
                        finally:
                            # 接口完成才记录耗时
                            if status == "finished":
                                time_cost_float = time.time() - start_time.timestamp()
                                report_sync_prometheus(dify_config.SERVICE_NAME, request.path, time_cost_float)
                            record_task_run_metrics(task_name, version, asyn, response_mode, status, time_cost_float)
                            api_request_service.async_task_update_api_requests(
                                run_id=run_id,
                                response=response_data,
                                status=status,
                                async_task_start_time=start_time,
                                error_code=error_code
                            )
                    api_request_dict["run_id"] = run_id
                    api_request_dict["status"] = "accept"
                    return Response(
                        stream_with_context(wrapped_stream()),
                        status=200,
                        mimetype="text/event-stream"
                    )
                return response
                
            except Exception as e:
                api_request_dict["status"] = "error"
                api_request_dict["response"] = handler_error(e)
                api_request_dict["error_code"] = api_request_dict["response"].get("code")
                if response_mode == "streaming":
                    def _generator():
                        d = AsyncResponseEntity(
                            request_id=request_id,
                            biz_name=biz_name,
                            code=api_request_dict["error_code"],
                            message=api_request_dict["response"].get("code"),
                            status=400
                        ).model_dump()
                        d["event"] = "error"
                        yield "data: " + json.dumps(d, ensure_ascii=False) + "\n\n"
                        return
                    return helper.compact_generate_response(_generator())
                else:
                    raise e
            finally:
                time_cost_float = time.time() - start_time
                time_cost = "{:.3f}".format(time_cost_float)
                info_list = [
                    f"请求完成: {request.method} {request_url} {task_name} {version}",
                ]
                if response_mode != "streaming":
                    if not bool_asyn_value:
                        # 同步任务记录指标
                        info_list += [f"接口耗时: {time_cost} s"]
                        if api_request_dict["status"] == "finished":
                            report_sync_prometheus(dify_config.SERVICE_NAME, request.path, time_cost_float)
                        record_task_run_metrics(task_name, version, asyn, response_mode, api_request_dict["status"], time_cost_float)
                    else:
                        # 异步任务提交阶段失败记录指标
                        if api_request_dict["status"] == "error":
                            record_task_run_metrics(task_name, version, asyn, response_mode, api_request_dict["status"], time_cost_float)
                if api_request_dict["status"] == "error":
                    logging.warning(
                        f"异步任务执行失败, task_name: {task_name}, version: {version}," \
                        f" error_code: {api_request_dict.get("error_code")}," \
                        f" error_msg: {api_request_dict["response"].get("message")}"
                    )
                logging.info("\t".join(info_list))
                api_request_service.add_api_requests(**api_request_dict)

        return decorated_view

    if view is None:
        return decorator
    else:
        return decorator(view)