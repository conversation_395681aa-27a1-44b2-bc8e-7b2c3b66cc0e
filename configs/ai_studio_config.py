import pathlib

from pydantic import Field
from .app_config import DifyConfig
from atta_ai_common.configs.config_library.apollo import ApolloServiceSelfConfig
from atta_ai_common.configs.config_library.kms import KMSConfig
from atta_ai_common.configs.config_library.mysql import MySQLConfig
from atta_ai_common.configs.package_config import common_config
from atta_ai_common.configs.config_library.apollo import ApolloBaseConfig
from atta_ai_common.oss.utils import OSS_PATH_PREFIX
from typing import Dict, Any, List


class ApolloAttaCommonConfig(ApolloBaseConfig):
    feign_access_token = ""

    def __init__(self):
        super().__init__()
        keymap = {"feign_access_token": "feign.access.token"}
        self.get_apollo_configs(
            app="atta-common", namespace="DevCenter.atta-common", keymap=keymap
        )

class AIStudioConfigManager(ApolloServiceSelfConfig, MySQLConfig, KMSConfig, ApolloAttaCommonConfig):

    local_mysql_host = "mysql.sitxtrt.sh"
    local_redis_host = "redis.sitxtrt.sh"

    def set_config_by_manager(self, obj: DifyConfig):
        manager_values = {}
        for key, config_value in self.config.lib.items():
            if hasattr(config_value, "value"):
                manager_values[key] = config_value.value

        if manager_values:
            current_fields = obj.model_fields.keys()
            updated_fields = []
            for key, value in manager_values.items():
                # apollo、kms中key统一转大写
                key = key.upper()
                if key in current_fields:
                    setattr(
                        obj,
                        key,
                        self.structure_transform(
                            obj.model_fields[key].annotation, value
                        ),
                    )
                    updated_fields.append(key)

            if updated_fields:
                import logging

                logging.info(f"Updated config from Apollo for fields: {updated_fields}")
        self.redis_config_transform(obj)
        self.mysql_config_transform(obj)
        self.oss_config_transform(obj)

    def structure_transform(self, t, v):
        if t is bool and isinstance(v, str):
            return v.lower() == "true"
        if t is int and isinstance(v, str):
            return int(v)
        if t is float and isinstance(v, str):
            return float(v)
        return v

    def redis_config_transform(self, obj: DifyConfig):
        """redis配置转换"""
        redis_host_key = "spring.redis.host"
        redis_password_key = "spring.redis.password"
        redis_port_key = "spring.redis.port"
        if not all(
            [
                getattr(self, i, None)
                for i in [redis_host_key, redis_password_key, redis_port_key]
            ]
        ):
            raise ValueError("Redis配置不完整，请检查配置是否正确")

        obj.REDIS_HOST = self.local_redis_host or getattr(self, redis_host_key, None)
        obj.REDIS_PASSWORD = getattr(self, redis_password_key, None)
        obj.REDIS_PORT = int(getattr(self, redis_port_key, None))
        return obj

    def mysql_config_transform(self, obj: DifyConfig):
        """mysql配置转换"""
        mysql_ip_key = "mysql_ip"
        mysql_username_key = "mysql_username"
        mysql_password_key = "mysql_password"
        mysql_port_key = "mysql_port"
        mysql_db_key = "mysql_db"
        if not all(
            [
                getattr(self, i, None)
                for i in [mysql_ip_key, mysql_username_key, mysql_password_key, mysql_port_key, mysql_db_key]
            ]
        ):
            raise ValueError("MySQL配置不完整，请检查配置是否正确")

        obj.DB_HOST = self.local_mysql_host or getattr(self, mysql_ip_key, None)
        obj.DB_USERNAME = getattr(self, mysql_username_key, None)
        obj.DB_PASSWORD = getattr(self, mysql_password_key, None)
        obj.DB_PORT = int(getattr(self, mysql_port_key, None))
        obj.DB_DATABASE = "atta_ai_studio" or getattr(self, mysql_db_key, None)

        return obj

    def oss_config_transform(self, obj: DifyConfig):
        """oss配置转换, 基于kms中的oss配置"""
        access_key_id_key = "atta.storage.oss.studio.accessKeyId"
        access_key_secret = "atta.storage.oss.studio.accessKeySecret"
        endpoint = "atta.storage.oss.studio.endPoint"

        if not all(
            [
                getattr(self, i, None)
                for i in [access_key_id_key, access_key_secret, endpoint]
            ]
        ):
            raise ValueError("OSS配置不完整，请检查配置是否正确")

        obj.ALIYUN_OSS_ACCESS_KEY = getattr(self, access_key_id_key, None)
        obj.ALIYUN_OSS_SECRET_KEY = getattr(self, access_key_secret, None)
        obj.ALIYUN_OSS_ENDPOINT = getattr(self, endpoint, None)
        return obj


manager = AIStudioConfigManager()


class AIStudioConfig(DifyConfig):

    SERVICE_NAME: str = Field(default="atta-ai-studio", description="服务名称")

    PLUGIN_DAEMON_ENABLED: bool = Field(default=False, description="是否开启外部插件")

    CODE_NODE_ENABLED: bool = Field(default=False, description="是否启用代码节点")

    HTTP_NODE_ENABLED: bool = Field(default=False, description="是否启用http节点")
    
    CUSTOM_TOOL_ENABLED: bool = Field(default=False, description="是否启用自定义工具")

    SQLALCHEMY_DATABASE_URI_SCHEME: str = Field(
        default="mysql+pymysql", description="SQLALCHEMY指定数据库"
    )

    MODEL_PATH: str = Field(
        default=str(pathlib.Path(__file__).parent.parent / "model"),
        description="oss下载文件路径",
    )

    CUSTOM_PLUGIN_PATH: str = Field(
        default=str(pathlib.Path(__file__).parent.parent / "custom_plugin"),
        description="自定义插件路径",
    )

    AIHUBSERV_SERVICE_NAME: str = Field(
        default="aihubserv", description="AIHubServ服务名称"
    )

    AIHUBSERV_LOW_LEVEL_API: str = Field(
        default="/llm/aiPool/chat/completions", description="AIHubServ API"
    )

    AIHUBSERV_LOW_LEVEL_STREAM_API: str = Field(
        default="/llm/aiPool/chat/completions/stream", description="AIHubServ流式API"
    )

    AIHUBSERV_SERVICE_REGION: str = Field(default="sh", description="AIHubServ服务区域")

    FEIGN_ACCESS_TOKEN: str = Field(default="", description="Feign访问令牌")

    STATIC_PROXY_URL: str = Field(default="", description="静态代理地址")

    RPA_SERVICE_NAME: str = Field(default="atta-ai-rpa", description="RPA服务名称")

    # oauth配置
    BOSS_ACCESS_TOKEN_URL: str = Field(
        default="https://sitxtrt.xtrfr.cn/oauth/token", description="token url"
    )
    BOSS_AUTHORIZE_URL: str = Field(
        default="https://sitxtrt.xtrfr.cn/oauth/authorize", description="authorize url"
    )
    BOSS_API_BASE_URL: str = Field(
        default="https://sitxtrt.xtrfr.cn/oauth", description="api base url"
    )
    BOSS_CLIENT_ID: str = Field(default="aistudio", description="client id")
    BOSS_CLIENT_SECRET: str = Field(default="aistudio", description="client secret")
    BOSS_PERMISSION_CODE: str = Field(default="", description="dify登录code")

    # redis超时设置，任务运行不强依赖redis
    REDIS_SOCKET_TIMEOUT: int = Field(default=5, description="redis执行超时时间")
    REDIS_SOCKET_CONNECT_TIMEOUT: int = Field(
        default=3, description="redis连接超时时间"
    )

    # celery设置
    CELERY_SOFT_TIME_LIMIT: int = Field(
        default=1200, description="celery任务软超时时间"
    )
    CELERY_TIME_LIMIT: int = Field(default=1500, description="celery任务硬超时时间")
    
    MAX_TEMPLATE_TRANSFORM_OUTPUT_LENGTH: int = Field(
        default=1000000, description="模板转换节点输出最大长度，默认百万字符约10M"
    )
    
    REFRESH_TOKEN_EXPIRE_HOURS: int = Field(
        default=2, description="refresh token过期时间，默认2小时"
    )
    
    GUNICORN_WORKERS_NUM: int = Field(default=4, description="gunicorn workers数量")
    GUNICORN_THREADS_NUM: int = Field(default=16, description="gunicorn threads数量")

    def __init__(self):
        super().__init__()
        # 从apollo、kms中获取配置进行替换
        manager.set_config_by_manager(self)

        if (
            isinstance(self.PLUGIN_DAEMON_ENABLED, str)
            and self.PLUGIN_DAEMON_ENABLED.lower() == "true"
        ):
            self.PLUGIN_DAEMON_ENABLED = True
        if (
            isinstance(self.CODE_NODE_ENABLED, str)
            and self.CODE_NODE_ENABLED.lower() == "true"
        ):
            self.CODE_NODE_ENABLED = True
        if (
            isinstance(self.HTTP_NODE_ENABLED, str)
            and self.HTTP_NODE_ENABLED.lower() == "true"
        ):
            self.HTTP_NODE_ENABLED = True
        if (
            isinstance(self.CUSTOM_TOOL_ENABLED, str)
            and self.CUSTOM_TOOL_ENABLED.lower() == "true"
        ):
            self.CUSTOM_TOOL_ENABLED = True 
  
