import pytest
from unittest.mock import Mock, patch


class TestCeleryApp:
    """Test cases for celery_program/app.py"""

    @patch('celery_program.app.flask_app')
    def test_celery_app_initialization(self, mock_flask_app):
        """Test celery app initialization from flask extensions"""
        # Setup
        mock_celery_instance = Mock()
        mock_flask_app.extensions = {"celery": mock_celery_instance}
        
        # Import the module to trigger initialization
        import celery_program.app
        
        # Verify
        assert celery_program.app.celery_app == mock_celery_instance

    @patch('celery_program.app.flask_app')
    def test_celery_app_missing_extension(self, mock_flask_app):
        """Test celery app when extension is missing"""
        # Setup
        mock_flask_app.extensions = {}
        
        # Import should raise KeyError when celery extension is missing
        with pytest.raises(KeyError):
            # Force reimport by clearing module cache
            import sys
            if 'celery_program.app' in sys.modules:
                del sys.modules['celery_program.app']
            import celery_program.app

    @patch('celery_program.app.flask_app')
    def test_celery_app_extensions_none(self, mock_flask_app):
        """Test celery app when extensions is None"""
        # Setup
        mock_flask_app.extensions = None
        
        # Import should raise TypeError when extensions is None
        with pytest.raises(TypeError):
            # Force reimport by clearing module cache
            import sys
            if 'celery_program.app' in sys.modules:
                del sys.modules['celery_program.app']
            import celery_program.app

    def test_task_import(self):
        """Test that Task is properly imported"""
        from celery_program.app import Task
        from celery import Task as CeleryTask
        
        # Verify Task is the correct Celery Task class
        assert Task is CeleryTask

    @patch('celery_program.app.flask_app')
    def test_celery_app_attribute_access(self, mock_flask_app):
        """Test accessing celery_app attributes"""
        # Setup
        mock_celery_instance = Mock()
        mock_celery_instance.task = Mock()
        mock_celery_instance.conf = Mock()
        mock_flask_app.extensions = {"celery": mock_celery_instance}
        
        # Import the module
        import celery_program.app
        
        # Verify we can access celery app attributes
        assert hasattr(celery_program.app.celery_app, 'task')
        assert hasattr(celery_program.app.celery_app, 'conf')
        assert celery_program.app.celery_app.task == mock_celery_instance.task
        assert celery_program.app.celery_app.conf == mock_celery_instance.conf
