import pytest
from unittest.mock import Mock, patch
from celery.exceptions import SoftTimeLimitExceeded

from libs.exception import BaseHTTPException


class TestChatMessageLogic:
    """Test cases for chat message logic without importing the module"""

    def test_app_mode_validation_logic(self):
        """Test app mode validation logic"""
        # Test valid mode
        valid_mode = "advanced-chat"
        assert valid_mode == "advanced-chat"

        # Test invalid mode logic
        invalid_mode = "invalid_mode"
        with pytest.raises(ValueError, match="Invalid app mode invalid_mode"):
            if invalid_mode != "advanced-chat":
                raise ValueError(f"Invalid app mode {invalid_mode}")

    def test_app_not_found_logic(self):
        """Test app not found logic"""
        app_model = None

        # Should raise AttributeError when trying to access mode on None
        with pytest.raises(AttributeError):
            mode = app_model.mode  # This should raise AttributeError

    def test_chatflow_app_run_success_logic(self):
        """Test chatflow_app_run success logic"""
        # Mock workflow service
        mock_workflow_service = Mock()
        mock_workflow = Mock()
        mock_workflow_service.get_workflow_by_id.return_value = mock_workflow

        # Mock generator
        mock_generator = Mock()
        mock_response = {"answer": "test response", "conversation_id": "conv_123"}
        mock_generator.generate.return_value = mock_response

        # Mock entity
        mock_entity = Mock()
        expected_result = {"request_id": "req_123", "answer": "test response"}
        mock_entity.model_dump.return_value = expected_result

        # Test the logic
        workflow = mock_workflow_service.get_workflow_by_id("workflow_456")
        response = mock_generator.generate(
            app_model=Mock(),
            workflow=workflow,
            user=Mock(),
            args={"query": "test message"},
            invoke_from=Mock(),
            streaming=False
        )
        result = mock_entity.model_dump()

        # Verify
        assert response == mock_response
        assert result == expected_result
        mock_workflow_service.get_workflow_by_id.assert_called_once_with("workflow_456")

    def test_chatflow_soft_time_limit_logic(self):
        """Test chatflow soft time limit exceeded logic"""
        # Mock generator that raises SoftTimeLimitExceeded
        mock_generator = Mock()
        mock_generator.generate.side_effect = SoftTimeLimitExceeded()

        # Mock entity for error response
        mock_entity = Mock()
        expected_result = {"code": "task_run_timeout", "message": "chatflow 执行超时"}
        mock_entity.model_dump.return_value = expected_result

        # Test the logic
        try:
            mock_generator.generate()
        except SoftTimeLimitExceeded:
            result = mock_entity.model_dump()
            assert result == expected_result

    def test_chatflow_base_http_exception_logic(self):
        """Test chatflow BaseHTTPException logic"""
        # Create test exception
        test_exception = BaseHTTPException("Test HTTP error")
        test_exception.error_code = "test_error"

        # Mock generator that raises BaseHTTPException
        mock_generator = Mock()
        mock_generator.generate.side_effect = test_exception

        # Mock entity for error response
        mock_entity = Mock()
        expected_result = {"code": "test_error", "message": "chatflow 执行主动异常 Test HTTP error"}
        mock_entity.model_dump.return_value = expected_result

        # Test the logic
        try:
            mock_generator.generate()
        except BaseHTTPException as e:
            result = mock_entity.model_dump()
            assert result == expected_result
            assert e.error_code == "test_error"

    def test_chatflow_unknown_exception_logic(self):
        """Test chatflow unknown exception logic"""
        # Create test exception
        test_exception = RuntimeError("Unknown error")

        # Mock generator that raises unknown exception
        mock_generator = Mock()
        mock_generator.generate.side_effect = test_exception

        # Mock entity for error response
        mock_entity = Mock()
        expected_result = {"code": "unknow", "message": "chatflow 执行未知异常"}
        mock_entity.model_dump.return_value = expected_result

        # Test the logic
        try:
            mock_generator.generate()
        except RuntimeError:
            result = mock_entity.model_dump()
            assert result == expected_result


class TestChatflowAppRun:
    """Test cases for chatflow_app_run function"""

    @pytest.fixture
    def mock_dependencies(self):
        """Mock all external dependencies"""
        with patch.multiple(
            'celery_program.chat_message',
            WorkflowService=Mock(),
            AdvancedChatAppGenerator=Mock(),
            ChatflowAPIResponseEntity=Mock(),
            AsyncResponseEntity=Mock(),
            InvokeFrom=Mock(),
            logger=Mock()
        ) as mocks:
            yield mocks

    @pytest.fixture
    def sample_params(self):
        """Sample parameters for chatflow_app_run"""
        return {
            "request_id": "req_123",
            "biz_name": "test_biz",
            "run_id": "run_789",
            "app_model": Mock(),
            "user": Mock(),
            "args": {"query": "test message"},
            "workflow_id": "workflow_456"
        }

    def test_chatflow_app_run_success(self, mock_dependencies, sample_params):
        """Test chatflow_app_run successful execution"""
        # Setup
        mock_workflow = Mock()
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.return_value = mock_workflow

        mock_generator = Mock()
        mock_response = {"answer": "test response", "conversation_id": "conv_123"}
        mock_generator.generate.return_value = mock_response
        mock_dependencies['AdvancedChatAppGenerator'].return_value = mock_generator

        mock_entity = Mock()
        expected_result = {"request_id": "req_123", "answer": "test response"}
        mock_entity.model_dump.return_value = expected_result
        mock_dependencies['ChatflowAPIResponseEntity'].return_value = mock_entity

        mock_invoke_from = Mock()
        mock_dependencies['InvokeFrom'].SERVICE_API = mock_invoke_from

        # Execute
        from celery_program.chat_message import chatflow_app_run
        result = chatflow_app_run(**sample_params)

        # Verify
        assert result == expected_result
        mock_dependencies['WorkflowService'].assert_called_once()
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.assert_called_once_with("workflow_456")

        mock_dependencies['AdvancedChatAppGenerator'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            run_id="run_789"
        )

        mock_generator.generate.assert_called_once_with(
            app_model=sample_params["app_model"],
            workflow=mock_workflow,
            user=sample_params["user"],
            args={"query": "test message"},
            invoke_from=mock_invoke_from,
            streaming=False
        )

        mock_dependencies['ChatflowAPIResponseEntity'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            **mock_response
        )

    def test_chatflow_app_run_soft_time_limit_exceeded(self, mock_dependencies, sample_params):
        """Test chatflow_app_run with SoftTimeLimitExceeded"""
        # Setup
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.return_value = Mock()
        mock_dependencies['AdvancedChatAppGenerator'].return_value.generate.side_effect = SoftTimeLimitExceeded()

        mock_entity = Mock()
        expected_result = {"code": "task_run_timeout", "message": "chatflow 执行超时"}
        mock_entity.model_dump.return_value = expected_result
        mock_dependencies['AsyncResponseEntity'].return_value = mock_entity

        # Execute
        from celery_program.chat_message import chatflow_app_run
        result = chatflow_app_run(**sample_params)

        # Verify
        assert result == expected_result
        mock_dependencies['logger'].error.assert_called_once_with("chatflow 执行超时")
        mock_dependencies['AsyncResponseEntity'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            code="task_run_timeout",
            message="chatflow 执行超时"
        )

    def test_chatflow_app_run_base_http_exception(self, mock_dependencies, sample_params):
        """Test chatflow_app_run with BaseHTTPException"""
        # Setup
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.return_value = Mock()

        test_exception = BaseHTTPException("Test HTTP error")
        test_exception.error_code = "test_error"
        mock_dependencies['AdvancedChatAppGenerator'].return_value.generate.side_effect = test_exception

        mock_entity = Mock()
        expected_result = {"code": "test_error", "message": "chatflow 执行主动异常 Test HTTP error"}
        mock_entity.model_dump.return_value = expected_result
        mock_dependencies['AsyncResponseEntity'].return_value = mock_entity

        # Execute
        from celery_program.chat_message import chatflow_app_run
        result = chatflow_app_run(**sample_params)

        # Verify
        assert result == expected_result
        mock_dependencies['logger'].error.assert_called_once_with(f"chatflow 执行主动异常: {test_exception}")
        mock_dependencies['AsyncResponseEntity'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            code="test_error",
            message="chatflow 执行主动异常 Test HTTP error"
        )

    def test_chatflow_app_run_unknown_exception(self, mock_dependencies, sample_params):
        """Test chatflow_app_run with unknown exception"""
        # Setup
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.return_value = Mock()

        test_exception = RuntimeError("Unknown error")
        mock_dependencies['AdvancedChatAppGenerator'].return_value.generate.side_effect = test_exception

        mock_entity = Mock()
        expected_result = {"code": "unknow", "message": "chatflow 执行未知异常"}
        mock_entity.model_dump.return_value = expected_result
        mock_dependencies['AsyncResponseEntity'].return_value = mock_entity

        # Execute
        from celery_program.chat_message import chatflow_app_run
        result = chatflow_app_run(**sample_params)

        # Verify
        assert result == expected_result
        mock_dependencies['logger'].error.assert_called_once_with(f"chatflow 执行未知异常: {test_exception}")
        mock_dependencies['AsyncResponseEntity'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            code="unknow",
            message="chatflow 执行未知异常"
        )


class TestChatflowAppRun:
    """Test cases for chatflow_app_run function"""

    @pytest.fixture
    def mock_dependencies(self):
        """Mock all external dependencies"""
        with patch.multiple(
            'celery_program.chat_message',
            WorkflowService=Mock(),
            AdvancedChatAppGenerator=Mock(),
            ChatflowAPIResponseEntity=Mock(),
            AsyncResponseEntity=Mock(),
            InvokeFrom=Mock(),
            logger=Mock()
        ) as mocks:
            yield mocks

    @pytest.fixture
    def sample_params(self):
        """Sample parameters for chatflow_app_run"""
        return {
            "request_id": "req_123",
            "biz_name": "test_biz",
            "run_id": "run_789",
            "app_model": Mock(),
            "user": Mock(),
            "args": {"query": "test message"},
            "workflow_id": "workflow_456"
        }

    def test_chatflow_app_run_success(self, mock_dependencies, sample_params):
        """Test chatflow_app_run successful execution"""
        # Setup
        mock_workflow = Mock()
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.return_value = mock_workflow
        
        mock_generator = Mock()
        mock_response = {"answer": "test response", "conversation_id": "conv_123"}
        mock_generator.generate.return_value = mock_response
        mock_dependencies['AdvancedChatAppGenerator'].return_value = mock_generator
        
        mock_entity = Mock()
        expected_result = {"request_id": "req_123", "answer": "test response"}
        mock_entity.model_dump.return_value = expected_result
        mock_dependencies['ChatflowAPIResponseEntity'].return_value = mock_entity
        
        mock_invoke_from = Mock()
        mock_dependencies['InvokeFrom'].SERVICE_API = mock_invoke_from
        
        # Execute
        from celery_program.chat_message import chatflow_app_run
        result = chatflow_app_run(**sample_params)
        
        # Verify
        assert result == expected_result
        mock_dependencies['WorkflowService'].assert_called_once()
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.assert_called_once_with("workflow_456")
        
        mock_dependencies['AdvancedChatAppGenerator'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            run_id="run_789"
        )
        
        mock_generator.generate.assert_called_once_with(
            app_model=sample_params["app_model"],
            workflow=mock_workflow,
            user=sample_params["user"],
            args={"query": "test message"},
            invoke_from=mock_invoke_from,
            streaming=False
        )
        
        mock_dependencies['ChatflowAPIResponseEntity'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            **mock_response
        )

    def test_chatflow_app_run_soft_time_limit_exceeded(self, mock_dependencies, sample_params):
        """Test chatflow_app_run with SoftTimeLimitExceeded"""
        # Setup
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.return_value = Mock()
        mock_dependencies['AdvancedChatAppGenerator'].return_value.generate.side_effect = SoftTimeLimitExceeded()
        
        mock_entity = Mock()
        expected_result = {"code": "task_run_timeout", "message": "chatflow 执行超时"}
        mock_entity.model_dump.return_value = expected_result
        mock_dependencies['AsyncResponseEntity'].return_value = mock_entity
        
        # Execute
        from celery_program.chat_message import chatflow_app_run
        result = chatflow_app_run(**sample_params)
        
        # Verify
        assert result == expected_result
        mock_dependencies['logger'].error.assert_called_once_with("chatflow 执行超时")
        mock_dependencies['AsyncResponseEntity'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            code="task_run_timeout",
            message="chatflow 执行超时"
        )

    def test_chatflow_app_run_base_http_exception(self, mock_dependencies, sample_params):
        """Test chatflow_app_run with BaseHTTPException"""
        # Setup
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.return_value = Mock()
        
        test_exception = BaseHTTPException("Test HTTP error")
        test_exception.error_code = "test_error"
        mock_dependencies['AdvancedChatAppGenerator'].return_value.generate.side_effect = test_exception
        
        mock_entity = Mock()
        expected_result = {"code": "test_error", "message": "chatflow 执行主动异常 Test HTTP error"}
        mock_entity.model_dump.return_value = expected_result
        mock_dependencies['AsyncResponseEntity'].return_value = mock_entity
        
        # Execute
        from celery_program.chat_message import chatflow_app_run
        result = chatflow_app_run(**sample_params)
        
        # Verify
        assert result == expected_result
        mock_dependencies['logger'].error.assert_called_once_with(f"chatflow 执行主动异常: {test_exception}")
        mock_dependencies['AsyncResponseEntity'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            code="test_error",
            message="chatflow 执行主动异常 Test HTTP error"
        )

    def test_chatflow_app_run_unknown_exception(self, mock_dependencies, sample_params):
        """Test chatflow_app_run with unknown exception"""
        # Setup
        mock_dependencies['WorkflowService'].return_value.get_workflow_by_id.return_value = Mock()
        
        test_exception = RuntimeError("Unknown error")
        mock_dependencies['AdvancedChatAppGenerator'].return_value.generate.side_effect = test_exception
        
        mock_entity = Mock()
        expected_result = {"code": "unknow", "message": "chatflow 执行未知异常"}
        mock_entity.model_dump.return_value = expected_result
        mock_dependencies['AsyncResponseEntity'].return_value = mock_entity
        
        # Execute
        from celery_program.chat_message import chatflow_app_run
        result = chatflow_app_run(**sample_params)
        
        # Verify
        assert result == expected_result
        mock_dependencies['logger'].error.assert_called_once_with(f"chatflow 执行未知异常: {test_exception}")
        mock_dependencies['AsyncResponseEntity'].assert_called_once_with(
            request_id="req_123",
            biz_name="test_biz",
            code="unknow",
            message="chatflow 执行未知异常"
        )
