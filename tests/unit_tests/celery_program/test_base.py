import pytest
import datetime
import json
from unittest.mock import Mock, patch

from celery_program.base import common_task, run_task_with_span, send_api_result, dump_celery_metrics


def create_mock_task_func(return_value=None, side_effect=None, name="test_task_function"):
    """Helper function to create a mock task function with __name__ attribute"""
    mock_func = Mock(return_value=return_value, side_effect=side_effect)
    mock_func.__name__ = name
    return mock_func


class TestCommonTask:
    """Test cases for common_task decorator"""

    @pytest.fixture
    def mock_dependencies(self):
        """Mock all external dependencies"""
        mocks = {
            'MQ': Mock(),
            'get_service_name': <PERSON><PERSON>(return_value="atta-ai-test"),
            'ApiRequestService': <PERSON><PERSON>(),
            'set_svc_ctx': <PERSON><PERSON>(),
            'handler_error': <PERSON><PERSON>(),
            'send_api_result': <PERSON><PERSON>(),
            'dump_celery_metrics': <PERSON><PERSON>(),
            'record_task_run_metrics': <PERSON><PERSON>(),
            'report_api_error_prometheus': <PERSON><PERSON>(),
            'config_opts': <PERSON><PERSON>(service_name="test-service"),
            'LOCAL_DEV_MODE': False,
            'common_config': Mock(skywalking_enable=False)
        }

        with patch.multiple('celery_program.base', **mocks):
            yield mocks

    @pytest.fixture
    def sample_task_kwargs(self):
        """Sample task kwargs for testing"""
        return {
            "task_name": "test_task",
            "version": "1.0",
            "request_id": "req_123",
            "biz_name": "test_biz",
            "run_id": "run_456",
            "request_time": "2023-01-01T10:00:00"
        }

    def test_common_task_decorator_basic_success(self, mock_dependencies, sample_task_kwargs):
        """Test common_task decorator with successful task execution"""
        # Setup
        mock_task_func = create_mock_task_func({"result": "success"})
        mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests = Mock()

        # Apply decorator
        decorated_func = common_task()(mock_task_func)

        # Execute
        decorated_func(**sample_task_kwargs)

        # Verify task function was called
        mock_task_func.assert_called_once_with(**sample_task_kwargs)

        # Verify MQ was configured and send_api_result was called
        mock_dependencies['send_api_result'].assert_called_once()

        # Verify API request service was called
        mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests.assert_called_once()

    def test_common_task_decorator_with_custom_mq(self, mock_dependencies, sample_task_kwargs):
        """Test common_task decorator with custom MQ configuration"""
        # Setup custom MQ
        custom_mq = Mock()
        custom_mq.routing_key = "custom.routing.key"
        custom_mq.exchange = "custom.exchange"

        mock_task_func = create_mock_task_func({"result": "success"})

        # Apply decorator with custom MQ
        decorated_func = common_task(mq=custom_mq)(mock_task_func)
        
        # Execute
        decorated_func(**sample_task_kwargs)
        
        # Verify custom MQ was used
        mock_dependencies['send_api_result'].assert_called_once()
        call_args = mock_dependencies['send_api_result'].call_args[1]
        assert call_args['mq'].routing_key == "custom.routing.key"
        assert call_args['mq'].exchange == "custom.exchange"

    def test_common_task_decorator_with_routing_key_override(self, mock_dependencies, sample_task_kwargs):
        """Test common_task decorator with routing_key parameter override"""
        # Setup
        mock_task_func = create_mock_task_func({"result": "success"})
        mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests = Mock()
        sample_task_kwargs["routing_key"] = "override.routing.key"

        # Apply decorator
        decorated_func = common_task()(mock_task_func)

        # Execute
        decorated_func(**sample_task_kwargs)

        # Verify routing key was overridden
        mock_dependencies['send_api_result'].assert_called_once()
        call_args = mock_dependencies['send_api_result'].call_args[1]
        assert call_args['mq'].routing_key == "override.routing.key"

    def test_common_task_decorator_task_function_exception(self, mock_dependencies, sample_task_kwargs):
        """Test common_task decorator when task function raises exception"""
        # Setup
        test_exception = ValueError("Test error")
        mock_task_func = create_mock_task_func(side_effect=test_exception)
        mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests = Mock()
        mock_dependencies['handler_error'].return_value = {
            "code": "test_error",
            "message": "Test error message"
        }

        # Apply decorator
        decorated_func = common_task()(mock_task_func)

        # Execute and expect exception
        with pytest.raises(ValueError):
            decorated_func(**sample_task_kwargs)
        
        # Verify error handling
        mock_dependencies['handler_error'].assert_called_once_with(test_exception)
        mock_dependencies['report_api_error_prometheus'].assert_called_once()
        
        # Verify API request service was called with error status
        update_call = mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests
        update_call.assert_called_once()
        call_kwargs = update_call.call_args[1]
        assert call_kwargs["status"] == "error"
        assert call_kwargs["error_code"] == "test_error"

    def test_common_task_decorator_task_function_returns_none(self, mock_dependencies, sample_task_kwargs):
        """Test common_task decorator when task function returns None"""
        # Setup
        mock_task_func = create_mock_task_func(return_value=None)
        mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests = Mock()

        # Apply decorator
        decorated_func = common_task()(mock_task_func)

        # Execute and expect ValueError
        with pytest.raises(ValueError, match="异步任务函数的返回值为None"):
            decorated_func(**sample_task_kwargs)

    def test_common_task_decorator_mq_send_failure(self, mock_dependencies, sample_task_kwargs):
        """Test common_task decorator when MQ send fails"""
        # Setup
        mock_task_func = Mock(return_value={"result": "success"})
        mq_exception = Exception("MQ send failed")
        mock_dependencies['send_api_result'].side_effect = mq_exception
        
        # Apply decorator
        decorated_func = common_task()(mock_task_func)
        
        # Execute and expect exception
        with pytest.raises(Exception, match="MQ send failed"):
            decorated_func(**sample_task_kwargs)
        
        # Verify error handling
        mock_dependencies['report_api_error_prometheus'].assert_called()
        
        # Verify API request service was called with MQ error
        update_call = mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests
        update_call.assert_called_once()
        call_kwargs = update_call.call_args[1]
        assert call_kwargs["status"] == "error"
        assert call_kwargs["error_code"] == "mq_send_error"

    def test_common_task_decorator_default_mq_configuration(self, mock_dependencies, sample_task_kwargs):
        """Test common_task decorator with default MQ configuration"""
        # Setup
        mock_mq_instance = Mock()
        mock_mq_instance.routing_key = None
        mock_mq_instance.exchange = None
        mock_dependencies['MQ'].return_value = mock_mq_instance
        
        mock_task_func = create_mock_task_func({"result": "success"})
        mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests = Mock()

        # Apply decorator
        decorated_func = common_task()(mock_task_func)

        # Execute
        decorated_func(**sample_task_kwargs)

        # Verify default MQ configuration was applied
        assert mock_mq_instance.routing_key == "celery.test_task"
        assert mock_mq_instance.exchange == "celery.ai.test.result"

    @patch('celery_program.base.LOCAL_DEV_MODE', True)
    def test_common_task_decorator_local_dev_mode(self, sample_task_kwargs):
        """Test common_task decorator in local development mode"""
        mocks = {
            'MQ': Mock(),
            'get_service_name': Mock(return_value="atta-ai-test"),
            'ApiRequestService': Mock(),
            'set_svc_ctx': Mock(),
            'send_api_result': Mock(),
            'dump_celery_metrics': Mock(),
            'record_task_run_metrics': Mock(),
            'common_config': Mock(skywalking_enable=False)
        }

        with patch.multiple('celery_program.base', **mocks):
            # Setup
            mock_task_func = create_mock_task_func({"result": "success"})
            mocks['ApiRequestService'].return_value.async_task_update_api_requests = Mock()

            # Apply decorator
            decorated_func = common_task()(mock_task_func)

            # The decorator should return the task_decorator_wrapper directly in dev mode
            # Execute
            decorated_func(**sample_task_kwargs)

            # Verify task was executed
            mock_task_func.assert_called_once_with(**sample_task_kwargs)

    @patch('celery_program.base.LOCAL_DEV_MODE', False)
    @patch('celery_program.base.common_config')
    def test_common_task_decorator_skywalking_enabled(self, mock_common_config, sample_task_kwargs):
        """Test common_task decorator with SkyWalking enabled"""
        # Setup
        mock_common_config.skywalking_enable = True
        mock_task_func = create_mock_task_func({"result": "success"})

        with patch('celery_program.base.run_task_with_span') as mock_run_task_with_span:
            # Apply decorator
            decorated_func = common_task()(mock_task_func)

            # Execute
            decorated_func(**sample_task_kwargs)

            # Verify run_task_with_span was called
            mock_run_task_with_span.assert_called_once()

    def test_common_task_decorator_metrics_recording(self, mock_dependencies, sample_task_kwargs):
        """Test common_task decorator records metrics correctly"""
        # Setup
        mock_task_func = create_mock_task_func({"result": "success"})
        mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests = Mock()

        # Apply decorator
        decorated_func = common_task(path="/test/path")(mock_task_func)

        # Execute
        decorated_func(**sample_task_kwargs)
        
        # Verify metrics were recorded
        mock_dependencies['dump_celery_metrics'].assert_called_once()
        mock_dependencies['record_task_run_metrics'].assert_called_once()
        
        # Check record_task_run_metrics call arguments
        call_args = mock_dependencies['record_task_run_metrics'].call_args[0]
        assert call_args[0] == "test_task"  # task_name
        assert call_args[1] == "1.0"        # version
        assert call_args[2] == "1"          # count
        assert call_args[3] == "blocking"   # mode
        assert call_args[4] == "finished"   # status

    def test_common_task_decorator_service_context_setup(self, mock_dependencies, sample_task_kwargs):
        """Test common_task decorator sets up service context correctly"""
        # Setup
        mock_task_func = create_mock_task_func({"result": "success"})
        mock_dependencies['ApiRequestService'].return_value.async_task_update_api_requests = Mock()

        # Apply decorator
        decorated_func = common_task()(mock_task_func)

        # Execute
        decorated_func(**sample_task_kwargs)
        
        # Verify service context was set
        mock_dependencies['set_svc_ctx'].assert_called_once_with(
            task_name="test_task",
            version="1.0",
            request_id="req_123",
            biz_name="test_biz",
            run_id="run_456",
            data={"streaming": False}
        )


class TestSendApiResult:
    """Test cases for send_api_result function"""

    @pytest.fixture
    def mock_mq(self):
        """Mock MQ object"""
        mq = Mock()
        mq.host = "localhost"
        mq.port = 5672
        mq.username = "user"
        mq.password = "pass"
        mq.exchange = "test_exchange"
        mq.routing_key = "test.routing.key"
        return mq

    @patch('celery_program.base.RabbitmqSender')
    @patch('celery_program.base.asdict')
    def test_send_api_result_success(self, mock_asdict, mock_rabbitmq_sender, mock_mq):
        """Test send_api_result with successful message sending"""
        # Setup
        mock_asdict.return_value = {
            "host": "localhost",
            "port": 5672,
            "username": "user",
            "password": "pass",
            "exchange": "test_exchange",
            "routing_key": "test.routing.key"
        }

        mock_sender_instance = Mock()
        mock_rabbitmq_sender.return_value = mock_sender_instance

        test_result = {"status": "success", "data": "test_data"}

        # Execute
        send_api_result(mq=mock_mq, result=test_result)

        # Verify
        mock_asdict.assert_called_once_with(mock_mq)
        mock_rabbitmq_sender.assert_called_once_with(
            host="localhost",
            port=5672,
            username="user",
            password="pass",
            exchange="test_exchange",
            routing_key="test.routing.key"
        )
        mock_sender_instance.send_message.assert_called_once_with(
            send_message=json.dumps(test_result, ensure_ascii=False)
        )

    @patch('celery_program.base.RabbitmqSender')
    @patch('celery_program.base.asdict')
    def test_send_api_result_with_complex_data(self, mock_asdict, mock_rabbitmq_sender, mock_mq):
        """Test send_api_result with complex data structure"""
        # Setup
        mock_asdict.return_value = {"host": "localhost"}
        mock_sender_instance = Mock()
        mock_rabbitmq_sender.return_value = mock_sender_instance

        complex_result = {
            "status": "success",
            "data": {
                "nested": {"key": "value"},
                "array": [1, 2, 3],
                "unicode": "测试数据"
            },
            "timestamp": "2023-01-01T10:00:00"
        }

        # Execute
        send_api_result(mq=mock_mq, result=complex_result)

        # Verify JSON serialization with ensure_ascii=False
        expected_json = json.dumps(complex_result, ensure_ascii=False)
        mock_sender_instance.send_message.assert_called_once_with(send_message=expected_json)

    @patch('celery_program.base.RabbitmqSender')
    @patch('celery_program.base.asdict')
    def test_send_api_result_sender_exception(self, mock_asdict, mock_rabbitmq_sender, mock_mq):
        """Test send_api_result when RabbitmqSender raises exception"""
        # Setup
        mock_asdict.return_value = {"host": "localhost"}
        mock_sender_instance = Mock()
        mock_sender_instance.send_message.side_effect = Exception("Connection failed")
        mock_rabbitmq_sender.return_value = mock_sender_instance

        test_result = {"status": "error"}

        # Execute and expect exception to be re-raised due to @common_error_catch_decorator
        with pytest.raises(Exception, match="Connection failed"):
            send_api_result(mq=mock_mq, result=test_result)

    @patch('celery_program.base.RabbitmqSender')
    @patch('celery_program.base.asdict')
    def test_send_api_result_asdict_conversion(self, mock_asdict, mock_rabbitmq_sender, mock_mq):
        """Test send_api_result properly converts MQ object to dict"""
        # Setup
        expected_dict = {
            "host": "test_host",
            "port": 1234,
            "exchange": "test_exchange"
        }
        mock_asdict.return_value = expected_dict
        mock_sender_instance = Mock()
        mock_rabbitmq_sender.return_value = mock_sender_instance

        # Execute
        send_api_result(mq=mock_mq, result={"test": "data"})

        # Verify asdict was called with MQ object
        mock_asdict.assert_called_once_with(mock_mq)

        # Verify RabbitmqSender was initialized with converted dict
        mock_rabbitmq_sender.assert_called_once_with(**expected_dict)


class TestDumpCeleryMetrics:
    """Test cases for dump_celery_metrics function"""

    @patch('celery_program.base.report_async_prometheus')
    @patch('celery_program.base.config_opts')
    def test_dump_celery_metrics_success(self, mock_config_opts, mock_report_async_prometheus):
        """Test dump_celery_metrics with successful metrics reporting"""
        # Setup
        mock_config_opts.service_name = "test-service"

        task_name = "test_task"
        request_time = "2023-01-01T10:00:00"
        timestamp = datetime.datetime(2023, 1, 1, 10, 5, 30)  # 5 minutes 30 seconds later
        async_task_start_time = datetime.datetime(2023, 1, 1, 10, 2, 15)  # 2 minutes 15 seconds after request

        # Execute
        dump_celery_metrics(task_name, request_time, timestamp, async_task_start_time)

        # Verify
        mock_report_async_prometheus.assert_called_once_with(
            "test-service",
            "test_task",
            135.0,  # duration_call_to_task_start (2 minutes 15 seconds)
            330.0,  # duration_call_to_finish (5 minutes 30 seconds)
            195.0   # duration_task_start_to_finish (3 minutes 15 seconds)
        )

    @patch('celery_program.base.report_async_prometheus')
    @patch('celery_program.base.config_opts')
    @patch('celery_program.base.logging')
    def test_dump_celery_metrics_negative_duration(self, mock_logging, mock_config_opts, mock_report_async_prometheus):
        """Test dump_celery_metrics with negative task duration (task not finished)"""
        # Setup
        mock_config_opts.service_name = "test-service"

        task_name = "test_task"
        request_time = "2023-01-01T10:00:00"
        timestamp = datetime.datetime(2023, 1, 1, 10, 1, 0)  # 1 minute after request
        async_task_start_time = datetime.datetime(2023, 1, 1, 10, 2, 0)  # 2 minutes after request (later than timestamp)

        # Execute
        dump_celery_metrics(task_name, request_time, timestamp, async_task_start_time)

        # Verify warning was logged and prometheus not called
        mock_logging.warning.assert_called_once_with(
            "异步任务未成功执行，timestamp未更新，不记录Prometheus Metric"
        )
        mock_report_async_prometheus.assert_not_called()

    @patch('celery_program.base.report_async_prometheus')
    @patch('celery_program.base.config_opts')
    @patch('celery_program.base.logging')
    def test_dump_celery_metrics_datetime_parse_exception(self, mock_logging, mock_config_opts, mock_report_async_prometheus):
        """Test dump_celery_metrics with datetime parsing exception"""
        # Setup
        mock_config_opts.service_name = "test-service"

        task_name = "test_task"
        request_time = "invalid-datetime-format"  # Invalid format
        timestamp = datetime.datetime(2023, 1, 1, 10, 5, 0)
        async_task_start_time = datetime.datetime(2023, 1, 1, 10, 2, 0)

        # Execute
        dump_celery_metrics(task_name, request_time, timestamp, async_task_start_time)

        # Verify warning was logged and prometheus not called
        mock_logging.warning.assert_called_once()
        warning_call_args = mock_logging.warning.call_args[0][0]
        assert "记录prome失败" in warning_call_args
        mock_report_async_prometheus.assert_not_called()

    @patch('celery_program.base.report_async_prometheus')
    @patch('celery_program.base.config_opts')
    def test_dump_celery_metrics_with_datetime_object(self, mock_config_opts, mock_report_async_prometheus):
        """Test dump_celery_metrics with datetime object as request_time"""
        # Setup
        mock_config_opts.service_name = "test-service"

        task_name = "test_task"
        request_time = datetime.datetime(2023, 1, 1, 10, 0, 0)  # datetime object
        timestamp = datetime.datetime(2023, 1, 1, 10, 3, 0)
        async_task_start_time = datetime.datetime(2023, 1, 1, 10, 1, 0)

        # Execute
        dump_celery_metrics(task_name, request_time, timestamp, async_task_start_time)

        # Verify
        mock_report_async_prometheus.assert_called_once_with(
            "test-service",
            "test_task",
            60.0,   # duration_call_to_task_start (1 minute)
            180.0,  # duration_call_to_finish (3 minutes)
            120.0   # duration_task_start_to_finish (2 minutes)
        )

    @patch('celery_program.base.report_async_prometheus')
    @patch('celery_program.base.config_opts')
    @patch('celery_program.base.logging')
    def test_dump_celery_metrics_prometheus_exception(self, mock_logging, mock_config_opts, mock_report_async_prometheus):
        """Test dump_celery_metrics when report_async_prometheus raises exception"""
        # Setup
        mock_config_opts.service_name = "test-service"
        mock_report_async_prometheus.side_effect = Exception("Prometheus error")

        task_name = "test_task"
        request_time = "2023-01-01T10:00:00"
        timestamp = datetime.datetime(2023, 1, 1, 10, 3, 0)
        async_task_start_time = datetime.datetime(2023, 1, 1, 10, 1, 0)

        # Execute
        dump_celery_metrics(task_name, request_time, timestamp, async_task_start_time)

        # Verify warning was logged
        mock_logging.warning.assert_called_once()
        warning_call_args = mock_logging.warning.call_args[0][0]
        assert "记录prome失败" in warning_call_args
        assert "Prometheus error" in warning_call_args
