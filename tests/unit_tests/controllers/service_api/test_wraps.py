import pytest
import time
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from flask import Flask, request, g
from werkzeug.exceptions import Forbidden, Unauthorized

from controllers.service_api.wraps import (
    WhereisUserArg,
    FetchUserArg,
    DatasetApiResource,
    validate_and_get_api_token,
    create_or_update_end_user_for_user_id,
    get_app_by_task_name,
    validate_app_token,
    cloud_edition_billing_resource_check,
    cloud_edition_billing_knowledge_limit_check,
    cloud_edition_billing_rate_limit_check,
    validate_dataset_token,
    studio_task_validate_decorator
)
from controllers.service_api.app.error import AppUnavailableError
from models.account import TenantStatus


class TestWhereisUserArg:
    """Test cases for WhereisUserArg enum"""

    def test_whereis_user_arg_values(self):
        """Test WhereisUserArg enum values"""
        assert WhereisUserArg.QUERY.value == "query"
        assert WhereisUserArg.JSON.value == "json"
        assert WhereisUserArg.FORM.value == "form"


class TestFetchUserArg:
    """Test cases for FetchUserArg model"""

    def test_fetch_user_arg_creation(self):
        """Test FetchUserArg model creation"""
        # Test with required=True
        fetch_arg = FetchUserArg(fetch_from=WhereisUserArg.QUERY, required=True)
        assert fetch_arg.fetch_from == WhereisUserArg.QUERY
        assert fetch_arg.required is True

        # Test with default required=False
        fetch_arg = FetchUserArg(fetch_from=WhereisUserArg.JSON)
        assert fetch_arg.fetch_from == WhereisUserArg.JSON
        assert fetch_arg.required is False


class TestCreateOrUpdateEndUserLogic:
    """Test cases for create_or_update_end_user_for_user_id logic"""

    def test_create_or_update_end_user_existing_user_logic(self):
        """Test create_or_update_end_user_for_user_id logic with existing user"""
        # Setup
        mock_app = Mock()
        mock_app.tenant_id = "tenant_123"
        mock_app.id = "app_123"

        mock_db = Mock()
        mock_end_user = Mock()
        mock_db.session.query.return_value.filter.return_value.first.return_value = mock_end_user

        # Test the logic
        result = mock_db.session.query().filter().first()

        # Verify
        assert result == mock_end_user

    def test_create_or_update_end_user_new_user_logic(self):
        """Test create_or_update_end_user_for_user_id logic with new user"""
        # Setup
        mock_app = Mock()
        mock_app.tenant_id = "tenant_123"
        mock_app.id = "app_123"

        mock_db = Mock()
        mock_db.session.query.return_value.filter.return_value.first.return_value = None

        mock_end_user_class = Mock()
        mock_new_end_user = Mock()
        mock_end_user_class.return_value = mock_new_end_user

        # Test the logic
        existing_user = mock_db.session.query().filter().first()
        if existing_user is None:
            new_user = mock_end_user_class(
                tenant_id="tenant_123",
                app_id="app_123",
                type="service_api",
                is_anonymous=False,
                session_id="user_123"
            )
            mock_db.session.add(new_user)
            mock_db.session.commit()

        # Verify
        assert existing_user is None
        mock_end_user_class.assert_called_once_with(
            tenant_id="tenant_123",
            app_id="app_123",
            type="service_api",
            is_anonymous=False,
            session_id="user_123"
        )
        mock_db.session.add.assert_called_once()
        mock_db.session.commit.assert_called_once()

    def test_create_or_update_end_user_default_user_logic(self):
        """Test create_or_update_end_user_for_user_id logic with default user"""
        # Test default user logic
        user_id = None
        if not user_id:
            user_id = "DEFAULT-USER"

        assert user_id == "DEFAULT-USER"

        # Test anonymous flag logic
        is_anonymous = user_id == "DEFAULT-USER"
        assert is_anonymous is True


class TestValidationLogic:
    """Test cases for validation logic"""

    def test_auth_header_validation_logic(self):
        """Test authorization header validation logic"""
        # Test missing header
        auth_header = None
        if auth_header is None or " " not in auth_header:
            error_msg = "Authorization header must be provided and start with 'Bearer'"
        else:
            error_msg = None

        assert error_msg == "Authorization header must be provided and start with 'Bearer'"

        # Test invalid format
        auth_header = "InvalidHeader"
        if auth_header is None or " " not in auth_header:
            error_msg = "Authorization header must be provided and start with 'Bearer'"
        else:
            error_msg = None

        assert error_msg == "Authorization header must be provided and start with 'Bearer'"

        # Test valid format
        auth_header = "Bearer token123"
        if auth_header is None or " " not in auth_header:
            error_msg = "Authorization header must be provided and start with 'Bearer'"
        else:
            auth_scheme, auth_token = auth_header.split(None, 1)
            auth_scheme = auth_scheme.lower()
            if auth_scheme != "bearer":
                error_msg = "Authorization scheme must be 'Bearer'"
            else:
                error_msg = None

        assert error_msg is None
        assert auth_token == "token123"

    def test_app_status_validation_logic(self):
        """Test app status validation logic"""
        # Test normal status
        app_status = "normal"
        enable_api = True

        if app_status != "normal":
            error_msg = "The app's status is abnormal."
        elif not enable_api:
            error_msg = "The app's API service has been disabled."
        else:
            error_msg = None

        assert error_msg is None

        # Test abnormal status
        app_status = "disabled"
        if app_status != "normal":
            error_msg = "The app's status is abnormal."
        else:
            error_msg = None

        assert error_msg == "The app's status is abnormal."

        # Test API disabled
        app_status = "normal"
        enable_api = False

        if app_status != "normal":
            error_msg = "The app's status is abnormal."
        elif not enable_api:
            error_msg = "The app's API service has been disabled."
        else:
            error_msg = None

        assert error_msg == "The app's API service has been disabled."

    def test_billing_limit_validation_logic(self):
        """Test billing limit validation logic"""
        # Test members limit exceeded
        billing_enabled = True
        members_limit = 5
        members_size = 5

        if billing_enabled and 0 < members_limit <= members_size:
            error_msg = "The number of members has reached the limit of your subscription."
        else:
            error_msg = None

        assert error_msg == "The number of members has reached the limit of your subscription."

        # Test within limits
        members_size = 3
        if billing_enabled and 0 < members_limit <= members_size:
            error_msg = "The number of members has reached the limit of your subscription."
        else:
            error_msg = None

        assert error_msg is None

        # Test billing disabled
        billing_enabled = False
        members_size = 10
        if billing_enabled and 0 < members_limit <= members_size:
            error_msg = "The number of members has reached the limit of your subscription."
        else:
            error_msg = None

        assert error_msg is None

    def test_knowledge_limit_validation_logic(self):
        """Test knowledge limit validation logic"""
        # Test sandbox plan
        billing_enabled = True
        subscription_plan = "sandbox"
        resource = "add_segment"

        if billing_enabled and resource == "add_segment" and subscription_plan == "sandbox":
            error_msg = "To unlock this feature and elevate your Dify experience, please upgrade to a paid plan."
        else:
            error_msg = None

        assert error_msg == "To unlock this feature and elevate your Dify experience, please upgrade to a paid plan."

        # Test paid plan
        subscription_plan = "professional"
        if billing_enabled and resource == "add_segment" and subscription_plan == "sandbox":
            error_msg = "To unlock this feature and elevate your Dify experience, please upgrade to a paid plan."
        else:
            error_msg = None

        assert error_msg is None

    def test_rate_limit_validation_logic(self):
        """Test rate limit validation logic"""
        # Test rate limit exceeded
        rate_limit_enabled = True
        rate_limit = 10
        request_count = 15

        if rate_limit_enabled and request_count > rate_limit:
            error_msg = "Sorry, you have reached the knowledge base request rate limit of your subscription."
        else:
            error_msg = None

        assert error_msg == "Sorry, you have reached the knowledge base request rate limit of your subscription."

        # Test within limits
        request_count = 5
        if rate_limit_enabled and request_count > rate_limit:
            error_msg = "Sorry, you have reached the knowledge base request rate limit of your subscription."
        else:
            error_msg = None

        assert error_msg is None

        # Test rate limiting disabled
        rate_limit_enabled = False
        request_count = 20
        if rate_limit_enabled and request_count > rate_limit:
            error_msg = "Sorry, you have reached the knowledge base request rate limit of your subscription."
        else:
            error_msg = None

        assert error_msg is None


class TestDatasetApiResource:
    """Test cases for DatasetApiResource class"""

    def test_dataset_api_resource_has_method_decorators(self):
        """Test DatasetApiResource has method_decorators attribute"""
        assert hasattr(DatasetApiResource, 'method_decorators')
        assert isinstance(DatasetApiResource.method_decorators, list)


class TestStudioTaskValidationLogic:
    """Test cases for studio task validation logic"""

    def test_async_parameter_validation_logic(self):
        """Test async parameter validation logic"""
        # Test valid async values
        asyn_values = ["0", "1"]
        for asyn in asyn_values:
            bool_asyn_value = asyn in ["1", "true", "True"]
            assert isinstance(bool_asyn_value, bool)

        # Test invalid async value
        asyn = "invalid"
        if asyn not in ["0", "1"]:
            error_msg = "asyn参数必须是0或1"
        else:
            error_msg = None

        assert error_msg == "asyn参数必须是0或1"

    def test_routing_key_validation_logic(self):
        """Test routing key validation logic for async requests"""
        # Test async request without routing key
        bool_asyn_value = True
        routing_key = None

        if bool_asyn_value and not routing_key:
            error_msg = "异步接口缺少routing_key参数"
        else:
            error_msg = None

        assert error_msg == "异步接口缺少routing_key参数"

        # Test async request with routing key
        routing_key = "test.routing.key"
        if bool_asyn_value and not routing_key:
            error_msg = "异步接口缺少routing_key参数"
        else:
            error_msg = None

        assert error_msg is None

        # Test sync request without routing key (should be OK)
        bool_asyn_value = False
        routing_key = None
        if bool_asyn_value and not routing_key:
            error_msg = "异步接口缺少routing_key参数"
        else:
            error_msg = None

        assert error_msg is None

    def test_required_parameters_validation_logic(self):
        """Test required parameters validation logic"""
        # Test missing task_name
        task_name = None
        if not task_name:
            error_msg = "缺少task_name参数"
        else:
            error_msg = None

        assert error_msg == "缺少task_name参数"

        # Test missing request_id
        request_id = None
        if not request_id:
            error_msg = "缺少request_id参数"
        else:
            error_msg = None

        assert error_msg == "缺少request_id参数"

        # Test missing biz_name
        biz_name = None
        if not biz_name:
            error_msg = "缺少biz_name参数"
        else:
            error_msg = None

        assert error_msg == "缺少biz_name参数"

        # Test all parameters present
        task_name = "test_task"
        request_id = "req_123"
        biz_name = "test_biz"

        errors = []
        if not task_name:
            errors.append("缺少task_name参数")
        if not request_id:
            errors.append("缺少request_id参数")
        if not biz_name:
            errors.append("缺少biz_name参数")

        assert len(errors) == 0


class TestValidateAndGetApiTokenLogic:
    """Test cases for validate_and_get_api_token logic without Flask context"""

    def test_validate_and_get_api_token_header_parsing_logic(self):
        """Test authorization header parsing logic"""
        # Test missing header
        auth_header = None
        if auth_header is None or " " not in auth_header:
            error_msg = "Authorization header must be provided and start with 'Bearer'"
        else:
            error_msg = None
        assert error_msg == "Authorization header must be provided and start with 'Bearer'"

        # Test invalid format
        auth_header = "InvalidHeader"
        if auth_header is None or " " not in auth_header:
            error_msg = "Authorization header must be provided and start with 'Bearer'"
        else:
            error_msg = None
        assert error_msg == "Authorization header must be provided and start with 'Bearer'"

        # Test valid format
        auth_header = "Bearer token123"
        if auth_header is None or " " not in auth_header:
            error_msg = "Authorization header must be provided and start with 'Bearer'"
        else:
            auth_scheme, auth_token = auth_header.split(None, 1)
            auth_scheme = auth_scheme.lower()
            if auth_scheme != "bearer":
                error_msg = "Authorization scheme must be 'Bearer'"
            else:
                error_msg = None

        assert error_msg is None
        assert auth_token == "token123"

    def test_validate_and_get_api_token_database_logic(self):
        """Test database query logic for API token validation"""
        # Test successful update scenario
        rowcount = 1
        if rowcount > 0:
            # Token was updated, query for it
            token_found_after_update = True
            commit_needed = True
        else:
            # No update, try to find existing token
            token_found_after_update = False
            commit_needed = False

        assert token_found_after_update is True
        assert commit_needed is True

        # Test no update scenario with existing token
        rowcount = 0
        existing_token = Mock()
        if rowcount > 0:
            token_found_after_update = True
            commit_needed = True
        else:
            if existing_token:
                token_found_after_update = True
                commit_needed = False
            else:
                token_found_after_update = False
                commit_needed = False

        assert token_found_after_update is True
        assert commit_needed is False

        # Test token not found scenario
        rowcount = 0
        existing_token = None
        if rowcount > 0:
            token_found_after_update = True
        else:
            if existing_token:
                token_found_after_update = True
            else:
                token_found_after_update = False
                error_msg = "Access token is invalid"

        assert token_found_after_update is False
        assert error_msg == "Access token is invalid"


class TestCreateOrUpdateEndUserFunction:
    """Test cases for create_or_update_end_user_for_user_id function"""

    @patch('controllers.service_api.wraps.db')
    def test_create_or_update_end_user_existing_user(self, mock_db):
        """Test create_or_update_end_user_for_user_id with existing user"""
        # Setup
        mock_app = Mock()
        mock_app.tenant_id = "tenant_123"
        mock_app.id = "app_123"

        mock_end_user = Mock()
        mock_db.session.query.return_value.filter.return_value.first.return_value = mock_end_user

        # Execute
        result = create_or_update_end_user_for_user_id(mock_app, "user_123")

        # Verify
        assert result == mock_end_user
        mock_db.session.add.assert_not_called()
        mock_db.session.commit.assert_not_called()

    @patch('controllers.service_api.wraps.db')
    @patch('controllers.service_api.wraps.EndUser')
    def test_create_or_update_end_user_new_user(self, mock_end_user_class, mock_db):
        """Test create_or_update_end_user_for_user_id with new user"""
        # Setup
        mock_app = Mock()
        mock_app.tenant_id = "tenant_123"
        mock_app.id = "app_123"

        mock_db.session.query.return_value.filter.return_value.first.return_value = None

        mock_new_end_user = Mock()
        mock_end_user_class.return_value = mock_new_end_user

        # Execute
        result = create_or_update_end_user_for_user_id(mock_app, "user_123")

        # Verify
        assert result == mock_new_end_user
        mock_end_user_class.assert_called_once_with(
            tenant_id="tenant_123",
            app_id="app_123",
            type="service_api",
            is_anonymous=False,
            session_id="user_123"
        )
        mock_db.session.add.assert_called_once_with(mock_new_end_user)
        mock_db.session.commit.assert_called_once()

    @patch('controllers.service_api.wraps.db')
    @patch('controllers.service_api.wraps.EndUser')
    def test_create_or_update_end_user_default_user(self, mock_end_user_class, mock_db):
        """Test create_or_update_end_user_for_user_id with default user"""
        # Setup
        mock_app = Mock()
        mock_app.tenant_id = "tenant_123"
        mock_app.id = "app_123"

        mock_db.session.query.return_value.filter.return_value.first.return_value = None

        mock_new_end_user = Mock()
        mock_end_user_class.return_value = mock_new_end_user

        # Execute with None user_id
        result = create_or_update_end_user_for_user_id(mock_app, None)

        # Verify
        assert result == mock_new_end_user
        mock_end_user_class.assert_called_once_with(
            tenant_id="tenant_123",
            app_id="app_123",
            type="service_api",
            is_anonymous=True,
            session_id="DEFAULT-USER"
        )


class TestGetAppByTaskNameLogic:
    """Test cases for get_app_by_task_name logic"""

    def test_get_app_by_task_name_logic(self):
        """Test get_app_by_task_name logic without Flask context"""
        # Test with valid task_name
        data = {"task_name": "test_task"}
        task_name = data.get("task_name")
        if task_name:
            result = "app_found"
        else:
            result = None
            warning_logged = True

        assert result == "app_found"

        # Test with missing task_name
        data = {}
        task_name = data.get("task_name")
        if task_name:
            result = "app_found"
        else:
            result = None
            warning_logged = True

        assert result is None
        assert warning_logged is True

        # Test with None task_name
        data = {"task_name": None}
        task_name = data.get("task_name")
        if task_name:
            result = "app_found"
        else:
            result = None
            warning_logged = True

        assert result is None
        assert warning_logged is True


class TestDecoratorFunctions:
    """Test cases for decorator functions with mocked dependencies"""

    def test_validate_app_token_decorator_usage(self):
        """Test validate_app_token decorator can be used with and without arguments"""
        # Test decorator without arguments
        @validate_app_token
        def test_view1():
            return "success"

        assert callable(test_view1)

        # Test decorator with arguments
        fetch_user_arg = FetchUserArg(fetch_from=WhereisUserArg.QUERY, required=True)

        @validate_app_token(fetch_user_arg=fetch_user_arg)
        def test_view2():
            return "success"

        assert callable(test_view2)

    def test_cloud_edition_billing_resource_check_decorator_usage(self):
        """Test cloud_edition_billing_resource_check decorator usage"""
        @cloud_edition_billing_resource_check("members", "app")
        def test_view():
            return "success"

        assert callable(test_view)

    def test_cloud_edition_billing_knowledge_limit_check_decorator_usage(self):
        """Test cloud_edition_billing_knowledge_limit_check decorator usage"""
        @cloud_edition_billing_knowledge_limit_check("add_segment", "app")
        def test_view():
            return "success"

        assert callable(test_view)

    def test_cloud_edition_billing_rate_limit_check_decorator_usage(self):
        """Test cloud_edition_billing_rate_limit_check decorator usage"""
        @cloud_edition_billing_rate_limit_check("knowledge", "app")
        def test_view():
            return "success"

        assert callable(test_view)

    def test_validate_dataset_token_decorator_usage(self):
        """Test validate_dataset_token decorator usage"""
        # Test decorator without arguments
        @validate_dataset_token
        def test_view1():
            return "success"

        assert callable(test_view1)

        # Test decorator with view argument
        def test_view2():
            return "success"

        decorated_view = validate_dataset_token(test_view2)
        assert callable(decorated_view)

    def test_studio_task_validate_decorator_usage(self):
        """Test studio_task_validate_decorator usage"""
        # Test decorator without arguments
        @studio_task_validate_decorator
        def test_view1():
            return "success"

        assert callable(test_view1)

        # Test decorator with arguments
        fetch_user_arg = FetchUserArg(fetch_from=WhereisUserArg.JSON, required=False)

        @studio_task_validate_decorator(fetch_user_arg=fetch_user_arg)
        def test_view2():
            return "success"

        assert callable(test_view2)


class TestBillingLogicFunctions:
    """Test cases for billing logic functions with mocked dependencies"""

    @patch('controllers.service_api.wraps.validate_and_get_api_token')
    @patch('controllers.service_api.wraps.FeatureService')
    def test_billing_resource_check_vector_space_limit(self, mock_feature_service, mock_validate_token):
        """Test billing resource check for vector space limit"""
        # Setup
        mock_api_token = Mock()
        mock_api_token.tenant_id = "tenant_123"
        mock_validate_token.return_value = mock_api_token

        mock_features = Mock()
        mock_features.billing.enabled = True
        mock_features.vector_space.limit = 100
        mock_features.vector_space.size = 100
        mock_feature_service.get_features.return_value = mock_features

        # Create test view function
        @cloud_edition_billing_resource_check("vector_space", "app")
        def test_view():
            return {"success": True}

        # Execute and verify
        with pytest.raises(Forbidden, match="The capacity of the vector space has reached the limit"):
            test_view()

    @patch('controllers.service_api.wraps.validate_and_get_api_token')
    @patch('controllers.service_api.wraps.FeatureService')
    def test_billing_resource_check_documents_limit(self, mock_feature_service, mock_validate_token):
        """Test billing resource check for documents limit"""
        # Setup
        mock_api_token = Mock()
        mock_api_token.tenant_id = "tenant_123"
        mock_validate_token.return_value = mock_api_token

        mock_features = Mock()
        mock_features.billing.enabled = True
        mock_features.documents_upload_quota.limit = 50
        mock_features.documents_upload_quota.size = 50
        mock_feature_service.get_features.return_value = mock_features

        # Create test view function
        @cloud_edition_billing_resource_check("documents", "app")
        def test_view():
            return {"success": True}

        # Execute and verify
        with pytest.raises(Forbidden, match="The number of documents has reached the limit"):
            test_view()

    @patch('controllers.service_api.wraps.validate_and_get_api_token')
    @patch('controllers.service_api.wraps.FeatureService')
    def test_billing_resource_check_unknown_resource(self, mock_feature_service, mock_validate_token):
        """Test billing resource check for unknown resource type"""
        # Setup
        mock_api_token = Mock()
        mock_api_token.tenant_id = "tenant_123"
        mock_validate_token.return_value = mock_api_token

        mock_features = Mock()
        mock_features.billing.enabled = True
        mock_feature_service.get_features.return_value = mock_features

        # Create test view function
        @cloud_edition_billing_resource_check("unknown_resource", "app")
        def test_view():
            return {"success": True}

        # Execute - should pass through for unknown resource
        result = test_view()
        assert result["success"] is True

    @patch('controllers.service_api.wraps.validate_and_get_api_token')
    @patch('controllers.service_api.wraps.FeatureService')
    def test_knowledge_limit_check_unknown_resource(self, mock_feature_service, mock_validate_token):
        """Test knowledge limit check for unknown resource type"""
        # Setup
        mock_api_token = Mock()
        mock_api_token.tenant_id = "tenant_123"
        mock_validate_token.return_value = mock_api_token

        mock_features = Mock()
        mock_features.billing.enabled = True
        mock_feature_service.get_features.return_value = mock_features

        # Create test view function
        @cloud_edition_billing_knowledge_limit_check("unknown_resource", "app")
        def test_view():
            return {"success": True}

        # Execute - should pass through for unknown resource
        result = test_view()
        assert result["success"] is True

    @patch('controllers.service_api.wraps.validate_and_get_api_token')
    @patch('controllers.service_api.wraps.FeatureService')
    def test_rate_limit_check_unknown_resource(self, mock_feature_service, mock_validate_token):
        """Test rate limit check for unknown resource type"""
        # Setup
        mock_api_token = Mock()
        mock_api_token.tenant_id = "tenant_123"
        mock_validate_token.return_value = mock_api_token

        mock_features = Mock()
        mock_feature_service.get_features.return_value = mock_features

        # Create test view function
        @cloud_edition_billing_rate_limit_check("unknown_resource", "app")
        def test_view():
            return {"success": True}

        # Execute - should pass through for unknown resource
        result = test_view()
        assert result["success"] is True

    def test_rate_limit_check_logic(self):
        """Test rate limit check logic without Redis dependency"""
        # Test within limits
        rate_limit_enabled = True
        rate_limit = 10
        request_count = 5

        if rate_limit_enabled and request_count > rate_limit:
            error_msg = "Sorry, you have reached the knowledge base request rate limit of your subscription."
            log_created = True
        else:
            error_msg = None
            log_created = False

        assert error_msg is None
        assert log_created is False

        # Test limit exceeded
        request_count = 15
        if rate_limit_enabled and request_count > rate_limit:
            error_msg = "Sorry, you have reached the knowledge base request rate limit of your subscription."
            log_created = True
        else:
            error_msg = None
            log_created = False

        assert error_msg == "Sorry, you have reached the knowledge base request rate limit of your subscription."
        assert log_created is True

        # Test rate limiting disabled
        rate_limit_enabled = False
        request_count = 20
        if rate_limit_enabled and request_count > rate_limit:
            error_msg = "Sorry, you have reached the knowledge base request rate limit of your subscription."
            log_created = True
        else:
            error_msg = None
            log_created = False

        assert error_msg is None
        assert log_created is False


class TestActualFunctionExecution:
    """Test cases that actually execute functions to improve coverage"""

    @patch('controllers.service_api.wraps.db')
    @patch('controllers.service_api.wraps.EndUser')
    def test_create_or_update_end_user_actual_execution(self, mock_end_user_class, mock_db):
        """Test actual execution of create_or_update_end_user_for_user_id"""
        # Setup
        mock_app = Mock()
        mock_app.tenant_id = "tenant_123"
        mock_app.id = "app_123"

        # Test existing user path
        mock_existing_user = Mock()
        mock_db.session.query.return_value.filter.return_value.first.return_value = mock_existing_user

        result = create_or_update_end_user_for_user_id(mock_app, "user_123")

        assert result == mock_existing_user
        mock_db.session.add.assert_not_called()
        mock_db.session.commit.assert_not_called()

        # Test new user path
        mock_db.session.query.return_value.filter.return_value.first.return_value = None
        mock_new_user = Mock()
        mock_end_user_class.return_value = mock_new_user

        result = create_or_update_end_user_for_user_id(mock_app, "user_456")

        assert result == mock_new_user
        mock_end_user_class.assert_called_with(
            tenant_id="tenant_123",
            app_id="app_123",
            type="service_api",
            is_anonymous=False,
            session_id="user_456"
        )
        mock_db.session.add.assert_called_with(mock_new_user)
        mock_db.session.commit.assert_called()

    def test_get_app_by_task_name_logic_extended(self):
        """Test extended logic for get_app_by_task_name"""
        # Test with valid task_name and app found
        data = {"task_name": "test_task"}
        task_name = data.get("task_name")
        if task_name:
            # Simulate database query
            app_found = True
            result = "mock_app"
        else:
            app_found = False
            result = None
            warning_logged = True

        assert app_found is True
        assert result == "mock_app"

        # Test with valid task_name but no app found
        data = {"task_name": "nonexistent_task"}
        task_name = data.get("task_name")
        if task_name:
            # Simulate database query returning None
            app_found = False
            result = None
        else:
            app_found = False
            result = None
            warning_logged = True

        assert app_found is False
        assert result is None

    def test_whereis_user_arg_enum_actual_usage(self):
        """Test actual usage of WhereisUserArg enum"""
        # Test all enum values
        assert WhereisUserArg.QUERY.value == "query"
        assert WhereisUserArg.JSON.value == "json"
        assert WhereisUserArg.FORM.value == "form"

        # Test enum comparison
        assert WhereisUserArg.QUERY != WhereisUserArg.JSON
        assert WhereisUserArg.JSON != WhereisUserArg.FORM

        # Test enum in list
        all_values = [WhereisUserArg.QUERY, WhereisUserArg.JSON, WhereisUserArg.FORM]
        assert len(all_values) == 3
        assert WhereisUserArg.QUERY in all_values

    def test_fetch_user_arg_model_actual_usage(self):
        """Test actual usage of FetchUserArg model"""
        # Test with required=True
        fetch_arg1 = FetchUserArg(fetch_from=WhereisUserArg.QUERY, required=True)
        assert fetch_arg1.fetch_from == WhereisUserArg.QUERY
        assert fetch_arg1.required is True

        # Test with default required=False
        fetch_arg2 = FetchUserArg(fetch_from=WhereisUserArg.JSON)
        assert fetch_arg2.fetch_from == WhereisUserArg.JSON
        assert fetch_arg2.required is False

        # Test with explicit required=False
        fetch_arg3 = FetchUserArg(fetch_from=WhereisUserArg.FORM, required=False)
        assert fetch_arg3.fetch_from == WhereisUserArg.FORM
        assert fetch_arg3.required is False

        # Test different fetch_from values
        for fetch_from in [WhereisUserArg.QUERY, WhereisUserArg.JSON, WhereisUserArg.FORM]:
            fetch_arg = FetchUserArg(fetch_from=fetch_from)
            assert fetch_arg.fetch_from == fetch_from
