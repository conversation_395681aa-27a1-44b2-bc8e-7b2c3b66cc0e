import pytest
import time
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Mock all the problematic imports before importing the target module
sys.modules['configs'] = Mock()
sys.modules['configs.dify_config'] = Mock()
sys.modules['extensions.ext_database'] = Mock()
sys.modules['extensions.ext_redis'] = Mock()
sys.modules['libs.login'] = Mock()
sys.modules['models.account'] = Mock()
sys.modules['models.dataset'] = Mock()
sys.modules['models.model'] = Mock()
sys.modules['services.feature_service'] = Mock()
sys.modules['controllers.service_api.app.error'] = Mock()

# Create mock classes for the models
class MockTenantStatus:
    NORMAL = "normal"

class MockApiToken:
    def __init__(self):
        self.token = "test_token"
        self.type = "app"
        self.app_id = "app_123"
        self.tenant_id = "tenant_123"

class MockApp:
    def __init__(self):
        self.id = "app_123"
        self.status = "normal"
        self.enable_api = True
        self.tenant_id = "tenant_123"

class MockTenant:
    def __init__(self):
        self.id = "tenant_123"
        self.status = MockTenantStatus.NORMAL

class MockEndUser:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

class MockAccount:
    def __init__(self):
        self.id = "account_123"

class MockTenantAccountJoin:
    def __init__(self):
        self.account_id = "account_123"

class MockRateLimitLog:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

class MockAppUnavailableError(Exception):
    pass

# Set up the mocks
sys.modules['models.account'].TenantStatus = MockTenantStatus
sys.modules['models.account'].Tenant = MockTenant
sys.modules['models.account'].Account = MockAccount
sys.modules['models.account'].TenantAccountJoin = MockTenantAccountJoin
sys.modules['models.model'].ApiToken = MockApiToken
sys.modules['models.model'].App = MockApp
sys.modules['models.model'].EndUser = MockEndUser
sys.modules['models.dataset'].RateLimitLog = MockRateLimitLog
sys.modules['controllers.service_api.app.error'].AppUnavailableError = MockAppUnavailableError

from werkzeug.exceptions import Forbidden, Unauthorized


class TestWrapsWithFlaskMocks:
    """Test cases that actually execute wraps.py code with mocked Flask dependencies"""

    @patch('flask.request')
    @patch('sqlalchemy.orm.Session')
    @patch('extensions.ext_database.db')
    def test_validate_and_get_api_token_with_flask_mocks(self, mock_db, mock_session_class, mock_request):
        """Test validate_and_get_api_token with mocked Flask dependencies"""
        # Import after mocking
        from controllers.service_api.wraps import validate_and_get_api_token
        
        # Setup request mock
        mock_request.headers.get.return_value = "Bearer valid_token"
        
        # Setup session mock
        mock_session = Mock()
        mock_session_class.return_value.__enter__.return_value = mock_session
        mock_session_class.return_value.__exit__.return_value = None
        
        # Setup database update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        # Setup API token query result
        mock_api_token = MockApiToken()
        mock_session.query.return_value.filter.return_value.first.return_value = mock_api_token
        
        # Mock datetime
        with patch('controllers.service_api.wraps.datetime') as mock_datetime:
            mock_datetime.now.return_value = datetime(2023, 1, 1, 12, 0, 0)
            
            # Execute the function
            result = validate_and_get_api_token("app")
            
            # Verify
            assert result == mock_api_token
            mock_session.commit.assert_called_once()

    @patch('flask.request')
    @patch('sqlalchemy.orm.Session')
    @patch('extensions.ext_database.db')
    def test_validate_and_get_api_token_no_update_needed(self, mock_db, mock_session_class, mock_request):
        """Test validate_and_get_api_token when no update is needed"""
        # Import after mocking
        from controllers.service_api.wraps import validate_and_get_api_token
        
        # Setup request mock
        mock_request.headers.get.return_value = "Bearer valid_token"
        
        # Setup session mock
        mock_session = Mock()
        mock_session_class.return_value.__enter__.return_value = mock_session
        mock_session_class.return_value.__exit__.return_value = None
        
        # Setup database update result (no rows updated)
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result
        
        # Setup API token scalar result
        mock_api_token = MockApiToken()
        mock_session.scalar.return_value = mock_api_token
        
        # Mock datetime
        with patch('controllers.service_api.wraps.datetime') as mock_datetime:
            mock_datetime.now.return_value = datetime(2023, 1, 1, 12, 0, 0)
            
            # Execute the function
            result = validate_and_get_api_token("app")
            
            # Verify
            assert result == mock_api_token
            mock_session.commit.assert_not_called()

    @patch('flask.request')
    def test_validate_and_get_api_token_missing_header(self, mock_request):
        """Test validate_and_get_api_token with missing Authorization header"""
        # Import after mocking
        from controllers.service_api.wraps import validate_and_get_api_token
        
        # Setup request mock
        mock_request.headers.get.return_value = None
        
        # Execute and verify exception
        with pytest.raises(Unauthorized, match="Authorization header must be provided"):
            validate_and_get_api_token("app")

    @patch('flask.request')
    def test_validate_and_get_api_token_invalid_scheme(self, mock_request):
        """Test validate_and_get_api_token with invalid auth scheme"""
        # Import after mocking
        from controllers.service_api.wraps import validate_and_get_api_token
        
        # Setup request mock
        mock_request.headers.get.return_value = "Basic token123"
        
        # Execute and verify exception
        with pytest.raises(Unauthorized, match="Authorization scheme must be 'Bearer'"):
            validate_and_get_api_token("app")

    @patch('extensions.ext_database.db')
    def test_create_or_update_end_user_existing_user(self, mock_db):
        """Test create_or_update_end_user_for_user_id with existing user"""
        # Import after mocking
        from controllers.service_api.wraps import create_or_update_end_user_for_user_id
        
        # Setup app mock
        mock_app = MockApp()
        
        # Setup existing user
        mock_existing_user = MockEndUser(id="user_123")
        mock_db.session.query.return_value.filter.return_value.first.return_value = mock_existing_user
        
        # Execute
        result = create_or_update_end_user_for_user_id(mock_app, "user_123")
        
        # Verify
        assert result == mock_existing_user
        mock_db.session.add.assert_not_called()
        mock_db.session.commit.assert_not_called()

    @patch('extensions.ext_database.db')
    def test_create_or_update_end_user_new_user(self, mock_db):
        """Test create_or_update_end_user_for_user_id with new user"""
        # Import after mocking
        from controllers.service_api.wraps import create_or_update_end_user_for_user_id
        
        # Setup app mock
        mock_app = MockApp()
        
        # Setup no existing user
        mock_db.session.query.return_value.filter.return_value.first.return_value = None
        
        # Mock EndUser class
        with patch('controllers.service_api.wraps.EndUser') as mock_end_user_class:
            mock_new_user = MockEndUser(id="new_user")
            mock_end_user_class.return_value = mock_new_user
            
            # Execute
            result = create_or_update_end_user_for_user_id(mock_app, "user_123")
            
            # Verify
            assert result == mock_new_user
            mock_end_user_class.assert_called_once_with(
                tenant_id="app_123",
                app_id="app_123",
                type="service_api",
                is_anonymous=False,
                session_id="user_123"
            )
            mock_db.session.add.assert_called_once_with(mock_new_user)
            mock_db.session.commit.assert_called_once()

    @patch('flask.request')
    @patch('extensions.ext_database.db')
    @patch('logging.warning')
    def test_get_app_by_task_name_success(self, mock_logging, mock_db, mock_request):
        """Test get_app_by_task_name with valid task_name"""
        # Import after mocking
        from controllers.service_api.wraps import get_app_by_task_name
        
        # Setup request mock
        mock_request.get_json.return_value = {"task_name": "test_task"}
        
        # Setup app query result
        mock_app = MockApp()
        mock_db.session.query.return_value.filter.return_value.first.return_value = mock_app
        
        # Execute
        result = get_app_by_task_name()
        
        # Verify
        assert result == mock_app
        mock_logging.assert_not_called()

    @patch('flask.request')
    @patch('logging.warning')
    def test_get_app_by_task_name_missing_task_name(self, mock_logging, mock_request):
        """Test get_app_by_task_name with missing task_name"""
        # Import after mocking
        from controllers.service_api.wraps import get_app_by_task_name
        
        # Setup request mock
        mock_request.get_json.return_value = {}
        
        # Execute
        result = get_app_by_task_name()
        
        # Verify
        assert result is None
        mock_logging.assert_called_once_with("当前请求中没有提供task_name")

    def test_whereis_user_arg_enum_execution(self):
        """Test WhereisUserArg enum actual execution"""
        # Import after mocking
        from controllers.service_api.wraps import WhereisUserArg
        
        # Test enum values
        assert WhereisUserArg.QUERY.value == "query"
        assert WhereisUserArg.JSON.value == "json"
        assert WhereisUserArg.FORM.value == "form"
        
        # Test enum comparison
        assert WhereisUserArg.QUERY != WhereisUserArg.JSON
        
        # Test enum in collections
        all_values = list(WhereisUserArg)
        assert len(all_values) == 3
        assert WhereisUserArg.QUERY in all_values

    def test_fetch_user_arg_model_execution(self):
        """Test FetchUserArg model actual execution"""
        # Import after mocking
        from controllers.service_api.wraps import FetchUserArg, WhereisUserArg
        
        # Test model creation with required=True
        fetch_arg1 = FetchUserArg(fetch_from=WhereisUserArg.QUERY, required=True)
        assert fetch_arg1.fetch_from == WhereisUserArg.QUERY
        assert fetch_arg1.required is True
        
        # Test model creation with default required=False
        fetch_arg2 = FetchUserArg(fetch_from=WhereisUserArg.JSON)
        assert fetch_arg2.fetch_from == WhereisUserArg.JSON
        assert fetch_arg2.required is False
        
        # Test model validation
        fetch_arg3 = FetchUserArg(fetch_from=WhereisUserArg.FORM, required=False)
        assert fetch_arg3.fetch_from == WhereisUserArg.FORM
        assert fetch_arg3.required is False

    @patch('flask.request')
    @patch('flask.current_app')
    @patch('flask_login.user_logged_in')
    @patch('libs.login._get_user')
    @patch('extensions.ext_database.db')
    def test_validate_app_token_decorator_execution(self, mock_db, mock_get_user, mock_user_logged_in, mock_current_app, mock_request):
        """Test validate_app_token decorator actual execution"""
        # Import after mocking
        from controllers.service_api.wraps import validate_app_token, validate_and_get_api_token

        # Setup mocks
        mock_api_token = MockApiToken()
        mock_app = MockApp()
        mock_tenant = MockTenant()
        mock_account = MockAccount()
        mock_ta = MockTenantAccountJoin()

        # Setup database queries
        mock_db.session.query.side_effect = [
            Mock(filter=Mock(return_value=Mock(first=Mock(return_value=mock_app)))),
            Mock(filter=Mock(return_value=Mock(first=Mock(return_value=mock_tenant)))),
            Mock(filter=Mock(return_value=Mock(filter=Mock(return_value=Mock(
                filter=Mock(return_value=Mock(filter=Mock(return_value=Mock(
                    one_or_none=Mock(return_value=(mock_tenant, mock_ta))
                ))))
            ))))),
            Mock(filter=Mock(return_value=Mock(first=Mock(return_value=mock_account))))
        ]

        # Mock validate_and_get_api_token
        with patch('controllers.service_api.wraps.validate_and_get_api_token') as mock_validate_token:
            mock_validate_token.return_value = mock_api_token

            # Create test view function
            @validate_app_token
            def test_view(*args, **kwargs):
                return {"success": True, "app_model": kwargs.get("app_model")}

            # Execute
            result = test_view()

            # Verify
            assert result["success"] is True
            assert result["app_model"] == mock_app

    @patch('flask.request')
    def test_validate_app_token_decorator_app_not_found(self, mock_request):
        """Test validate_app_token decorator when app is not found"""
        # Import after mocking
        from controllers.service_api.wraps import validate_app_token

        # Setup mocks
        mock_api_token = MockApiToken()

        with patch('controllers.service_api.wraps.validate_and_get_api_token') as mock_validate_token, \
             patch('extensions.ext_database.db') as mock_db:

            mock_validate_token.return_value = mock_api_token
            mock_db.session.query.return_value.filter.return_value.first.return_value = None

            # Create test view function
            @validate_app_token
            def test_view(*args, **kwargs):
                return {"success": True}

            # Execute and verify
            with pytest.raises(Forbidden, match="The app no longer exists"):
                test_view()

    @patch('flask.request')
    def test_validate_app_token_decorator_app_abnormal_status(self, mock_request):
        """Test validate_app_token decorator when app status is abnormal"""
        # Import after mocking
        from controllers.service_api.wraps import validate_app_token

        # Setup mocks
        mock_api_token = MockApiToken()
        mock_app = MockApp()
        mock_app.status = "disabled"  # Abnormal status

        with patch('controllers.service_api.wraps.validate_and_get_api_token') as mock_validate_token, \
             patch('extensions.ext_database.db') as mock_db:

            mock_validate_token.return_value = mock_api_token
            mock_db.session.query.return_value.filter.return_value.first.return_value = mock_app

            # Create test view function
            @validate_app_token
            def test_view(*args, **kwargs):
                return {"success": True}

            # Execute and verify
            with pytest.raises(Forbidden, match="The app's status is abnormal"):
                test_view()

    @patch('flask.request')
    def test_validate_app_token_decorator_api_disabled(self, mock_request):
        """Test validate_app_token decorator when API is disabled"""
        # Import after mocking
        from controllers.service_api.wraps import validate_app_token

        # Setup mocks
        mock_api_token = MockApiToken()
        mock_app = MockApp()
        mock_app.enable_api = False  # API disabled

        with patch('controllers.service_api.wraps.validate_and_get_api_token') as mock_validate_token, \
             patch('extensions.ext_database.db') as mock_db:

            mock_validate_token.return_value = mock_api_token
            mock_db.session.query.return_value.filter.return_value.first.return_value = mock_app

            # Create test view function
            @validate_app_token
            def test_view(*args, **kwargs):
                return {"success": True}

            # Execute and verify
            with pytest.raises(Forbidden, match="The app's API service has been disabled"):
                test_view()

    def test_cloud_edition_billing_resource_check_decorator_creation(self):
        """Test cloud_edition_billing_resource_check decorator creation"""
        # Import after mocking
        from controllers.service_api.wraps import cloud_edition_billing_resource_check

        # Test decorator creation
        def dummy_view():
            return "test"

        decorated = cloud_edition_billing_resource_check("members", "app")(dummy_view)
        assert callable(decorated)

        # Test decorator with different resources
        resources = ["members", "apps", "vector_space", "documents"]
        for resource in resources:
            decorated = cloud_edition_billing_resource_check(resource, "app")(dummy_view)
            assert callable(decorated)

    def test_cloud_edition_billing_knowledge_limit_check_decorator_creation(self):
        """Test cloud_edition_billing_knowledge_limit_check decorator creation"""
        # Import after mocking
        from controllers.service_api.wraps import cloud_edition_billing_knowledge_limit_check

        # Test decorator creation
        def dummy_view():
            return "test"

        decorated = cloud_edition_billing_knowledge_limit_check("add_segment", "app")(dummy_view)
        assert callable(decorated)

        # Test decorator with different resources
        resources = ["add_segment", "upload_file", "create_dataset"]
        for resource in resources:
            decorated = cloud_edition_billing_knowledge_limit_check(resource, "app")(dummy_view)
            assert callable(decorated)

    def test_cloud_edition_billing_rate_limit_check_decorator_creation(self):
        """Test cloud_edition_billing_rate_limit_check decorator creation"""
        # Import after mocking
        from controllers.service_api.wraps import cloud_edition_billing_rate_limit_check

        # Test decorator creation
        def dummy_view():
            return "test"

        decorated = cloud_edition_billing_rate_limit_check("knowledge", "app")(dummy_view)
        assert callable(decorated)

        # Test decorator with different resources
        resources = ["knowledge", "chat", "completion"]
        for resource in resources:
            decorated = cloud_edition_billing_rate_limit_check(resource, "app")(dummy_view)
            assert callable(decorated)

    def test_validate_dataset_token_decorator_creation(self):
        """Test validate_dataset_token decorator creation"""
        # Import after mocking
        from controllers.service_api.wraps import validate_dataset_token

        # Test decorator creation
        def dummy_view():
            return "test"

        decorated = validate_dataset_token(dummy_view)
        assert callable(decorated)

    def test_studio_task_validate_decorator_creation(self):
        """Test studio_task_validate_decorator creation"""
        # Import after mocking
        from controllers.service_api.wraps import studio_task_validate_decorator, FetchUserArg, WhereisUserArg

        # Test decorator creation without arguments
        def dummy_view():
            return "test"

        decorated1 = studio_task_validate_decorator(dummy_view)
        assert callable(decorated1)

        # Test decorator creation with fetch_user_arg
        fetch_user_arg = FetchUserArg(fetch_from=WhereisUserArg.JSON, required=False)
        decorated2 = studio_task_validate_decorator(dummy_view, fetch_user_arg=fetch_user_arg)
        assert callable(decorated2)

    def test_dataset_api_resource_class_execution(self):
        """Test DatasetApiResource class execution"""
        # Import after mocking
        from controllers.service_api.wraps import DatasetApiResource, validate_dataset_token

        # Test class has method_decorators
        assert hasattr(DatasetApiResource, 'method_decorators')
        assert isinstance(DatasetApiResource.method_decorators, list)
        assert validate_dataset_token in DatasetApiResource.method_decorators

        # Test class inheritance
        from flask_restful import Resource
        assert issubclass(DatasetApiResource, Resource)

    @patch('services.feature_service.FeatureService')
    def test_billing_resource_check_members_limit_exceeded(self, mock_feature_service_class):
        """Test billing resource check when members limit is exceeded"""
        # Import after mocking
        from controllers.service_api.wraps import cloud_edition_billing_resource_check

        # Setup feature service mock
        mock_features = Mock()
        mock_features.billing.enabled = True
        mock_features.members.limit = 5
        mock_features.members.size = 5
        mock_feature_service_class.get_features.return_value = mock_features

        # Setup API token mock
        mock_api_token = MockApiToken()

        with patch('controllers.service_api.wraps.validate_and_get_api_token') as mock_validate_token:
            mock_validate_token.return_value = mock_api_token

            # Create test view function
            @cloud_edition_billing_resource_check("members", "app")
            def test_view():
                return {"success": True}

            # Execute and verify
            with pytest.raises(Forbidden, match="The number of members has reached the limit"):
                test_view()

    @patch('services.feature_service.FeatureService')
    def test_billing_resource_check_billing_disabled(self, mock_feature_service_class):
        """Test billing resource check when billing is disabled"""
        # Import after mocking
        from controllers.service_api.wraps import cloud_edition_billing_resource_check

        # Setup feature service mock
        mock_features = Mock()
        mock_features.billing.enabled = False
        mock_feature_service_class.get_features.return_value = mock_features

        # Setup API token mock
        mock_api_token = MockApiToken()

        with patch('controllers.service_api.wraps.validate_and_get_api_token') as mock_validate_token:
            mock_validate_token.return_value = mock_api_token

            # Create test view function
            @cloud_edition_billing_resource_check("members", "app")
            def test_view():
                return {"success": True}

            # Execute
            result = test_view()

            # Verify
            assert result["success"] is True

    @patch('services.feature_service.FeatureService')
    def test_knowledge_limit_check_sandbox_plan(self, mock_feature_service_class):
        """Test knowledge limit check for sandbox plan"""
        # Import after mocking
        from controllers.service_api.wraps import cloud_edition_billing_knowledge_limit_check

        # Setup feature service mock
        mock_features = Mock()
        mock_features.billing.enabled = True
        mock_features.billing.subscription.plan = "sandbox"
        mock_feature_service_class.get_features.return_value = mock_features

        # Setup API token mock
        mock_api_token = MockApiToken()

        with patch('controllers.service_api.wraps.validate_and_get_api_token') as mock_validate_token:
            mock_validate_token.return_value = mock_api_token

            # Create test view function
            @cloud_edition_billing_knowledge_limit_check("add_segment", "app")
            def test_view():
                return {"success": True}

            # Execute and verify
            with pytest.raises(Forbidden, match="To unlock this feature and elevate your Dify experience"):
                test_view()

    @patch('services.feature_service.FeatureService')
    @patch('extensions.ext_redis.redis_client')
    @patch('time.time')
    @patch('extensions.ext_database.db')
    def test_rate_limit_check_exceeded(self, mock_db, mock_time, mock_redis, mock_feature_service_class):
        """Test rate limit check when limit is exceeded"""
        # Import after mocking
        from controllers.service_api.wraps import cloud_edition_billing_rate_limit_check

        # Setup mocks
        mock_time.return_value = 1000.0
        mock_redis.zcard.return_value = 15  # Exceeds limit

        mock_rate_limit = Mock()
        mock_rate_limit.enabled = True
        mock_rate_limit.limit = 10
        mock_rate_limit.subscription_plan = "basic"
        mock_feature_service_class.get_knowledge_rate_limit.return_value = mock_rate_limit

        mock_api_token = MockApiToken()

        with patch('controllers.service_api.wraps.validate_and_get_api_token') as mock_validate_token, \
             patch('controllers.service_api.wraps.RateLimitLog') as mock_rate_limit_log_class:

            mock_validate_token.return_value = mock_api_token
            mock_rate_limit_log = MockRateLimitLog()
            mock_rate_limit_log_class.return_value = mock_rate_limit_log

            # Create test view function
            @cloud_edition_billing_rate_limit_check("knowledge", "app")
            def test_view():
                return {"success": True}

            # Execute and verify
            with pytest.raises(Forbidden, match="you have reached the knowledge base request rate limit"):
                test_view()

            # Verify rate limit log was created
            mock_db.session.add.assert_called_once()
            mock_db.session.commit.assert_called_once()
