import pytest
from unittest.mock import MagicMock
from core.entities.service_api_entities import (
    ModelStatus,
    AsyncResponseEntity,
    WorkflowAPIResponseEntity,
    ChatflowAPIResponseEntity
)

class TestModelStatus:
    def test_enum_values(self):
        assert ModelStatus.ACTIVE.value == "active"
        assert ModelStatus.NO_CONFIGURE.value == "no-configure"
        assert ModelStatus.QUOTA_EXCEEDED.value == "quota-exceeded"
        assert ModelStatus.NO_PERMISSION.value == "no-permission"
        assert ModelStatus.DISABLED.value == "disabled"

class TestAsyncResponseEntity:
    def test_default_values(self):
        entity = AsyncResponseEntity(request_id="test123")
        assert entity.request_id == "test123"
        assert entity.code == "normal"
        assert entity.message == ""
        assert entity.status == 200

    def test_custom_values(self):
        entity = AsyncResponseEntity(
            request_id="test123",
            code="error",
            message="test error",
            status=400
        )
        assert entity.code == "error"
        assert entity.message == "test error"
        assert entity.status == 400

class TestWorkflowAPIResponseEntity:
    @pytest.fixture
    def mock_workflow_data(self):
        return {
            "id": "wf123",
            "workflow_id": "workflow123",
            "status": "succeeded",
            "error": None,
            "task_id": "task123",
            "workflow_run_id": "run123",
            "elapsed_time": 1.5,
            "total_tokens": 100,
            "total_steps": 5,
            "created_at": 1234567890,
            "finished_at": 1234567891
        }

    @pytest.fixture
    def mock_failed_workflow_data(self):
        return {
            "id": "wf456",
            "workflow_id": "workflow456",
            "status": "failed",
            "error": "node error",
            "task_id": "task456",
            "workflow_run_id": "run456",
            "elapsed_time": 0.5,
            "total_tokens": 50,
            "total_steps": 3,
            "created_at": 1234567892,
            "finished_at": 1234567893
        }

    def test_success_validation(self, mock_workflow_data):
        entity = WorkflowAPIResponseEntity(
            request_id="test123",
            task_id="task123",
            workflow_run_id="run123",
            data=mock_workflow_data
        )
        assert entity.code == "normal"
        assert entity.status == 200
        assert entity.message == ""

    def test_failed_validation(self, mock_failed_workflow_data):
        entity = WorkflowAPIResponseEntity(
            request_id="test123",
            task_id="task123",
            workflow_run_id="run123",
            data=mock_failed_workflow_data
        )
        assert entity.code == "node_execute_faild"
        assert entity.status == 400
        assert entity.message == "node error"

    def test_default_values(self, mock_workflow_data):
        entity = WorkflowAPIResponseEntity(
            request_id="test123",
            task_id="task123",
            workflow_run_id="run123",
            data=mock_workflow_data
        )
        assert entity.code == "normal"
        assert entity.message == ""
        assert entity.status == 200

class TestChatflowAPIResponseEntity:
    def test_default_values(self):
        entity = ChatflowAPIResponseEntity(request_id="test123")
        assert entity.request_id == "test123"
        assert entity.event == "message"
        assert entity.code == "normal"
        assert entity.message == ""
        assert entity.status == 200
        assert entity.metadata == {}
        assert entity.task_id == ""
        assert entity.conversation_id == ""
        assert entity.message_id == ""

    def test_custom_values(self):
        entity = ChatflowAPIResponseEntity(
            request_id="test123",
            event="error",
            code="invalid_input",
            message="test error",
            status=400,
            metadata={"key": "value"},
            task_id="task123",
            conversation_id="conv123",
            message_id="msg123"
        )
        assert entity.event == "error"
        assert entity.code == "invalid_input"
        assert entity.message == "test error"
        assert entity.status == 400
        assert entity.metadata == {"key": "value"}
        assert entity.task_id == "task123"
        assert entity.conversation_id == "conv123"
        assert entity.message_id == "msg123"
