import uuid
import pytest

from core.tools.utils.uuid_utils import is_valid_uuid


class TestIsValidUuid:
    """Test cases for is_valid_uuid function"""

    def test_valid_uuid_v4(self):
        """Test with valid UUID v4"""
        valid_uuid = str(uuid.uuid4())
        assert is_valid_uuid(valid_uuid) is True

    def test_valid_uuid_v1(self):
        """Test with valid UUID v1"""
        valid_uuid = str(uuid.uuid1())
        assert is_valid_uuid(valid_uuid) is True

    def test_valid_uuid_v3(self):
        """Test with valid UUID v3"""
        valid_uuid = str(uuid.uuid3(uuid.NAMESPACE_DNS, 'example.com'))
        assert is_valid_uuid(valid_uuid) is True

    def test_valid_uuid_v5(self):
        """Test with valid UUID v5"""
        valid_uuid = str(uuid.uuid5(uuid.NAMESPACE_DNS, 'example.com'))
        assert is_valid_uuid(valid_uuid) is True

    def test_valid_uuid_uppercase(self):
        """Test with valid UUID in uppercase"""
        valid_uuid = str(uuid.uuid4()).upper()
        assert is_valid_uuid(valid_uuid) is True

    def test_valid_uuid_mixed_case(self):
        """Test with valid UUID in mixed case"""
        valid_uuid = str(uuid.uuid4())
        mixed_case_uuid = ''.join(c.upper() if i % 2 == 0 else c.lower() 
                                 for i, c in enumerate(valid_uuid))
        assert is_valid_uuid(mixed_case_uuid) is True

    def test_valid_uuid_with_braces(self):
        """Test with valid UUID wrapped in braces"""
        valid_uuid = '{' + str(uuid.uuid4()) + '}'
        assert is_valid_uuid(valid_uuid) is True

    def test_invalid_uuid_empty_string(self):
        """Test with empty string"""
        assert is_valid_uuid('') is False

    def test_invalid_uuid_none_string(self):
        """Test with 'None' string"""
        assert is_valid_uuid('None') is False

    def test_invalid_uuid_random_string(self):
        """Test with random string"""
        assert is_valid_uuid('not-a-uuid') is False

    def test_invalid_uuid_partial(self):
        """Test with partial UUID"""
        partial_uuid = str(uuid.uuid4())[:10]
        assert is_valid_uuid(partial_uuid) is False

    def test_invalid_uuid_too_long(self):
        """Test with UUID that's too long"""
        too_long_uuid = str(uuid.uuid4()) + 'extra'
        assert is_valid_uuid(too_long_uuid) is False

    def test_valid_uuid_without_hyphens(self):
        """Test with valid UUID without hyphens (this is actually valid)"""
        valid_uuid = str(uuid.uuid4()).replace('-', '')
        assert is_valid_uuid(valid_uuid) is True

    def test_invalid_uuid_wrong_characters(self):
        """Test with invalid characters"""
        invalid_uuid = '12345678-1234-1234-1234-12345678901g'  # 'g' is invalid hex
        assert is_valid_uuid(invalid_uuid) is False

    def test_invalid_uuid_numeric_string(self):
        """Test with numeric string"""
        assert is_valid_uuid('12345') is False

    def test_invalid_uuid_special_characters(self):
        """Test with special characters"""
        assert is_valid_uuid('!@#$%^&*()') is False

    def test_invalid_uuid_whitespace(self):
        """Test with whitespace"""
        assert is_valid_uuid('   ') is False

    def test_invalid_uuid_with_spaces(self):
        """Test with UUID containing spaces"""
        valid_uuid = str(uuid.uuid4())
        uuid_with_spaces = valid_uuid[:8] + ' ' + valid_uuid[8:]
        assert is_valid_uuid(uuid_with_spaces) is False

    def test_edge_case_almost_valid_uuid(self):
        """Test with almost valid UUID (wrong segment lengths)"""
        almost_valid = '12345678-123-1234-1234-123456789012'
        assert is_valid_uuid(almost_valid) is False
