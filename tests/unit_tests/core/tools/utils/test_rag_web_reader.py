import pytest

from core.tools.utils.rag_web_reader import get_image_upload_file_ids


class TestGetImageUploadFileIds:
    """Test cases for get_image_upload_file_ids function"""

    def test_get_image_upload_file_ids_file_preview(self):
        """Test extracting file IDs from file-preview URLs"""
        content = "Here is an image: ![image](http://example.com/files/abc123/file-preview)"
        result = get_image_upload_file_ids(content)
        
        expected = ["abc123"]
        assert result == expected

    def test_get_image_upload_file_ids_image_preview(self):
        """Test extracting file IDs from image-preview URLs"""
        content = "Here is an image: ![image](http://example.com/files/def456/image-preview)"
        result = get_image_upload_file_ids(content)
        
        expected = ["def456"]
        assert result == expected

    def test_get_image_upload_file_ids_multiple_images(self):
        """Test extracting file IDs from multiple images"""
        content = """
        First image: ![image](http://example.com/files/abc123/file-preview)
        Second image: ![image](http://example.com/files/def456/image-preview)
        Third image: ![image](http://example.com/files/ghi789/file-preview)
        """
        result = get_image_upload_file_ids(content)
        
        expected = ["abc123", "def456", "ghi789"]
        assert result == expected

    def test_get_image_upload_file_ids_mixed_protocols(self):
        """Test extracting file IDs from HTTP URLs (HTTPS not supported by current regex)"""
        content = """
        HTTP image: ![image](http://example.com/files/abc123/file-preview)
        HTTPS image: ![image](https://example.com/files/def456/image-preview)
        """
        result = get_image_upload_file_ids(content)

        # Current regex only supports http://, not https://
        expected = ["abc123"]
        assert result == expected

    def test_get_image_upload_file_ids_no_matches(self):
        """Test with content that has no matching image patterns"""
        content = "This is just regular text with no images."
        result = get_image_upload_file_ids(content)
        
        expected = []
        assert result == expected

    def test_get_image_upload_file_ids_invalid_image_format(self):
        """Test with invalid image format that doesn't match pattern"""
        content = "Invalid image: [image](http://example.com/files/abc123/file-preview)"
        result = get_image_upload_file_ids(content)
        
        expected = []
        assert result == expected

    def test_get_image_upload_file_ids_no_file_id_in_url(self):
        """Test with URLs that don't contain file IDs in expected format"""
        content = "![image](http://example.com/some-other-path/file-preview)"
        result = get_image_upload_file_ids(content)
        
        expected = []
        assert result == expected

    def test_get_image_upload_file_ids_empty_content(self):
        """Test with empty content"""
        content = ""
        result = get_image_upload_file_ids(content)
        
        expected = []
        assert result == expected

    def test_get_image_upload_file_ids_complex_file_ids(self):
        """Test with complex file IDs containing various characters"""
        content = """
        Complex ID 1: ![image](http://example.com/files/abc-123_def/file-preview)
        Complex ID 2: ![image](http://example.com/files/xyz789.test/image-preview)
        """
        result = get_image_upload_file_ids(content)
        
        expected = ["abc-123_def", "xyz789.test"]
        assert result == expected

    def test_get_image_upload_file_ids_duplicate_ids(self):
        """Test with duplicate file IDs"""
        content = """
        First occurrence: ![image](http://example.com/files/abc123/file-preview)
        Second occurrence: ![image](http://example.com/files/abc123/image-preview)
        """
        result = get_image_upload_file_ids(content)
        
        # Should include duplicates as the function doesn't deduplicate
        expected = ["abc123", "abc123"]
        assert result == expected

    def test_get_image_upload_file_ids_different_domains(self):
        """Test with different domains (only HTTP supported)"""
        content = """
        Domain 1: ![image](http://domain1.com/files/abc123/file-preview)
        Domain 2: ![image](http://domain2.org/files/def456/image-preview)
        """
        result = get_image_upload_file_ids(content)

        expected = ["abc123", "def456"]
        assert result == expected

    def test_get_image_upload_file_ids_with_query_params(self):
        """Test with URLs containing query parameters (not supported by current regex)"""
        content = "![image](http://example.com/files/abc123/file-preview?param=value)"
        result = get_image_upload_file_ids(content)

        # Current regex doesn't support query parameters
        expected = []
        assert result == expected

    def test_get_image_upload_file_ids_with_fragments(self):
        """Test with URLs containing fragments (not supported by current regex)"""
        content = "![image](http://example.com/files/abc123/image-preview#fragment)"
        result = get_image_upload_file_ids(content)

        # Current regex doesn't support fragments
        expected = []
        assert result == expected

    def test_get_image_upload_file_ids_multiline_content(self):
        """Test with multiline content and various spacing (only HTTP supported)"""
        content = """
        Line 1 with image: ![image](http://example.com/files/line1-id/file-preview)

        Line 3 with another image: ![image](http://example.com/files/line3-id/image-preview)

        Final line: ![image](http://example.com/files/final-id/file-preview)
        """
        result = get_image_upload_file_ids(content)

        # Only HTTP URLs are supported, not HTTPS
        expected = ["line1-id", "line3-id", "final-id"]
        assert result == expected

    def test_get_image_upload_file_ids_case_sensitivity(self):
        """Test case sensitivity of the pattern matching"""
        content = """
        Lowercase: ![image](http://example.com/files/abc123/file-preview)
        Mixed case path: ![image](http://example.com/Files/def456/Image-Preview)
        """
        result = get_image_upload_file_ids(content)
        
        # Only the first one should match due to case sensitivity
        expected = ["abc123"]
        assert result == expected
