import hashlib
import json
import os
import tempfile
import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock, mock_open
from bs4 import BeautifulSoup, Comment, CData

from core.tools.utils.web_reader_tool import (
    page_result,
    normalize_text,
    normalize_unicode,
    normalize_whitespace,
    strip_control_characters,
    is_leaf,
    is_text,
    is_non_printing,
    content_digest,
    add_content_digest,
    plain_text_leaf_node,
    find_module_path,
    extract_using_trafilatura,
    get_image_upload_file_ids
)


class TestPageResult:
    """Test cases for page_result function"""

    def test_page_result_normal_case(self):
        """Test normal paging through text"""
        text = "This is a long text that needs to be paged through"
        result = page_result(text, 0, 10)
        assert result == "This is a "

    def test_page_result_with_cursor(self):
        """Test paging with cursor position"""
        text = "This is a long text that needs to be paged through"
        result = page_result(text, 10, 10)
        assert result == "long text "

    def test_page_result_beyond_text_length(self):
        """Test paging beyond text length"""
        text = "Short text"
        result = page_result(text, 5, 20)
        assert result == " text"

    def test_page_result_empty_text(self):
        """Test paging with empty text"""
        text = ""
        result = page_result(text, 0, 10)
        assert result == ""


class TestNormalizeText:
    """Test cases for text normalization functions"""

    def test_normalize_text_basic(self):
        """Test basic text normalization"""
        text = "  Hello   World  \n\n  "
        result = normalize_text(text)
        assert result == "Hello World"

    def test_normalize_unicode(self):
        """Test unicode normalization"""
        # Test with unicode characters that should be normalized
        text = "café"  # Using combining characters
        result = normalize_unicode(text)
        assert isinstance(result, str)

    def test_normalize_whitespace(self):
        """Test whitespace normalization"""
        text = "Hello\n\n\tWorld   \r\n  Test"
        result = normalize_whitespace(text)
        assert result == "Hello World Test"

    def test_strip_control_characters(self):
        """Test stripping control characters"""
        # Include some control characters but keep newlines and tabs
        text = "Hello\tWorld\nTest\r"
        result = strip_control_characters(text)
        assert "\t" in result
        assert "\n" in result
        assert "\r" in result


class TestElementCheckers:
    """Test cases for element checking functions"""

    def test_is_leaf_paragraph(self):
        """Test is_leaf with paragraph element"""
        soup = BeautifulSoup("<p>Test</p>", "html.parser")
        p_element = soup.find("p")
        assert is_leaf(p_element) is True

    def test_is_leaf_list_item(self):
        """Test is_leaf with list item element"""
        soup = BeautifulSoup("<li>Test</li>", "html.parser")
        li_element = soup.find("li")
        assert is_leaf(li_element) is True

    def test_is_leaf_div(self):
        """Test is_leaf with div element"""
        soup = BeautifulSoup("<div>Test</div>", "html.parser")
        div_element = soup.find("div")
        assert is_leaf(div_element) is False

    def test_is_text_with_string(self):
        """Test is_text with NavigableString"""
        soup = BeautifulSoup("Hello World", "html.parser")
        text_node = soup.string
        assert is_text(text_node) is True

    def test_is_text_with_element(self):
        """Test is_text with HTML element"""
        soup = BeautifulSoup("<p>Test</p>", "html.parser")
        p_element = soup.find("p")
        assert is_text(p_element) is False

    def test_is_non_printing_comment(self):
        """Test is_non_printing with comment"""
        soup = BeautifulSoup("<!-- comment -->", "html.parser")
        comment = soup.contents[0]
        assert is_non_printing(comment) is True

    def test_is_non_printing_cdata(self):
        """Test is_non_printing with CDATA"""
        # CDATA is less common in HTML but can exist
        cdata = CData("test data")
        assert is_non_printing(cdata) is True

    def test_is_non_printing_regular_text(self):
        """Test is_non_printing with regular text"""
        soup = BeautifulSoup("Hello World", "html.parser")
        text_node = soup.string
        assert is_non_printing(text_node) is False


class TestContentDigest:
    """Test cases for content_digest function"""

    def test_content_digest_text_element(self):
        """Test content digest for text element"""
        soup = BeautifulSoup("Hello World", "html.parser")
        text_node = soup.string
        result = content_digest(text_node)
        
        expected = hashlib.sha256("Hello World".encode("utf-8")).hexdigest()
        assert result == expected

    def test_content_digest_empty_text(self):
        """Test content digest for empty text"""
        soup = BeautifulSoup("   ", "html.parser")
        text_node = soup.string
        result = content_digest(text_node)
        assert result == ""

    def test_content_digest_empty_element(self):
        """Test content digest for empty element"""
        soup = BeautifulSoup("<div></div>", "html.parser")
        div_element = soup.find("div")
        result = content_digest(div_element)
        assert result == ""

    def test_content_digest_single_child(self):
        """Test content digest for element with single child"""
        soup = BeautifulSoup("<div>Hello</div>", "html.parser")
        div_element = soup.find("div")
        result = content_digest(div_element)
        
        expected = hashlib.sha256("Hello".encode("utf-8")).hexdigest()
        assert result == expected


class TestAddContentDigest:
    """Test cases for add_content_digest function"""

    def test_add_content_digest_element(self):
        """Test adding content digest to element"""
        soup = BeautifulSoup("<p>Hello World</p>", "html.parser")
        p_element = soup.find("p")
        
        result = add_content_digest(p_element)
        
        assert "data-content-digest" in result.attrs
        expected_digest = hashlib.sha256("Hello World".encode("utf-8")).hexdigest()
        assert result["data-content-digest"] == expected_digest

    def test_add_content_digest_text_node(self):
        """Test adding content digest to text node (should not add attribute)"""
        soup = BeautifulSoup("Hello World", "html.parser")
        text_node = soup.string
        
        result = add_content_digest(text_node)
        
        # Text nodes can't have attributes, so it should return unchanged
        assert result == text_node


class TestPlainTextLeafNode:
    """Test cases for plain_text_leaf_node function"""

    def test_plain_text_leaf_node_paragraph(self):
        """Test plain text extraction from paragraph"""
        soup = BeautifulSoup("<p>Hello <strong>World</strong></p>", "html.parser")
        p_element = soup.find("p")
        
        result = plain_text_leaf_node(p_element)
        
        assert result["text"] == "Hello World"

    def test_plain_text_leaf_node_list_item(self):
        """Test plain text extraction from list item"""
        soup = BeautifulSoup("<li>Item text</li>", "html.parser")
        li_element = soup.find("li")
        
        result = plain_text_leaf_node(li_element)
        
        assert result["text"] == "* Item text, "

    def test_plain_text_leaf_node_empty(self):
        """Test plain text extraction from empty element"""
        soup = BeautifulSoup("<p></p>", "html.parser")
        p_element = soup.find("p")
        
        result = plain_text_leaf_node(p_element)
        
        assert result["text"] is None

    def test_plain_text_leaf_node_with_node_index(self):
        """Test plain text extraction with node index"""
        soup = BeautifulSoup('<p data-node-index="1.2">Hello</p>', "html.parser")
        p_element = soup.find("p")
        
        result = plain_text_leaf_node(p_element)
        
        assert result["text"] == "Hello"
        assert result["node_index"] == "1.2"


class TestFindModulePath:
    """Test cases for find_module_path function"""

    @patch('core.tools.utils.web_reader_tool.site.getsitepackages')
    @patch('core.tools.utils.web_reader_tool.os.path.exists')
    def test_find_module_path_found(self, mock_exists, mock_getsitepackages):
        """Test finding module path when it exists"""
        mock_getsitepackages.return_value = ["/usr/lib/python3.8/site-packages"]
        mock_exists.return_value = True
        
        result = find_module_path("test_module")
        
        assert result == "/usr/lib/python3.8/site-packages/test_module"

    @patch('core.tools.utils.web_reader_tool.site.getsitepackages')
    @patch('core.tools.utils.web_reader_tool.os.path.exists')
    def test_find_module_path_not_found(self, mock_exists, mock_getsitepackages):
        """Test finding module path when it doesn't exist"""
        mock_getsitepackages.return_value = ["/usr/lib/python3.8/site-packages"]
        mock_exists.return_value = False
        
        result = find_module_path("nonexistent_module")
        
        assert result is None


class TestExtractUsingTrafilatura:
    """Test cases for extract_using_trafilatura function"""

    @patch('trafilatura.extract')
    @patch('trafilatura.extract_metadata')
    def test_extract_using_trafilatura_success(self, mock_extract_metadata, mock_extract):
        """Test successful extraction using trafilatura"""
        # Mock metadata
        mock_meta = Mock()
        mock_meta.title = "Test Title"
        mock_meta.date = "2023-01-01"
        mock_meta.author = "Test Author"
        mock_extract_metadata.return_value = mock_meta

        # Mock content extraction
        mock_extract.return_value = "Extracted content"

        html = "<html><body><p>Test content</p></body></html>"
        result = extract_using_trafilatura(html)

        assert result["title"] == "Test Title"
        assert result["date"] == "2023-01-01"
        assert result["byline"] == "Test Author"
        assert result["content"] == "Extracted content"
        assert result["plain_content"] == "Extracted content"
        assert result["plain_text"] == "Extracted content"

    @patch('trafilatura.extract')
    @patch('trafilatura.extract_metadata')
    def test_extract_using_trafilatura_no_metadata(self, mock_extract_metadata, mock_extract):
        """Test extraction when no metadata is available (will raise AttributeError)"""
        mock_extract_metadata.return_value = None
        mock_extract.return_value = "Extracted content"

        html = "<html><body><p>Test content</p></body></html>"

        # The current implementation has a bug - it will fail when meta is None
        with pytest.raises(AttributeError):
            extract_using_trafilatura(html)


class TestGetImageUploadFileIds:
    """Test cases for get_image_upload_file_ids function (duplicate from rag_web_reader)"""

    def test_get_image_upload_file_ids_basic(self):
        """Test basic image file ID extraction"""
        content = "![image](http://example.com/files/abc123/file-preview)"
        result = get_image_upload_file_ids(content)
        
        expected = ["abc123"]
        assert result == expected
