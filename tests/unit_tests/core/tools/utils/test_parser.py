import pytest
from unittest.mock import patch, MagicMock
import json
import yaml

from core.tools.utils.parser import ApiBasedToolSchemaParser
from core.tools.errors import ToolApiSchemaError, ToolProviderNotFoundError, ToolNotSupportedError
from core.tools.entities.tool_entities import ToolParameter, ApiProviderSchemaType

class TestApiBasedToolSchemaParser:
    @pytest.fixture
    def sample_openapi(self):
        return {
            "openapi": "3.0.0",
            "info": {
                "title": "Test API",
                "description": "Test Description",
                "version": "1.0.0"
            },
            "servers": [{"url": "http://example.com"}],
            "paths": {
                "/test": {
                    "get": {
                        "operationId": "getTest",
                        "description": "Test operation",
                        "parameters": [
                            {
                                "name": "param1",
                                "in": "query",
                                "description": "Test param",
                                "required": True,
                                "schema": {"type": "string"}
                            }
                        ]
                    }
                }
            }
        }

    @pytest.fixture
    def sample_swagger(self):
        return {
            "swagger": "2.0",
            "info": {
                "title": "Test API",
                "description": "Test Description",
                "version": "1.0.0"
            },
            "servers": [{"url": "http://example.com"}],
            "paths": {
                "/test": {
                    "get": {
                        "operationId": "getTest",
                        "summary": "Test operation",
                        "parameters": [
                            {
                                "name": "param1",
                                "in": "query",
                                "description": "Test param",
                                "required": True,
                                "type": "string"
                            }
                        ]
                    }
                }
            },
            "definitions": {}
        }

    def test_parse_openapi_to_tool_bundle(self, sample_openapi):
        # Create complete mock for request
        mock_request = MagicMock()
        mock_request.headers = MagicMock()
        mock_request.headers.get.return_value = None
        
        # Patch the request object
        with patch('core.tools.utils.parser.request', mock_request):
            bundles = ApiBasedToolSchemaParser.parse_openapi_to_tool_bundle(sample_openapi)
            assert len(bundles) == 1
            bundle = bundles[0]
            assert bundle.method == "get"
            assert bundle.operation_id == "getTest"
            assert len(bundle.parameters) == 1
            assert bundle.parameters[0].name == "param1"
            assert bundle.parameters[0].type == ToolParameter.ToolParameterType.STRING
            mock_request.headers.get.assert_called_once_with("X-Request-Env")

    def test_parse_openapi_missing_servers(self):
        invalid_openapi = {
            "openapi": "3.0.0",
            "info": {"title": "Test"},
            "paths": {},
            "servers": []
        }
        with pytest.raises(ToolProviderNotFoundError):
            ApiBasedToolSchemaParser.parse_openapi_to_tool_bundle(invalid_openapi)

    @patch('core.tools.utils.parser.safe_load')
    def test_parse_openapi_yaml_to_tool_bundle(self, mock_safe_load, sample_openapi):
        mock_safe_load.return_value = sample_openapi
        
        # Create complete mock for request
        mock_request = MagicMock()
        mock_request.headers = MagicMock()
        mock_request.headers.get.return_value = None
        
        yaml_str = "test yaml"
        with patch('core.tools.utils.parser.request', mock_request):
            bundles = ApiBasedToolSchemaParser.parse_openapi_yaml_to_tool_bundle(yaml_str)
            assert len(bundles) == 1
            mock_safe_load.assert_called_once_with(yaml_str)
            mock_request.headers.get.assert_called_once_with("X-Request-Env")

    def test_parse_swagger_to_openapi(self, sample_swagger):
        openapi = ApiBasedToolSchemaParser.parse_swagger_to_openapi(sample_swagger)
        assert openapi["openapi"] == "3.0.0"
        assert "/test" in openapi["paths"]
        assert "get" in openapi["paths"]["/test"]

    def test_get_tool_parameter_type(self):
        # Test string type
        param = {"type": "string"}
        assert ApiBasedToolSchemaParser._get_tool_parameter_type(param) == ToolParameter.ToolParameterType.STRING

        # Test number type
        param = {"type": "number"}
        assert ApiBasedToolSchemaParser._get_tool_parameter_type(param) == ToolParameter.ToolParameterType.NUMBER

        # Test file type
        param = {"format": "binary"}
        assert ApiBasedToolSchemaParser._get_tool_parameter_type(param) == ToolParameter.ToolParameterType.FILE

        # Test invalid type
        param = {"type": "invalid"}
        assert ApiBasedToolSchemaParser._get_tool_parameter_type(param) is None
