import pytest
from unittest.mock import patch, MagicMock
import json
import httpx

from core.tools.entities.common_entities import I18nObject
from core.tools.entities.tool_entities import ToolIdentity, ToolDescription
from core.tools.custom_tool.tool import ApiTool
from core.tools.entities.tool_bundle import ApiToolBundle
from core.tools.entities.tool_entities import ToolEntity, ToolParameter, ToolProviderType
from core.tools.errors import ToolInvokeError, ToolParameterValidationError, ToolProviderCredentialValidationError

class TestApiTool:
    @pytest.fixture
    def sample_api_bundle(self):
        return ApiToolBundle(
            server_url="http://example.com/api",
            author="test",
            method="GET",
            operation_id="testOperation",
            parameters=[
                ToolParameter(
                    name="param1",
                    label=I18nObject(en_US="Param1", zh_Hans="参数1"),
                    placeholder=None,
                    human_description=I18nObject(en_US="First parameter", zh_Hans="第一个参数"),
                    type=ToolParameter.ToolParameterType.STRING,
                    form=ToolParameter.ToolParameterForm.SCHEMA,
                    required=True,
                    default=None
                )
            ],
            openapi={
                "openapi": "3.0.0",
                "paths": {
                    "/api": {
                        "get": {
                            "parameters": [
                                {
                                    "name": "param1",
                                    "in": "query",
                                    "required": True,
                                    "schema": {"type": "string"}
                                }
                            ]
                        }
                    }
                }
            }
        )

    @pytest.fixture
    def sample_tool_entity(self):
        return ToolEntity(
            identity=ToolIdentity(
                author="test",
                name="Test Tool",
                label=I18nObject(en_US="Test Label", zh_Hans="测试标签"),
                provider="test_provider"
            ),
            description=ToolDescription(
                human=I18nObject(en_US="Test Description", zh_Hans="测试描述"),
                llm="Test Description for LLM"
            ),
            parameters=[]
        )

    @pytest.fixture
    def api_tool(self, sample_tool_entity, sample_api_bundle):
        runtime = MagicMock()
        runtime.credentials = {
            "auth_type": "api_key",
            "api_key_header": "Authorization",
            "api_key_value": "test_key"
        }
        return ApiTool(
            entity=sample_tool_entity,
            api_bundle=sample_api_bundle,
            runtime=runtime,
            provider_id="test_provider"
        )

    def test_assembling_request_api_key(self, api_tool):
        parameters = {"param1": "value1"}
        headers = api_tool.assembling_request(parameters)
        
        assert "Authorization" in headers
        assert headers["Authorization"] == "test_key"
        assert api_tool.runtime.credentials["api_key_value"] == "test_key"

    def test_assembling_request_missing_required_param(self, api_tool):
        with pytest.raises(ToolParameterValidationError):
            api_tool.assembling_request({})

    @patch('core.tools.custom_tool.tool.ssrf_proxy')
    def test_do_http_request_success(self, mock_ssrf, api_tool):
        mock_response = MagicMock(spec=httpx.Response)
        mock_response.status_code = 200
        mock_response.text = '{"result": "success"}'
        mock_ssrf.get.return_value = mock_response

        response = api_tool.do_http_request(
            url="http://example.com/api",
            method="GET",
            headers={"Authorization": "test_key"},
            parameters={"param1": "value1"}
        )

        assert response.status_code == 200
        mock_ssrf.get.assert_called_once()

    def test_validate_and_parse_response_success(self, api_tool):
        mock_response = MagicMock(spec=httpx.Response)
        mock_response.status_code = 200
        mock_response.json.return_value = {"result": "success"}
        
        result = api_tool.validate_and_parse_response(mock_response)
        assert json.loads(result) == {"result": "success"}

    def test_validate_and_parse_response_error(self, api_tool):
        mock_response = MagicMock(spec=httpx.Response)
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        
        with pytest.raises(ToolInvokeError):
            api_tool.validate_and_parse_response(mock_response)

    def test_convert_body_property_type_string(self, api_tool):
        result = api_tool._convert_body_property_type(
            {"type": "string"}, 
            123
        )
        assert result == "123"

    def test_convert_body_property_type_number(self, api_tool):
        result = api_tool._convert_body_property_type(
            {"type": "number"}, 
            "123.45"
        )
        assert result == 123.45

    def test_convert_body_property_type_boolean(self, api_tool):
        result = api_tool._convert_body_property_type(
            {"type": "boolean"}, 
            "true"
        )
        assert result is True

    def test_tool_provider_type(self, api_tool):
        assert api_tool.tool_provider_type() == ToolProviderType.API

    def test_fork_tool_runtime(self, api_tool):
        new_runtime = MagicMock()
        new_tool = api_tool.fork_tool_runtime(new_runtime)
        
        assert new_tool.runtime == new_runtime
        assert new_tool.api_bundle.operation_id == "testOperation"

    def test_assembling_request_with_optional_param(self, api_tool):
        api_tool.api_bundle.parameters.append(
            ToolParameter(
                name="optional_param",
                label=I18nObject(en_US="Optional Param", zh_Hans="可选参数"),
                placeholder=None,
                human_description=I18nObject(en_US="Optional parameter", zh_Hans="可选参数"),
                type=ToolParameter.ToolParameterType.STRING,
                form=ToolParameter.ToolParameterForm.SCHEMA,
                required=False,
                default="default_value"
            )
        )
        headers = api_tool.assembling_request({"param1": "value1"})
        assert "Authorization" in headers

    @patch('core.tools.custom_tool.tool.ssrf_proxy')
    def test_do_http_request_post_method(self, mock_ssrf, api_tool):
        api_tool.api_bundle.method = "POST"
        mock_response = MagicMock(spec=httpx.Response)
        mock_response.status_code = 201
        mock_response.text = '{"result": "created"}'
        mock_ssrf.post.return_value = mock_response

        response = api_tool.do_http_request(
            url="http://example.com/api",
            method="POST",
            headers={"Authorization": "test_key"},
            parameters={"param1": "value1"}
        )

        assert response.status_code == 201
        mock_ssrf.post.assert_called_once()

    def test_validate_and_parse_response_invalid_json(self, api_tool):
        mock_response = MagicMock(spec=httpx.Response)
        mock_response.status_code = 200
        mock_response.text = "invalid json"
        mock_response.json.side_effect = json.JSONDecodeError("Expecting value", "", 0)

    def test_convert_body_property_type_array(self, api_tool):
        result = api_tool._convert_body_property_type(
            {"type": "array", "items": {"type": "string"}},
            ["1", "2", "3"]
        )
        assert result == ["1", "2", "3"]

    def test_convert_body_property_type_object(self, api_tool):
        result = api_tool._convert_body_property_type(
            {"type": "object"},
            {"key": "value"}
        )
        assert result == {"key": "value"}
