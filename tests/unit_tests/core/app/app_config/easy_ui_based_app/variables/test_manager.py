import pytest
from unittest.mock import Mock, patch

from core.app.app_config.easy_ui_based_app.variables.manager import BasicVariablesConfigManager
from core.app.app_config.entities import ExternalDataVariableEntity, VariableEntity, VariableEntityType


class TestBasicVariablesConfigManager:
    """Test cases for BasicVariablesConfigManager"""

    def test_convert_empty_config(self):
        """Test convert with empty config"""
        config = {}
        
        variable_entities, external_data_variables = BasicVariablesConfigManager.convert(config)
        
        assert variable_entities == []
        assert external_data_variables == []

    def test_convert_with_external_data_tools_enabled(self):
        """Test convert with enabled external data tools"""
        config = {
            "external_data_tools": [
                {
                    "enabled": True,
                    "variable": "external_var1",
                    "type": "web_search",
                    "config": {"api_key": "test_key"}
                },
                {
                    "enabled": False,
                    "variable": "external_var2", 
                    "type": "database",
                    "config": {"connection": "test_conn"}
                }
            ]
        }
        
        variable_entities, external_data_variables = BasicVariablesConfigManager.convert(config)
        
        assert len(variable_entities) == 0
        assert len(external_data_variables) == 1
        assert external_data_variables[0].variable == "external_var1"
        assert external_data_variables[0].type == "web_search"
        assert external_data_variables[0].config == {"api_key": "test_key"}

    def test_convert_with_external_data_tools_missing_enabled(self):
        """Test convert with external data tools missing enabled field"""
        config = {
            "external_data_tools": [
                {
                    "variable": "external_var1",
                    "type": "web_search",
                    "config": {"api_key": "test_key"}
                }
            ]
        }
        
        variable_entities, external_data_variables = BasicVariablesConfigManager.convert(config)
        
        assert len(variable_entities) == 0
        assert len(external_data_variables) == 0

    def test_convert_with_user_input_form_text_input(self):
        """Test convert with text input in user input form"""
        config = {
            "user_input_form": [
                {
                    "text-input": {
                        "variable": "user_name",
                        "description": "Enter your name",
                        "label": "Name",
                        "required": True,
                        "max_length": 50
                    }
                }
            ]
        }
        
        variable_entities, external_data_variables = BasicVariablesConfigManager.convert(config)
        
        assert len(variable_entities) == 1
        assert len(external_data_variables) == 0
        
        var = variable_entities[0]
        assert var.type == VariableEntityType.TEXT_INPUT
        assert var.variable == "user_name"
        assert var.description == "Enter your name"
        assert var.label == "Name"
        assert var.required is True
        assert var.max_length == 50

    def test_convert_with_user_input_form_select(self):
        """Test convert with select input in user input form"""
        config = {
            "user_input_form": [
                {
                    "select": {
                        "variable": "category",
                        "label": "Category",
                        "required": False,
                        "options": ["option1", "option2", "option3"]
                    }
                }
            ]
        }
        
        variable_entities, external_data_variables = BasicVariablesConfigManager.convert(config)
        
        assert len(variable_entities) == 1
        var = variable_entities[0]
        assert var.type == VariableEntityType.SELECT
        assert var.variable == "category"
        assert var.options == ["option1", "option2", "option3"]

    def test_convert_with_user_input_form_external_data_tool(self):
        """Test convert with external data tool in user input form"""
        config = {
            "user_input_form": [
                {
                    "external_data_tool": {
                        "variable": "external_var",
                        "type": "web_search",
                        "config": {"api_key": "test_key"}
                    }
                }
            ]
        }
        
        variable_entities, external_data_variables = BasicVariablesConfigManager.convert(config)
        
        assert len(variable_entities) == 0
        assert len(external_data_variables) == 1
        assert external_data_variables[0].variable == "external_var"

    def test_convert_with_user_input_form_external_data_tool_missing_config(self):
        """Test convert with external data tool missing config"""
        config = {
            "user_input_form": [
                {
                    "external_data_tool": {
                        "variable": "external_var",
                        "type": "web_search"
                    }
                }
            ]
        }
        
        variable_entities, external_data_variables = BasicVariablesConfigManager.convert(config)
        
        assert len(variable_entities) == 0
        assert len(external_data_variables) == 0

    def test_convert_with_mixed_user_input_form(self):
        """Test convert with mixed types in user input form"""
        config = {
            "user_input_form": [
                {
                    "text-input": {
                        "variable": "name",
                        "label": "Name",
                        "required": True
                    }
                },
                {
                    "paragraph": {
                        "variable": "description",
                        "label": "Description"
                    }
                },
                {
                    "number": {
                        "variable": "age",
                        "label": "Age"
                    }
                }
            ]
        }
        
        variable_entities, external_data_variables = BasicVariablesConfigManager.convert(config)
        
        assert len(variable_entities) == 3
        assert len(external_data_variables) == 0
        
        types = [var.type for var in variable_entities]
        assert VariableEntityType.TEXT_INPUT in types
        assert VariableEntityType.PARAGRAPH in types
        assert VariableEntityType.NUMBER in types

    def test_validate_variables_and_set_defaults_empty_config(self):
        """Test validate_variables_and_set_defaults with empty config"""
        config = {}
        
        result_config, related_keys = BasicVariablesConfigManager.validate_variables_and_set_defaults(config)
        
        assert result_config["user_input_form"] == []
        assert related_keys == ["user_input_form"]

    def test_validate_variables_and_set_defaults_invalid_type(self):
        """Test validate_variables_and_set_defaults with invalid user_input_form type"""
        config = {"user_input_form": "invalid"}
        
        with pytest.raises(ValueError, match="user_input_form must be a list of objects"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_variables_and_set_defaults_invalid_key(self):
        """Test validate_variables_and_set_defaults with invalid key"""
        config = {
            "user_input_form": [
                {
                    "invalid-type": {
                        "variable": "test",
                        "label": "Test"
                    }
                }
            ]
        }
        
        with pytest.raises(ValueError, match="Keys in user_input_form list can only be"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_variables_and_set_defaults_missing_label(self):
        """Test validate_variables_and_set_defaults with missing label"""
        config = {
            "user_input_form": [
                {
                    "text-input": {
                        "variable": "test"
                    }
                }
            ]
        }
        
        with pytest.raises(ValueError, match="label is required in user_input_form"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_variables_and_set_defaults_invalid_label_type(self):
        """Test validate_variables_and_set_defaults with invalid label type"""
        config = {
            "user_input_form": [
                {
                    "text-input": {
                        "variable": "test",
                        "label": 123
                    }
                }
            ]
        }
        
        with pytest.raises(ValueError, match="label in user_input_form must be of string type"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_variables_and_set_defaults_missing_variable(self):
        """Test validate_variables_and_set_defaults with missing variable"""
        config = {
            "user_input_form": [
                {
                    "text-input": {
                        "label": "Test"
                    }
                }
            ]
        }
        
        with pytest.raises(ValueError, match="variable is required in user_input_form"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_variables_and_set_defaults_invalid_variable_type(self):
        """Test validate_variables_and_set_defaults with invalid variable type"""
        config = {
            "user_input_form": [
                {
                    "text-input": {
                        "variable": 123,
                        "label": "Test"
                    }
                }
            ]
        }
        
        with pytest.raises(ValueError, match="variable in user_input_form must be of string type"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_variables_and_set_defaults_invalid_variable_pattern(self):
        """Test validate_variables_and_set_defaults with invalid variable pattern"""
        config = {
            "user_input_form": [
                {
                    "text-input": {
                        "variable": "123invalid",  # starts with number
                        "label": "Test"
                    }
                }
            ]
        }
        
        with pytest.raises(ValueError, match="variable in user_input_form must be a string, and cannot start with a number"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_variables_and_set_defaults_sets_required_default(self):
        """Test validate_variables_and_set_defaults sets required default to False"""
        config = {
            "user_input_form": [
                {
                    "text-input": {
                        "variable": "test_var",
                        "label": "Test"
                    }
                }
            ]
        }
        
        result_config, _ = BasicVariablesConfigManager.validate_variables_and_set_defaults(config)
        
        assert result_config["user_input_form"][0]["text-input"]["required"] is False

    def test_validate_variables_and_set_defaults_invalid_required_type(self):
        """Test validate_variables_and_set_defaults with invalid required type"""
        config = {
            "user_input_form": [
                {
                    "text-input": {
                        "variable": "test_var",
                        "label": "Test",
                        "required": "true"  # should be boolean
                    }
                }
            ]
        }
        
        with pytest.raises(ValueError, match="required in user_input_form must be of boolean type"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_variables_and_set_defaults_select_sets_options_default(self):
        """Test validate_variables_and_set_defaults sets options default for select"""
        config = {
            "user_input_form": [
                {
                    "select": {
                        "variable": "test_var",
                        "label": "Test"
                    }
                }
            ]
        }
        
        result_config, _ = BasicVariablesConfigManager.validate_variables_and_set_defaults(config)
        
        assert result_config["user_input_form"][0]["select"]["options"] == []

    def test_validate_variables_and_set_defaults_select_invalid_options_type(self):
        """Test validate_variables_and_set_defaults with invalid options type"""
        config = {
            "user_input_form": [
                {
                    "select": {
                        "variable": "test_var",
                        "label": "Test",
                        "options": "invalid"  # should be list
                    }
                }
            ]
        }
        
        with pytest.raises(ValueError, match="options in user_input_form must be a list of strings"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_variables_and_set_defaults_select_invalid_default(self):
        """Test validate_variables_and_set_defaults with invalid default value"""
        config = {
            "user_input_form": [
                {
                    "select": {
                        "variable": "test_var",
                        "label": "Test",
                        "options": ["option1", "option2"],
                        "default": "invalid_option"
                    }
                }
            ]
        }
        
        with pytest.raises(ValueError, match="default value in user_input_form must be in the options list"):
            BasicVariablesConfigManager.validate_variables_and_set_defaults(config)

    def test_validate_external_data_tools_and_set_defaults_empty_config(self):
        """Test validate_external_data_tools_and_set_defaults with empty config"""
        config = {}

        result_config, related_keys = BasicVariablesConfigManager.validate_external_data_tools_and_set_defaults("tenant_id", config)

        assert result_config["external_data_tools"] == []
        assert related_keys == ["external_data_tools"]

    def test_validate_external_data_tools_and_set_defaults_invalid_type(self):
        """Test validate_external_data_tools_and_set_defaults with invalid type"""
        config = {"external_data_tools": "invalid"}

        with pytest.raises(ValueError, match="external_data_tools must be of list type"):
            BasicVariablesConfigManager.validate_external_data_tools_and_set_defaults("tenant_id", config)

    def test_validate_external_data_tools_and_set_defaults_sets_enabled_default(self):
        """Test validate_external_data_tools_and_set_defaults sets enabled default to False"""
        config = {
            "external_data_tools": [
                {
                    "type": "web_search",
                    "config": {"api_key": "test"}
                }
            ]
        }

        result_config, _ = BasicVariablesConfigManager.validate_external_data_tools_and_set_defaults("tenant_id", config)

        assert result_config["external_data_tools"][0]["enabled"] is False

    def test_validate_external_data_tools_and_set_defaults_disabled_tool(self):
        """Test validate_external_data_tools_and_set_defaults with disabled tool"""
        config = {
            "external_data_tools": [
                {
                    "enabled": False,
                    "type": "web_search",
                    "config": {"api_key": "test"}
                }
            ]
        }

        # Should not raise any validation errors for disabled tools
        result_config, related_keys = BasicVariablesConfigManager.validate_external_data_tools_and_set_defaults("tenant_id", config)

        assert related_keys == ["external_data_tools"]

    def test_validate_external_data_tools_and_set_defaults_missing_type(self):
        """Test validate_external_data_tools_and_set_defaults with missing type"""
        config = {
            "external_data_tools": [
                {
                    "enabled": True,
                    "config": {"api_key": "test"}
                }
            ]
        }

        with pytest.raises(ValueError, match="external_data_tools\\[\\]\\.type is required"):
            BasicVariablesConfigManager.validate_external_data_tools_and_set_defaults("tenant_id", config)

    def test_validate_external_data_tools_and_set_defaults_empty_type(self):
        """Test validate_external_data_tools_and_set_defaults with empty type"""
        config = {
            "external_data_tools": [
                {
                    "enabled": True,
                    "type": "",
                    "config": {"api_key": "test"}
                }
            ]
        }

        with pytest.raises(ValueError, match="external_data_tools\\[\\]\\.type is required"):
            BasicVariablesConfigManager.validate_external_data_tools_and_set_defaults("tenant_id", config)

    @patch('core.app.app_config.easy_ui_based_app.variables.manager.ExternalDataToolFactory.validate_config')
    def test_validate_external_data_tools_and_set_defaults_valid_tool(self, mock_validate_config):
        """Test validate_external_data_tools_and_set_defaults with valid enabled tool"""
        config = {
            "external_data_tools": [
                {
                    "enabled": True,
                    "type": "web_search",
                    "config": {"api_key": "test"}
                }
            ]
        }

        result_config, related_keys = BasicVariablesConfigManager.validate_external_data_tools_and_set_defaults("tenant_id", config)

        mock_validate_config.assert_called_once_with(name="web_search", tenant_id="tenant_id", config={"api_key": "test"})
        assert related_keys == ["external_data_tools"]

    @patch('core.app.app_config.easy_ui_based_app.variables.manager.BasicVariablesConfigManager.validate_variables_and_set_defaults')
    @patch('core.app.app_config.easy_ui_based_app.variables.manager.BasicVariablesConfigManager.validate_external_data_tools_and_set_defaults')
    def test_validate_and_set_defaults(self, mock_validate_external, mock_validate_variables):
        """Test validate_and_set_defaults calls both validation methods"""
        mock_validate_variables.return_value = ({"user_input_form": []}, ["user_input_form"])
        mock_validate_external.return_value = ({"external_data_tools": []}, ["external_data_tools"])

        config = {}
        result_config, related_keys = BasicVariablesConfigManager.validate_and_set_defaults("tenant_id", config)

        mock_validate_variables.assert_called_once_with(config)
        # The config is modified by the first call, so we expect the modified config
        mock_validate_external.assert_called_once_with("tenant_id", {"user_input_form": []})
        assert related_keys == ["user_input_form", "external_data_tools"]
