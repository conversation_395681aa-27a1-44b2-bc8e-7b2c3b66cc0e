import pytest
from unittest.mock import Mock

from core.app.apps.advanced_chat.app_generator import AdvancedChatAppGenerator
from core.app.entities.app_invoke_entities import InvokeFrom


class TestAdvancedChatAppGenerator:
    """Test cases for AdvancedChatAppGenerator"""

    def setup_method(self):
        """Setup test fixtures"""
        self.generator = AdvancedChatAppGenerator()
        
        # Mock objects
        self.mock_app = Mock()
        self.mock_app.id = "app_123"
        self.mock_app.tenant_id = "tenant_123"
        
        self.mock_workflow = Mock()
        self.mock_workflow.features_dict = {}
        self.mock_workflow.features = {}
        
        self.mock_user = Mock()
        self.mock_user.id = "user_123"

    def test_generate_missing_query_raises_error(self):
        """Test generate method raises error when query is missing"""
        args = {}
        
        with pytest.raises(ValueError, match="query is required"):
            self.generator.generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                user=self.mock_user,
                args=args,
                invoke_from=InvokeFrom.WEB_APP,
                streaming=False
            )

    def test_generate_non_string_query_raises_error(self):
        """Test generate method raises error when query is not a string"""
        args = {"query": 123}
        
        with pytest.raises(ValueError, match="query must be a string"):
            self.generator.generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                user=self.mock_user,
                args=args,
                invoke_from=InvokeFrom.WEB_APP,
                streaming=False
            )

    def test_single_iteration_generate_missing_node_id(self):
        """Test single_iteration_generate raises error when node_id is missing"""
        args = {"inputs": {"key": "value"}}
        
        with pytest.raises(ValueError, match="node_id is required"):
            self.generator.single_iteration_generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                node_id="",
                user=self.mock_user,
                args=args,
                streaming=False
            )

    def test_single_iteration_generate_missing_inputs(self):
        """Test single_iteration_generate raises error when inputs is missing"""
        args = {}
        
        with pytest.raises(ValueError, match="inputs is required"):
            self.generator.single_iteration_generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                node_id="node_123",
                user=self.mock_user,
                args=args,
                streaming=False
            )

    def test_single_loop_generate_missing_node_id(self):
        """Test single_loop_generate raises error when node_id is missing"""
        args = {"inputs": {"key": "value"}}
        
        with pytest.raises(ValueError, match="node_id is required"):
            self.generator.single_loop_generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                node_id="",
                user=self.mock_user,
                args=args,
                streaming=False
            )

    def test_single_loop_generate_missing_inputs(self):
        """Test single_loop_generate raises error when inputs is missing"""
        args = {}
        
        with pytest.raises(ValueError, match="inputs is required"):
            self.generator.single_loop_generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                node_id="node_123",
                user=self.mock_user,
                args=args,
                streaming=False
            )

    def test_generator_initialization(self):
        """Test AdvancedChatAppGenerator initialization"""
        generator = AdvancedChatAppGenerator()
        assert generator is not None
        assert hasattr(generator, 'generate')
        assert hasattr(generator, 'single_iteration_generate')
        assert hasattr(generator, 'single_loop_generate')

    def test_generator_inherits_from_base(self):
        """Test that AdvancedChatAppGenerator inherits from base class"""
        from core.app.apps.base_app_generator import BaseAppGenerator
        assert issubclass(AdvancedChatAppGenerator, BaseAppGenerator)

    def test_run_id_property(self):
        """Test run_id property getter and setter"""
        generator = AdvancedChatAppGenerator()
        
        # Test initial state
        assert generator.run_id is None
        
        # Test setter
        test_run_id = "test_run_123"
        generator.run_id = test_run_id
        assert generator.run_id == test_run_id
        
        # Test getter
        retrieved_run_id = generator.run_id
        assert retrieved_run_id == test_run_id

    def test_null_byte_removal_logic(self):
        """Test null byte removal logic"""
        # Test the null byte removal logic directly
        test_string = "test\x00query\x00with\x00nulls"
        cleaned_string = test_string.replace('\x00', '')
        assert cleaned_string == "testquerywithnulls"
        
        # Test with no null bytes
        normal_string = "normal query"
        cleaned_normal = normal_string.replace('\x00', '')
        assert cleaned_normal == "normal query"
        
        # Test with only null bytes
        null_only = "\x00\x00\x00"
        cleaned_null = null_only.replace('\x00', '')
        assert cleaned_null == ""

    def test_class_methods_exist(self):
        """Test that all expected methods exist"""
        generator = AdvancedChatAppGenerator()
        
        # Test public methods
        assert hasattr(generator, 'generate')
        assert hasattr(generator, 'single_iteration_generate')
        assert hasattr(generator, 'single_loop_generate')
        
        # Test private methods
        assert hasattr(generator, '_generate')
        assert hasattr(generator, '_generate_worker')
        assert hasattr(generator, '_handle_advanced_chat_response')
        
        # Test all methods are callable
        assert callable(generator.generate)
        assert callable(generator.single_iteration_generate)
        assert callable(generator.single_loop_generate)
        assert callable(generator._generate)
        assert callable(generator._generate_worker)
        assert callable(generator._handle_advanced_chat_response)

    def test_invoke_from_enum_values(self):
        """Test InvokeFrom enum values are accessible"""
        # Test that we can access InvokeFrom values
        assert InvokeFrom.WEB_APP is not None
        assert InvokeFrom.DEBUGGER is not None
        assert InvokeFrom.SERVICE_API is not None
        
        # Test enum comparison
        assert InvokeFrom.WEB_APP != InvokeFrom.DEBUGGER
        assert InvokeFrom.WEB_APP != InvokeFrom.SERVICE_API
        assert InvokeFrom.DEBUGGER != InvokeFrom.SERVICE_API

    def test_generator_attributes(self):
        """Test generator has expected attributes"""
        generator = AdvancedChatAppGenerator()
        
        # Test that generator has run_id attribute
        assert hasattr(generator, 'run_id')
        
        # Test initial run_id is None
        assert generator.run_id is None
        
        # Test we can set run_id
        generator.run_id = "test_123"
        assert generator.run_id == "test_123"

    def test_string_operations(self):
        """Test string operations used in the generator"""
        # Test string replacement (used for null byte removal)
        test_str = "hello\x00world\x00test"
        result = test_str.replace('\x00', '')
        assert result == "helloworldtest"
        
        # Test string strip
        test_str2 = "  hello world  "
        result2 = test_str2.strip()
        assert result2 == "hello world"
        
        # Test empty string handling
        empty_str = ""
        assert empty_str.replace('\x00', '') == ""
        assert empty_str.strip() == ""

    def test_mock_object_setup(self):
        """Test that mock objects are set up correctly"""
        # Test mock app
        assert self.mock_app.id == "app_123"
        assert self.mock_app.tenant_id == "tenant_123"
        
        # Test mock workflow
        assert self.mock_workflow.features_dict == {}
        assert self.mock_workflow.features == {}
        
        # Test mock user
        assert self.mock_user.id == "user_123"
        
        # Test generator
        assert self.generator is not None
        assert isinstance(self.generator, AdvancedChatAppGenerator)

    def test_import_and_instantiate_generator(self):
        """Test importing and instantiating the generator class"""
        # This test will actually import the module and create an instance
        from core.app.apps.advanced_chat.app_generator import AdvancedChatAppGenerator

        # Create instance
        generator = AdvancedChatAppGenerator()

        # Test basic properties
        assert generator is not None
        assert hasattr(generator, 'generate')
        assert hasattr(generator, 'single_iteration_generate')
        assert hasattr(generator, 'single_loop_generate')

        # Test run_id property
        assert generator.run_id is None
        generator.run_id = "test_123"
        assert generator.run_id == "test_123"

    def test_validation_error_messages(self):
        """Test validation error messages are correct"""
        from core.app.apps.advanced_chat.app_generator import AdvancedChatAppGenerator

        generator = AdvancedChatAppGenerator()

        # Test query validation
        try:
            generator.generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                user=self.mock_user,
                args={},
                invoke_from=InvokeFrom.WEB_APP,
                streaming=False
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "query is required" in str(e)

        # Test query type validation
        try:
            generator.generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                user=self.mock_user,
                args={"query": 123},
                invoke_from=InvokeFrom.WEB_APP,
                streaming=False
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "query must be a string" in str(e)

    def test_single_iteration_validation_error_messages(self):
        """Test single iteration validation error messages"""
        from core.app.apps.advanced_chat.app_generator import AdvancedChatAppGenerator

        generator = AdvancedChatAppGenerator()

        # Test node_id validation
        try:
            generator.single_iteration_generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                node_id="",
                user=self.mock_user,
                args={"inputs": {"key": "value"}},
                streaming=False
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "node_id is required" in str(e)

        # Test inputs validation
        try:
            generator.single_iteration_generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                node_id="node_123",
                user=self.mock_user,
                args={},
                streaming=False
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "inputs is required" in str(e)

    def test_single_loop_validation_error_messages(self):
        """Test single loop validation error messages"""
        from core.app.apps.advanced_chat.app_generator import AdvancedChatAppGenerator

        generator = AdvancedChatAppGenerator()

        # Test node_id validation
        try:
            generator.single_loop_generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                node_id="",
                user=self.mock_user,
                args={"inputs": {"key": "value"}},
                streaming=False
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "node_id is required" in str(e)

        # Test inputs validation
        try:
            generator.single_loop_generate(
                app_model=self.mock_app,
                workflow=self.mock_workflow,
                node_id="node_123",
                user=self.mock_user,
                args={},
                streaming=False
            )
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "inputs is required" in str(e)
