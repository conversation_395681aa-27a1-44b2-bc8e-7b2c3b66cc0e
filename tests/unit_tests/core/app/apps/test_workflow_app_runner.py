import pytest
from unittest.mock import MagicMock, patch
from core.app.apps.workflow_app_runner import WorkflowBasedAppRunner
from core.workflow.nodes import NodeType
from models.workflow import Workflow

class TestWorkflowBasedAppRunner:
    @pytest.fixture
    def mock_queue_manager(self):
        return MagicMock()

    @pytest.fixture
    def runner(self, mock_queue_manager):
        return WorkflowBasedAppRunner(mock_queue_manager)

    def test_init_graph_success(self, runner):
        graph_config = {
            "nodes": [{"id": "node1", "data": {"type": "start"}}],
            "edges": [{"source": "node1", "target": "node2"}]
        }
        
        with patch('core.workflow.graph_engine.entities.graph.Graph.init') as mock_init:
            mock_init.return_value = MagicMock()
            graph = runner._init_graph(graph_config)
            assert graph is not None

    def test_init_graph_missing_nodes(self, runner):
        with pytest.raises(ValueError, match="nodes or edges not found in workflow graph"):
            runner._init_graph({"edges": []})

    def test_init_graph_invalid_nodes_type(self, runner):
        with pytest.raises(ValueError, match="nodes in workflow graph must be a list"):
            runner._init_graph({"nodes": {}, "edges": []})

    def test_get_graph_and_variable_pool_iteration(self, runner):
        mock_workflow = MagicMock()
        mock_workflow.graph_dict = {
            "nodes": [
                {"id": "node1", "data": {"type": "iteration", "iteration_id": "iter1"}},
                {"id": "iter1", "data": {
                    "type": "start",
                    "title": "Start Node",
                    "position": {"x": 0, "y": 0},
                    "selected": False
                }}
            ],
            "edges": [{"source": "iter1", "target": "node1"}]
        }
        mock_workflow.environment_variables = {}
        mock_workflow.tenant_id = "tenant1"

        with patch('core.workflow.graph_engine.entities.graph.Graph.init') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('core.workflow.nodes.node_mapping.NODE_TYPE_CLASSES_MAPPING', {"start": {"1": MagicMock()}}):
                with patch('core.workflow.nodes.start.entities.StartNodeData') as mock_start_node:
                    mock_instance = MagicMock()
                    mock_start_node.model_validate.return_value = mock_instance
                    graph, variable_pool = runner._get_graph_and_variable_pool_of_single_iteration(
                        mock_workflow, "iter1", {}
                    )
                    assert graph is not None
                    assert variable_pool is not None

    def test_handle_graph_started_event(self, runner):
        mock_entry = MagicMock()
        mock_entry.graph_engine.graph_runtime_state = MagicMock()
        mock_event = MagicMock(spec=runner._handle_event.__annotations__['event'])
        mock_event.__class__.__name__ = "GraphRunStartedEvent"

        with patch('core.app.apps.workflow_app_runner.QueueWorkflowStartedEvent') as mock_queue_event:
            runner._handle_event(mock_entry, mock_event)

    def test_get_workflow(self, runner):
        mock_app = MagicMock()
        mock_app.tenant_id = "tenant1"
        mock_app.id = "app1"
        
        mock_workflow = MagicMock()
        mock_workflow.id = "workflow1"

        with patch('models.workflow.db.session.query') as mock_query:
            mock_filter = mock_query.return_value.filter
            mock_filter.return_value.first.return_value = mock_workflow
            
            workflow = runner.get_workflow(mock_app, "workflow1")
            assert workflow == mock_workflow

    def test_publish_event(self, runner, mock_queue_manager):
        mock_event = MagicMock()
        runner._publish_event(mock_event)
        # Compare using the actual enum value instead of mock
        args, kwargs = mock_queue_manager.publish.call_args
        assert args[0] == mock_event
        assert args[1].value == 1  # APPLICATION_MANAGER enum value
        assert mock_queue_manager.publish.call_count == 1

    @pytest.mark.parametrize("event_type,queue_event_type", [
        ("GraphRunStartedEvent", "QueueWorkflowStartedEvent"),
        ("GraphRunSucceededEvent", "QueueWorkflowSucceededEvent"),
        ("NodeRunStartedEvent", "QueueNodeStartedEvent"),
        ("AgentLogEvent", "QueueAgentLogEvent")
    ])
    def test_handle_event_types(self, runner, event_type, queue_event_type):
        mock_entry = MagicMock()
        mock_entry.graph_engine = MagicMock()
        mock_entry.graph_engine.graph_runtime_state = MagicMock()
        
        # Create event mock with proper spec
        mock_event = MagicMock(spec=runner._handle_event.__annotations__['event'])
        mock_event.__class__.__name__ = event_type
        
        # Add required attributes based on event type
        if event_type == "GraphRunSucceededEvent":
            mock_event.outputs = {}
        elif event_type == "NodeRunStartedEvent":
            mock_event.id = "node1"
            mock_event.node_id = "node1"
            mock_event.node_type = "type1"
            mock_event.node_data = {}
            mock_event.route_node_state = MagicMock()
            mock_event.route_node_state.start_at = None
            mock_event.route_node_state.index = 0
        
        with patch(f'core.app.apps.workflow_app_runner.{queue_event_type}') as mock_queue_event:
            runner._handle_event(mock_entry, mock_event)
