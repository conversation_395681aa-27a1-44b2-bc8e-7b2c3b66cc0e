import pytest
from unittest.mock import Mock

from core.model_runtime.schema_validators.common_validator import CommonValidator
from core.model_runtime.entities.provider_entities import CredentialFormSchema, FormType, FormShowOnObject, FormOption
from core.model_runtime.entities.common_entities import I18nObject


class TestCommonValidator:
    """Test cases for CommonValidator"""

    def setup_method(self):
        """Setup test fixtures"""
        self.validator = CommonValidator()

    def test_validate_credential_form_schema_required_field_missing(self):
        """Test validation when required field is missing"""
        schema = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True
        )
        credentials = {}

        with pytest.raises(ValueError, match="Variable api_key is required"):
            self.validator._validate_credential_form_schema(schema, credentials)

    def test_validate_credential_form_schema_required_field_empty(self):
        """Test validation when required field is empty"""
        schema = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True
        )
        credentials = {"api_key": ""}

        with pytest.raises(ValueError, match="Variable api_key is required"):
            self.validator._validate_credential_form_schema(schema, credentials)

    def test_validate_credential_form_schema_optional_field_missing_with_default(self):
        """Test validation when optional field is missing but has default"""
        schema = CredentialFormSchema(
            variable="timeout",
            label=I18nObject(en_US="Timeout"),
            type=FormType.TEXT_INPUT,
            required=False,
            default="30"
        )
        credentials = {}

        result = self.validator._validate_credential_form_schema(schema, credentials)
        assert result == "30"

    def test_validate_credential_form_schema_optional_field_missing_no_default(self):
        """Test validation when optional field is missing and no default"""
        schema = CredentialFormSchema(
            variable="timeout",
            label=I18nObject(en_US="Timeout"),
            type=FormType.TEXT_INPUT,
            required=False
        )
        credentials = {}

        result = self.validator._validate_credential_form_schema(schema, credentials)
        assert result is None

    def test_validate_credential_form_schema_valid_string(self):
        """Test validation with valid string value"""
        schema = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True
        )
        credentials = {"api_key": "test_key_123"}

        result = self.validator._validate_credential_form_schema(schema, credentials)
        assert result == "test_key_123"

    def test_validate_credential_form_schema_max_length_exceeded(self):
        """Test validation when max length is exceeded"""
        schema = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True,
            max_length=10
        )
        credentials = {"api_key": "this_is_too_long"}

        with pytest.raises(ValueError, match="Variable api_key length should not greater than 10"):
            self.validator._validate_credential_form_schema(schema, credentials)

    def test_validate_credential_form_schema_max_length_valid(self):
        """Test validation when max length is within limit"""
        schema = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True,
            max_length=10
        )
        credentials = {"api_key": "short"}

        result = self.validator._validate_credential_form_schema(schema, credentials)
        assert result == "short"

    def test_validate_credential_form_schema_non_string_value(self):
        """Test validation with non-string value"""
        schema = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True
        )
        credentials = {"api_key": 123}

        with pytest.raises(ValueError, match="Variable api_key should be string"):
            self.validator._validate_credential_form_schema(schema, credentials)

    def test_validate_credential_form_schema_select_valid_option(self):
        """Test validation with valid select option"""
        options = [
            FormOption(label=I18nObject(en_US="Option 1"), value="opt1"),
            FormOption(label=I18nObject(en_US="Option 2"), value="opt2")
        ]
        schema = CredentialFormSchema(
            variable="model",
            label=I18nObject(en_US="Model"),
            type=FormType.SELECT,
            required=True,
            options=options
        )
        credentials = {"model": "opt1"}

        result = self.validator._validate_credential_form_schema(schema, credentials)
        assert result == "opt1"

    def test_validate_credential_form_schema_select_invalid_option(self):
        """Test validation with invalid select option"""
        options = [
            FormOption(label=I18nObject(en_US="Option 1"), value="opt1"),
            FormOption(label=I18nObject(en_US="Option 2"), value="opt2")
        ]
        schema = CredentialFormSchema(
            variable="model",
            label=I18nObject(en_US="Model"),
            type=FormType.SELECT,
            required=True,
            options=options
        )
        credentials = {"model": "invalid_option"}

        with pytest.raises(ValueError, match="Variable model is not in options"):
            self.validator._validate_credential_form_schema(schema, credentials)

    def test_validate_credential_form_schema_radio_valid_option(self):
        """Test validation with valid radio option"""
        options = [
            FormOption(label=I18nObject(en_US="Yes"), value="yes"),
            FormOption(label=I18nObject(en_US="No"), value="no")
        ]
        schema = CredentialFormSchema(
            variable="enabled",
            label=I18nObject(en_US="Enabled"),
            type=FormType.RADIO,
            required=True,
            options=options
        )
        credentials = {"enabled": "yes"}

        result = self.validator._validate_credential_form_schema(schema, credentials)
        assert result == "yes"

    def test_validate_credential_form_schema_switch_true(self):
        """Test validation with switch set to true"""
        schema = CredentialFormSchema(
            variable="debug",
            label=I18nObject(en_US="Debug Mode"),
            type=FormType.SWITCH,
            required=True
        )
        credentials = {"debug": "true"}

        result = self.validator._validate_credential_form_schema(schema, credentials)
        assert result is True

    def test_validate_credential_form_schema_switch_false(self):
        """Test validation with switch set to false"""
        schema = CredentialFormSchema(
            variable="debug",
            label=I18nObject(en_US="Debug Mode"),
            type=FormType.SWITCH,
            required=True
        )
        credentials = {"debug": "False"}

        result = self.validator._validate_credential_form_schema(schema, credentials)
        assert result is False

    def test_validate_credential_form_schema_switch_invalid(self):
        """Test validation with invalid switch value"""
        schema = CredentialFormSchema(
            variable="debug",
            label=I18nObject(en_US="Debug Mode"),
            type=FormType.SWITCH,
            required=True
        )
        credentials = {"debug": "maybe"}

        with pytest.raises(ValueError, match="Variable debug should be true or false"):
            self.validator._validate_credential_form_schema(schema, credentials)

    def test_validate_and_filter_credential_form_schemas_no_show_on(self):
        """Test filtering schemas without show_on conditions"""
        schema1 = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True
        )
        schema2 = CredentialFormSchema(
            variable="timeout",
            label=I18nObject(en_US="Timeout"),
            type=FormType.TEXT_INPUT,
            required=False,
            default="30"
        )

        schemas = [schema1, schema2]
        credentials = {"api_key": "test_key"}

        result = self.validator._validate_and_filter_credential_form_schemas(schemas, credentials)

        expected = {"api_key": "test_key", "timeout": "30"}
        assert result == expected

    def test_validate_and_filter_credential_form_schemas_with_show_on_match(self):
        """Test filtering schemas with matching show_on conditions"""
        show_on = [FormShowOnObject(variable="auth_type", value="api_key")]
        schema1 = CredentialFormSchema(
            variable="auth_type",
            label=I18nObject(en_US="Auth Type"),
            type=FormType.SELECT,
            required=True
        )
        schema2 = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True,
            show_on=show_on
        )

        schemas = [schema1, schema2]
        credentials = {"auth_type": "api_key", "api_key": "test_key"}

        result = self.validator._validate_and_filter_credential_form_schemas(schemas, credentials)

        expected = {"auth_type": "api_key", "api_key": "test_key"}
        assert result == expected

    def test_validate_and_filter_credential_form_schemas_with_show_on_no_match(self):
        """Test filtering schemas with non-matching show_on conditions"""
        show_on = [FormShowOnObject(variable="auth_type", value="api_key")]
        schema1 = CredentialFormSchema(
            variable="auth_type",
            label=I18nObject(en_US="Auth Type"),
            type=FormType.SELECT,
            required=True
        )
        schema2 = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True,
            show_on=show_on
        )

        schemas = [schema1, schema2]
        credentials = {"auth_type": "oauth", "api_key": "test_key"}

        result = self.validator._validate_and_filter_credential_form_schemas(schemas, credentials)

        # Only auth_type should be included since show_on condition for api_key doesn't match
        expected = {"auth_type": "oauth"}
        assert result == expected

    def test_validate_and_filter_credential_form_schemas_show_on_variable_missing(self):
        """Test filtering when show_on variable is missing from credentials"""
        show_on = [FormShowOnObject(variable="auth_type", value="api_key")]
        schema = CredentialFormSchema(
            variable="api_key",
            label=I18nObject(en_US="API Key"),
            type=FormType.TEXT_INPUT,
            required=True,
            show_on=show_on
        )

        schemas = [schema]
        credentials = {"api_key": "test_key"}  # auth_type is missing

        result = self.validator._validate_and_filter_credential_form_schemas(schemas, credentials)

        # Schema should be filtered out since show_on condition can't be evaluated
        assert result == {}
