import pytest
from unittest.mock import Mock

from core.model_runtime.schema_validators.common_validator import CommonValidator
from core.model_runtime.entities.provider_entities import CredentialFormSchema, FormType, FormOption, FormShowOnObject


class TestCommonValidator:
    """Test cases for CommonValidator"""

    @pytest.fixture
    def validator(self):
        """Create test validator instance"""
        return CommonValidator()

    @pytest.fixture
    def basic_credential_schema(self):
        """Create basic credential form schema"""
        return CredentialFormSchema(
            variable="api_key",
            label={"en_US": "API Key"},
            type=FormType.TEXT_INPUT,
            required=True,
            max_length=100
        )

    @pytest.fixture
    def optional_credential_schema(self):
        """Create optional credential form schema with default"""
        return CredentialFormSchema(
            variable="timeout",
            label={"en_US": "Timeout"},
            type=FormType.TEXT_INPUT,
            required=False,
            default="30"
        )

    @pytest.fixture
    def select_credential_schema(self):
        """Create select type credential form schema"""
        options = [
            FormOption(value="option1", label={"en_US": "Option 1"}),
            FormOption(value="option2", label={"en_US": "Option 2"})
        ]
        return CredentialFormSchema(
            variable="model_type",
            label={"en_US": "Model Type"},
            type=FormType.SELECT,
            required=True,
            options=options
        )

    @pytest.fixture
    def switch_credential_schema(self):
        """Create switch type credential form schema"""
        return CredentialFormSchema(
            variable="enable_feature",
            label={"en_US": "Enable Feature"},
            type=FormType.SWITCH,
            required=False,
            default="false"
        )

    @pytest.fixture
    def conditional_credential_schema(self):
        """Create conditional credential form schema with show_on"""
        show_on = [FormShowOnObject(variable="model_type", value="option1")]

        return CredentialFormSchema(
            variable="advanced_setting",
            label={"en_US": "Advanced Setting"},
            type=FormType.TEXT_INPUT,
            required=False,
            show_on=show_on
        )

    def test_validate_and_filter_credential_form_schemas_basic(self, validator, basic_credential_schema):
        """Test basic credential validation"""
        schemas = [basic_credential_schema]
        credentials = {"api_key": "test_key_123"}
        
        result = validator._validate_and_filter_credential_form_schemas(schemas, credentials)
        
        assert result == {"api_key": "test_key_123"}

    def test_validate_and_filter_credential_form_schemas_missing_required(self, validator, basic_credential_schema):
        """Test validation with missing required credential"""
        schemas = [basic_credential_schema]
        credentials = {}
        
        with pytest.raises(ValueError, match="Variable api_key is required"):
            validator._validate_and_filter_credential_form_schemas(schemas, credentials)

    def test_validate_and_filter_credential_form_schemas_optional_with_default(self, validator, optional_credential_schema):
        """Test validation with optional credential using default"""
        schemas = [optional_credential_schema]
        credentials = {}
        
        result = validator._validate_and_filter_credential_form_schemas(schemas, credentials)
        
        assert result == {"timeout": "30"}

    def test_validate_and_filter_credential_form_schemas_optional_provided(self, validator, optional_credential_schema):
        """Test validation with optional credential provided"""
        schemas = [optional_credential_schema]
        credentials = {"timeout": "60"}
        
        result = validator._validate_and_filter_credential_form_schemas(schemas, credentials)
        
        assert result == {"timeout": "60"}

    def test_validate_and_filter_credential_form_schemas_conditional_show(self, validator, conditional_credential_schema):
        """Test validation with conditional schema that should show"""
        schemas = [conditional_credential_schema]
        credentials = {"model_type": "option1", "advanced_setting": "test_value"}
        
        result = validator._validate_and_filter_credential_form_schemas(schemas, credentials)
        
        assert result == {"advanced_setting": "test_value"}

    def test_validate_and_filter_credential_form_schemas_conditional_hide(self, validator, conditional_credential_schema):
        """Test validation with conditional schema that should hide"""
        schemas = [conditional_credential_schema]
        credentials = {"model_type": "option2", "advanced_setting": "test_value"}
        
        result = validator._validate_and_filter_credential_form_schemas(schemas, credentials)
        
        assert result == {}

    def test_validate_and_filter_credential_form_schemas_conditional_missing_dependency(self, validator, conditional_credential_schema):
        """Test validation with conditional schema missing dependency"""
        schemas = [conditional_credential_schema]
        credentials = {"advanced_setting": "test_value"}
        
        result = validator._validate_and_filter_credential_form_schemas(schemas, credentials)
        
        assert result == {}

    def test_validate_credential_form_schema_valid_string(self, validator, basic_credential_schema):
        """Test validation of valid string credential"""
        credentials = {"api_key": "valid_key"}
        
        result = validator._validate_credential_form_schema(basic_credential_schema, credentials)
        
        assert result == "valid_key"

    def test_validate_credential_form_schema_max_length_exceeded(self, validator, basic_credential_schema):
        """Test validation with max length exceeded"""
        credentials = {"api_key": "a" * 101}  # Exceeds max_length of 100
        
        with pytest.raises(ValueError, match="length should not greater than 100"):
            validator._validate_credential_form_schema(basic_credential_schema, credentials)

    def test_validate_credential_form_schema_select_valid_option(self, validator, select_credential_schema):
        """Test validation of select type with valid option"""
        credentials = {"model_type": "option1"}
        
        result = validator._validate_credential_form_schema(select_credential_schema, credentials)
        
        assert result == "option1"

    def test_validate_credential_form_schema_select_invalid_option(self, validator, select_credential_schema):
        """Test validation of select type with invalid option"""
        credentials = {"model_type": "invalid_option"}
        
        with pytest.raises(ValueError, match="is not in options"):
            validator._validate_credential_form_schema(select_credential_schema, credentials)

    def test_validate_credential_form_schema_switch_true(self, validator, switch_credential_schema):
        """Test validation of switch type with true value"""
        credentials = {"enable_feature": "true"}
        
        result = validator._validate_credential_form_schema(switch_credential_schema, credentials)
        
        assert result is True

    def test_validate_credential_form_schema_switch_false(self, validator, switch_credential_schema):
        """Test validation of switch type with false value"""
        credentials = {"enable_feature": "false"}
        
        result = validator._validate_credential_form_schema(switch_credential_schema, credentials)
        
        assert result is False

    def test_validate_credential_form_schema_switch_case_insensitive(self, validator, switch_credential_schema):
        """Test validation of switch type with case insensitive values"""
        credentials = {"enable_feature": "TRUE"}
        
        result = validator._validate_credential_form_schema(switch_credential_schema, credentials)
        
        assert result is True

    def test_validate_credential_form_schema_switch_invalid(self, validator, switch_credential_schema):
        """Test validation of switch type with invalid value"""
        credentials = {"enable_feature": "maybe"}
        
        with pytest.raises(ValueError, match="should be true or false"):
            validator._validate_credential_form_schema(switch_credential_schema, credentials)

    def test_validate_credential_form_schema_empty_value_required(self, validator, basic_credential_schema):
        """Test validation with empty value for required field"""
        credentials = {"api_key": ""}
        
        with pytest.raises(ValueError, match="Variable api_key is required"):
            validator._validate_credential_form_schema(basic_credential_schema, credentials)

    def test_validate_credential_form_schema_empty_value_optional_with_default(self, validator, optional_credential_schema):
        """Test validation with empty value for optional field with default"""
        credentials = {"timeout": ""}
        
        result = validator._validate_credential_form_schema(optional_credential_schema, credentials)
        
        assert result == "30"

    def test_validate_credential_form_schema_optional_no_default(self, validator):
        """Test validation with optional field without default"""
        schema = CredentialFormSchema(
            variable="optional_field",
            label={"en_US": "Optional Field"},
            type=FormType.TEXT_INPUT,
            required=False
        )
        credentials = {}
        
        result = validator._validate_credential_form_schema(schema, credentials)
        
        assert result is None

    def test_validate_credential_form_schema_radio_type(self, validator):
        """Test validation of radio type credential"""
        options = [
            FormOption(value="radio1", label={"en_US": "Radio 1"}),
            FormOption(value="radio2", label={"en_US": "Radio 2"})
        ]
        schema = CredentialFormSchema(
            variable="radio_choice",
            label={"en_US": "Radio Choice"},
            type=FormType.RADIO,
            required=True,
            options=options
        )
        credentials = {"radio_choice": "radio1"}
        
        result = validator._validate_credential_form_schema(schema, credentials)
        
        assert result == "radio1"

    def test_validate_credential_form_schema_no_max_length(self, validator):
        """Test validation without max_length restriction"""
        schema = CredentialFormSchema(
            variable="unlimited_field",
            label={"en_US": "Unlimited Field"},
            type=FormType.TEXT_INPUT,
            required=True
        )
        credentials = {"unlimited_field": "a" * 1000}  # Very long string
        
        result = validator._validate_credential_form_schema(schema, credentials)
        
        assert result == "a" * 1000

    def test_validate_credential_form_schema_multiple_show_on_conditions(self, validator):
        """Test validation with multiple show_on conditions"""
        show_on = [
            FormShowOnObject(variable="condition1", value="value1"),
            FormShowOnObject(variable="condition2", value="value2")
        ]

        schema = CredentialFormSchema(
            variable="conditional_field",
            label={"en_US": "Conditional Field"},
            type=FormType.TEXT_INPUT,
            required=False,
            show_on=show_on
        )
        
        # All conditions match
        schemas = [schema]
        credentials = {
            "condition1": "value1",
            "condition2": "value2",
            "conditional_field": "test_value"
        }
        
        result = validator._validate_and_filter_credential_form_schemas(schemas, credentials)
        
        assert result == {"conditional_field": "test_value"}

    def test_validate_credential_form_schema_partial_show_on_conditions(self, validator):
        """Test validation with partial show_on conditions"""
        show_on = [
            FormShowOnObject(variable="condition1", value="value1"),
            FormShowOnObject(variable="condition2", value="value2")
        ]

        schema = CredentialFormSchema(
            variable="conditional_field",
            label={"en_US": "Conditional Field"},
            type=FormType.TEXT_INPUT,
            required=False,
            show_on=show_on
        )
        
        # Only one condition matches
        schemas = [schema]
        credentials = {
            "condition1": "value1",
            "condition2": "wrong_value",
            "conditional_field": "test_value"
        }
        
        result = validator._validate_and_filter_credential_form_schemas(schemas, credentials)
        
        assert result == {}
