import pytest
from unittest.mock import patch, MagicMock
from pydantic import BaseModel
from core.model_runtime.utils.helper import dump_model

class TestModel(BaseModel):
    name: str = "test"
    value: int = 123

def test_dump_model_v2():
    # Test with pydantic v2 style
    mock_model = MagicMock()
    mock_model.model_dump.return_value = {"name": "test", "value": 123}
    
    result = dump_model(mock_model)
    assert result == {"name": "test", "value": 123}
    mock_model.model_dump.assert_called_once()
def test_dump_model_with_real_model():
    # Test with actual model instance
    model = TestModel(name="real", value=456)
    result = dump_model(model)
    
    # Check based on actual pydantic version
    if hasattr(model, "model_dump"):
        expected = model.model_dump()
    else:
        expected = {"name": "real", "value": 456}
    
    assert result == expected

def test_dump_model_with_complex_model():
    # Test with nested model
    class NestedModel(BaseModel):
        items: list[str] = ["a", "b"]
    
    class ComplexModel(BaseModel):
        nested: NestedModel = NestedModel()
        count: int = 2
    
    model = ComplexModel()
    result = dump_model(model)
    
    if hasattr(model, "model_dump"):
        expected = model.model_dump()
    else:
        expected = {
            "nested": {"items": ["a", "b"]},
            "count": 2
        }
    
    assert result == expected
