import pytest
from unittest.mock import Mock, patch, MagicMock
from io import String<PERSON>
import sys
from datetime import datetime

from core.workflow.callbacks.workflow_logging_callback import WorkflowLoggingCallback, _TEXT_COLOR_MAPPING
from core.workflow.graph_engine.entities.event import (
    GraphRunStartedEvent,
    GraphRunSucceededEvent,
    GraphRunPartialSucceededEvent,
    GraphRunFailedEvent,
    NodeRunStartedEvent,
    NodeRunSucceededEvent,
    NodeRunFailedEvent,
    NodeRunStreamChunkEvent,
    ParallelBranchRunStartedEvent,
    ParallelBranchRunSucceededEvent,
    ParallelBranchRunFailedEvent,
    IterationRunStartedEvent,
    IterationRunNextEvent,
    IterationRunSucceededEvent,
    IterationRunFailedEvent,
    LoopRunStartedEvent,
    LoopRunNextEvent,
    LoopRunSucceededEvent,
    LoopRunFailedEvent
)
from core.workflow.nodes.enums import NodeType
from core.workflow.nodes.base import BaseNodeData
from core.workflow.graph_engine.entities.runtime_route_state import RouteNodeState
from core.workflow.entities.node_entities import NodeRunResult


class TestWorkflowLoggingCallback:
    """Test cases for WorkflowLoggingCallback"""

    @pytest.fixture
    def callback(self):
        """Create test callback instance"""
        return WorkflowLoggingCallback()

    @pytest.fixture
    def mock_node_data(self):
        """Create mock node data"""
        return Mock(spec=BaseNodeData, title="Test Node")

    @pytest.fixture
    def mock_route_node_state(self):
        """Create mock route node state"""
        mock_state = Mock(spec=RouteNodeState)
        mock_state.node_id = "test_node_id"
        mock_state.node_run_result = Mock(spec=NodeRunResult)
        mock_state.node_run_result.metadata = {"test": "metadata"}
        mock_state.node_run_result.inputs = {"input1": "value1"}
        mock_state.node_run_result.outputs = {"output1": "value1"}
        mock_state.node_run_result.process_data = {"process": "data"}
        mock_state.node_run_result.execution_metadata = {"exec": "meta"}
        return mock_state

    def test_init(self, callback):
        """Test callback initialization"""
        assert callback.current_node_id is None

    @patch('builtins.print')
    def test_on_event_graph_run_started(self, mock_print, callback):
        """Test handling GraphRunStartedEvent"""
        event = GraphRunStartedEvent()
        
        callback.on_event(event)
        
        mock_print.assert_called_once()
        call_args = mock_print.call_args[0][0]
        assert "[GraphRunStartedEvent]" in call_args
        assert "\u001b[38;5;200m" in call_args  # pink color

    @patch('builtins.print')
    def test_on_event_graph_run_succeeded(self, mock_print, callback):
        """Test handling GraphRunSucceededEvent"""
        event = GraphRunSucceededEvent()
        
        callback.on_event(event)
        
        mock_print.assert_called_once()
        call_args = mock_print.call_args[0][0]
        assert "[GraphRunSucceededEvent]" in call_args
        assert "\u001b[32;1m" in call_args  # green color

    @patch('builtins.print')
    def test_on_event_graph_run_partial_succeeded(self, mock_print, callback):
        """Test handling GraphRunPartialSucceededEvent"""
        event = GraphRunPartialSucceededEvent(exceptions_count=1)

        callback.on_event(event)

        mock_print.assert_called_once()
        call_args = mock_print.call_args[0][0]
        assert "[GraphRunPartialSucceededEvent]" in call_args
        assert "\u001b[38;5;200m" in call_args  # pink color

    @patch('builtins.print')
    def test_on_event_graph_run_failed(self, mock_print, callback):
        """Test handling GraphRunFailedEvent"""
        event = GraphRunFailedEvent(error="Test error message")
        
        callback.on_event(event)
        
        mock_print.assert_called_once()
        call_args = mock_print.call_args[0][0]
        assert "[GraphRunFailedEvent]" in call_args
        assert "Test error message" in call_args
        assert "\u001b[31;1m" in call_args  # red color

    @patch('builtins.print')
    def test_on_event_node_run_started(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test handling NodeRunStartedEvent"""
        event = NodeRunStartedEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state
        )
        
        callback.on_event(event)
        
        # Should print multiple lines for node started event
        assert mock_print.call_count >= 3
        
        # Check that node information is printed
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[NodeRunStartedEvent]" in printed_text
        assert "test_node" in printed_text
        assert "Test Node" in printed_text

    @patch('builtins.print')
    def test_on_event_node_run_succeeded(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test handling NodeRunSucceededEvent"""
        event = NodeRunSucceededEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state
        )
        
        callback.on_event(event)
        
        # Should print node succeeded information
        assert mock_print.call_count >= 1
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[NodeRunSucceededEvent]" in printed_text

    @patch('builtins.print')
    def test_on_event_node_run_failed(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test handling NodeRunFailedEvent"""
        event = NodeRunFailedEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state,
            error="Test error"
        )

        callback.on_event(event)

        # Should print node failed information
        assert mock_print.call_count >= 1
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[NodeRunFailedEvent]" in printed_text

    @patch('builtins.print')
    def test_on_event_node_run_stream_chunk(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test handling NodeRunStreamChunkEvent"""
        event = NodeRunStreamChunkEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state,
            chunk_content="Test chunk content"
        )
        
        callback.on_event(event)
        
        # Should print chunk content
        assert mock_print.call_count >= 1
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "Test chunk content" in printed_text

    @patch('builtins.print')
    def test_on_event_parallel_branch_started(self, mock_print, callback):
        """Test handling ParallelBranchRunStartedEvent"""
        event = ParallelBranchRunStartedEvent(
            parallel_id="parallel_1",
            parallel_start_node_id="start_node_1"
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[ParallelBranchRunStartedEvent]" in printed_text
        assert "parallel_1" in printed_text

    @patch('builtins.print')
    def test_on_event_parallel_branch_succeeded(self, mock_print, callback):
        """Test handling ParallelBranchRunSucceededEvent"""
        event = ParallelBranchRunSucceededEvent(
            parallel_id="parallel_1",
            parallel_start_node_id="start_node_1"
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[ParallelBranchRunSucceededEvent]" in printed_text

    @patch('builtins.print')
    def test_on_event_parallel_branch_failed(self, mock_print, callback):
        """Test handling ParallelBranchRunFailedEvent"""
        event = ParallelBranchRunFailedEvent(
            parallel_id="parallel_1",
            parallel_start_node_id="start_node_1",
            error="Parallel execution failed"
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[ParallelBranchRunFailedEvent]" in printed_text
        assert "Parallel execution failed" in printed_text

    @patch('builtins.print')
    def test_on_event_iteration_started(self, mock_print, callback, mock_node_data):
        """Test handling IterationRunStartedEvent"""
        event = IterationRunStartedEvent(
            iteration_id="iter_1",
            iteration_node_id="iter_node_1",
            iteration_node_type=NodeType.ITERATION,
            iteration_node_data=mock_node_data,
            start_at=datetime.now(),
            inputs={}
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[IterationRunStartedEvent]" in printed_text
        assert "iter_1" in printed_text

    @patch('builtins.print')
    def test_on_event_iteration_next(self, mock_print, callback, mock_node_data):
        """Test handling IterationRunNextEvent"""
        event = IterationRunNextEvent(
            iteration_id="iter_1",
            iteration_node_id="iter_node_1",
            iteration_node_type=NodeType.ITERATION,
            iteration_node_data=mock_node_data,
            index=1,
            pre_iteration_output={}
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[IterationRunNextEvent]" in printed_text
        assert "iter_1" in printed_text

    @patch('builtins.print')
    def test_on_event_iteration_succeeded(self, mock_print, callback, mock_node_data):
        """Test handling IterationRunSucceededEvent"""
        event = IterationRunSucceededEvent(
            iteration_id="iter_1",
            iteration_node_id="iter_node_1",
            iteration_node_type=NodeType.ITERATION,
            iteration_node_data=mock_node_data,
            start_at=datetime.now(),
            inputs={},
            outputs={},
            steps=3
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[IterationRunSucceededEvent]" in printed_text

    @patch('builtins.print')
    def test_on_event_iteration_failed(self, mock_print, callback, mock_node_data):
        """Test handling IterationRunFailedEvent"""
        event = IterationRunFailedEvent(
            iteration_id="iter_1",
            iteration_node_id="iter_node_1",
            iteration_node_type=NodeType.ITERATION,
            iteration_node_data=mock_node_data,
            start_at=datetime.now(),
            inputs={},
            steps=2,
            error="Iteration failed"
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[IterationRunFailedEvent]" in printed_text

    @patch('builtins.print')
    def test_on_event_loop_started(self, mock_print, callback, mock_node_data):
        """Test handling LoopRunStartedEvent"""
        event = LoopRunStartedEvent(
            loop_id="loop_1",
            loop_node_id="loop_node_1",
            loop_node_type=NodeType.LOOP,
            loop_node_data=mock_node_data,
            start_at=datetime.now(),
            inputs={}
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[LoopRunStartedEvent]" in printed_text
        assert "loop_1" in printed_text

    @patch('builtins.print')
    def test_on_event_loop_next(self, mock_print, callback, mock_node_data):
        """Test handling LoopRunNextEvent"""
        event = LoopRunNextEvent(
            loop_id="loop_1",
            loop_node_id="loop_node_1",
            loop_node_type=NodeType.LOOP,
            loop_node_data=mock_node_data,
            index=2,
            pre_loop_output={}
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[LoopRunNextEvent]" in printed_text
        assert "loop_1" in printed_text

    @patch('builtins.print')
    def test_on_event_loop_succeeded(self, mock_print, callback, mock_node_data):
        """Test handling LoopRunSucceededEvent"""
        event = LoopRunSucceededEvent(
            loop_id="loop_1",
            loop_node_id="loop_node_1",
            loop_node_type=NodeType.LOOP,
            loop_node_data=mock_node_data,
            start_at=datetime.now(),
            inputs={},
            outputs={},
            steps=5
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[LoopRunSucceededEvent]" in printed_text

    @patch('builtins.print')
    def test_on_event_loop_failed(self, mock_print, callback, mock_node_data):
        """Test handling LoopRunFailedEvent"""
        event = LoopRunFailedEvent(
            loop_id="loop_1",
            loop_node_id="loop_node_1",
            loop_node_type=NodeType.LOOP,
            loop_node_data=mock_node_data,
            start_at=datetime.now(),
            inputs={},
            steps=3,
            error="Loop execution failed"
        )
        
        callback.on_event(event)
        
        mock_print.assert_called()
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[LoopRunFailedEvent]" in printed_text

    @patch('builtins.print')
    def test_on_event_unknown_event(self, mock_print, callback):
        """Test handling unknown event type"""
        class UnknownEvent:
            pass
        
        event = UnknownEvent()
        
        callback.on_event(event)
        
        mock_print.assert_called_once()
        call_args = mock_print.call_args[0][0]
        assert "[UnknownEvent]" in call_args
        assert "\u001b[36;1m" in call_args  # blue color

    @patch('builtins.print')
    def test_on_workflow_node_execute_started(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test on_workflow_node_execute_started method"""
        event = NodeRunStartedEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state
        )

        callback.on_workflow_node_execute_started(event)

        # Should print multiple lines
        assert mock_print.call_count >= 4
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[NodeRunStartedEvent]" in printed_text
        assert "Node ID: test_node" in printed_text
        assert "Node Title: Test Node" in printed_text
        assert "Type: llm" in printed_text

    @patch('builtins.print')
    def test_on_workflow_node_execute_succeeded(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test on_workflow_node_execute_succeeded method"""
        event = NodeRunSucceededEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state
        )

        callback.on_workflow_node_execute_succeeded(event)

        # Should print node succeeded information
        assert mock_print.call_count >= 1
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[NodeRunSucceededEvent]" in printed_text
        assert "Node ID: test_node" in printed_text

    @patch('builtins.print')
    def test_on_workflow_node_execute_failed(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test on_workflow_node_execute_failed method"""
        event = NodeRunFailedEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state
        )

        callback.on_workflow_node_execute_failed(event)

        # Should print node failed information
        assert mock_print.call_count >= 1
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[NodeRunFailedEvent]" in printed_text
        assert "Node ID: test_node" in printed_text

    @patch('builtins.print')
    def test_on_node_text_chunk_new_node(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test on_node_text_chunk with new node"""
        event = NodeRunStreamChunkEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state,
            chunk_content="Test chunk"
        )

        callback.on_node_text_chunk(event)

        # Should print node info and chunk content
        assert mock_print.call_count >= 3
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[NodeRunStreamChunkEvent]" in printed_text
        assert "Node ID: test_node_id" in printed_text
        assert "Test chunk" in printed_text

    @patch('builtins.print')
    def test_on_node_text_chunk_same_node(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test on_node_text_chunk with same node (should not print header again)"""
        callback.current_node_id = "test_node_id"

        event = NodeRunStreamChunkEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state,
            chunk_content="Test chunk"
        )

        callback.on_node_text_chunk(event)

        # Should only print chunk content, not header
        mock_print.assert_called_once()
        call_args = mock_print.call_args[0][0]
        assert call_args == "Test chunk"

    @patch('builtins.print')
    def test_on_node_text_chunk_no_metadata(self, mock_print, callback, mock_node_data, mock_route_node_state):
        """Test on_node_text_chunk with no metadata"""
        mock_route_node_state.node_run_result.metadata = None

        event = NodeRunStreamChunkEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state,
            chunk_content="Test chunk"
        )

        callback.on_node_text_chunk(event)

        # Should print node info and chunk content
        assert mock_print.call_count >= 3
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "Metadata: " in printed_text

    @patch('builtins.print')
    def test_on_workflow_parallel_started(self, mock_print, callback):
        """Test on_workflow_parallel_started method"""
        event = ParallelBranchRunStartedEvent(
            parallel_id="parallel_1",
            parallel_start_node_id="start_node_1",
            in_iteration_id="iter_1",
            in_loop_id="loop_1"
        )

        callback.on_workflow_parallel_started(event)

        assert mock_print.call_count >= 4
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[ParallelBranchRunStartedEvent]" in printed_text
        assert "Parallel ID: parallel_1" in printed_text
        assert "Branch ID: start_node_1" in printed_text
        assert "Iteration ID: iter_1" in printed_text
        assert "Loop ID: loop_1" in printed_text

    @patch('builtins.print')
    def test_on_workflow_parallel_completed_succeeded(self, mock_print, callback):
        """Test on_workflow_parallel_completed with succeeded event"""
        event = ParallelBranchRunSucceededEvent(
            parallel_id="parallel_1",
            parallel_start_node_id="start_node_1"
        )

        callback.on_workflow_parallel_completed(event)

        assert mock_print.call_count >= 3
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[ParallelBranchRunSucceededEvent]" in printed_text
        assert "Parallel ID: parallel_1" in printed_text

    @patch('builtins.print')
    def test_on_workflow_parallel_completed_failed(self, mock_print, callback):
        """Test on_workflow_parallel_completed with failed event"""
        event = ParallelBranchRunFailedEvent(
            parallel_id="parallel_1",
            parallel_start_node_id="start_node_1",
            error="Parallel execution failed"
        )

        callback.on_workflow_parallel_completed(event)

        assert mock_print.call_count >= 4
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[ParallelBranchRunFailedEvent]" in printed_text
        assert "Error: Parallel execution failed" in printed_text

    @patch('builtins.print')
    def test_on_workflow_iteration_started(self, mock_print, callback, mock_node_data):
        """Test on_workflow_iteration_started method"""
        event = IterationRunStartedEvent(
            iteration_id="iter_1",
            iteration_node_id="iter_node_1",
            iteration_node_type=NodeType.ITERATION,
            iteration_node_data=mock_node_data,
            start_at=Mock(),
            inputs={}
        )

        callback.on_workflow_iteration_started(event)

        assert mock_print.call_count >= 2
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[IterationRunStartedEvent]" in printed_text
        assert "Iteration Node ID: iter_1" in printed_text

    @patch('builtins.print')
    def test_on_workflow_iteration_next(self, mock_print, callback, mock_node_data):
        """Test on_workflow_iteration_next method"""
        event = IterationRunNextEvent(
            iteration_id="iter_1",
            iteration_node_id="iter_node_1",
            iteration_node_type=NodeType.ITERATION,
            iteration_node_data=mock_node_data,
            index=2,
            pre_iteration_output={}
        )

        callback.on_workflow_iteration_next(event)

        assert mock_print.call_count >= 3
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[IterationRunNextEvent]" in printed_text
        assert "Iteration Node ID: iter_1" in printed_text
        assert "Iteration Index: 2" in printed_text

    @patch('builtins.print')
    def test_on_workflow_iteration_completed_succeeded(self, mock_print, callback, mock_node_data):
        """Test on_workflow_iteration_completed with succeeded event"""
        event = IterationRunSucceededEvent(
            iteration_id="iter_1",
            iteration_node_id="iter_node_1",
            iteration_node_type=NodeType.ITERATION,
            iteration_node_data=mock_node_data,
            start_at=Mock(),
            inputs={},
            outputs={},
            steps=3
        )

        callback.on_workflow_iteration_completed(event)

        assert mock_print.call_count >= 2
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[IterationRunSucceededEvent]" in printed_text
        assert "Node ID: iter_1" in printed_text

    @patch('builtins.print')
    def test_on_workflow_iteration_completed_failed(self, mock_print, callback, mock_node_data):
        """Test on_workflow_iteration_completed with failed event"""
        event = IterationRunFailedEvent(
            iteration_id="iter_1",
            iteration_node_id="iter_node_1",
            iteration_node_type=NodeType.ITERATION,
            iteration_node_data=mock_node_data,
            start_at=Mock(),
            inputs={},
            steps=2,
            error="Iteration failed"
        )

        callback.on_workflow_iteration_completed(event)

        assert mock_print.call_count >= 2
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[IterationRunFailedEvent]" in printed_text
        assert "Node ID: iter_1" in printed_text

    @patch('builtins.print')
    def test_on_workflow_loop_started(self, mock_print, callback, mock_node_data):
        """Test on_workflow_loop_started method"""
        event = LoopRunStartedEvent(
            loop_id="loop_1",
            loop_node_id="loop_node_1",
            loop_node_type=NodeType.LOOP,
            loop_node_data=mock_node_data,
            start_at=Mock(),
            inputs={}
        )

        callback.on_workflow_loop_started(event)

        assert mock_print.call_count >= 2
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[LoopRunStartedEvent]" in printed_text
        assert "Loop Node ID: loop_1" in printed_text

    @patch('builtins.print')
    def test_on_workflow_loop_next(self, mock_print, callback, mock_node_data):
        """Test on_workflow_loop_next method"""
        event = LoopRunNextEvent(
            loop_id="loop_1",
            loop_node_id="loop_node_1",
            loop_node_type=NodeType.LOOP,
            loop_node_data=mock_node_data,
            index=3,
            pre_loop_output={}
        )

        callback.on_workflow_loop_next(event)

        assert mock_print.call_count >= 3
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[LoopRunNextEvent]" in printed_text
        assert "Loop Node ID: loop_1" in printed_text
        assert "Loop Index: 3" in printed_text

    @patch('builtins.print')
    def test_on_workflow_loop_completed_succeeded(self, mock_print, callback, mock_node_data):
        """Test on_workflow_loop_completed with succeeded event"""
        event = LoopRunSucceededEvent(
            loop_id="loop_1",
            loop_node_id="loop_node_1",
            loop_node_type=NodeType.LOOP,
            loop_node_data=mock_node_data,
            start_at=Mock(),
            inputs={},
            outputs={},
            steps=5
        )

        callback.on_workflow_loop_completed(event)

        assert mock_print.call_count >= 2
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[LoopRunSucceededEvent]" in printed_text
        assert "Node ID: loop_1" in printed_text

    @patch('builtins.print')
    def test_on_workflow_loop_completed_failed(self, mock_print, callback, mock_node_data):
        """Test on_workflow_loop_completed with failed event"""
        event = LoopRunFailedEvent(
            loop_id="loop_1",
            loop_node_id="loop_node_1",
            loop_node_type=NodeType.LOOP,
            loop_node_data=mock_node_data,
            start_at=Mock(),
            inputs={},
            steps=3,
            error="Loop execution failed"
        )

        callback.on_workflow_loop_completed(event)

        assert mock_print.call_count >= 2
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[LoopRunFailedEvent]" in printed_text
        assert "Node ID: loop_1" in printed_text

    def test_print_text_no_color(self, callback):
        """Test print_text without color"""
        with patch('builtins.print') as mock_print:
            callback.print_text("Test message")

            mock_print.assert_called_once_with("Test message", end="\n")

    def test_print_text_with_color(self, callback):
        """Test print_text with color"""
        with patch('builtins.print') as mock_print:
            callback.print_text("Test message", color="blue")

            mock_print.assert_called_once()
            call_args = mock_print.call_args[0][0]
            assert "\u001b[36;1m" in call_args  # blue color
            assert "Test message" in call_args
            assert "\u001b[0m" in call_args  # reset

    def test_print_text_with_custom_end(self, callback):
        """Test print_text with custom end character"""
        with patch('builtins.print') as mock_print:
            callback.print_text("Test message", end="")

            mock_print.assert_called_once_with("Test message", end="")

    def test_get_colored_text(self, callback):
        """Test _get_colored_text method"""
        result = callback._get_colored_text("Test message", "red")

        assert "\u001b[31;1m" in result  # red color
        assert "Test message" in result
        assert "\u001b[0m" in result  # reset

    def test_get_colored_text_all_colors(self, callback):
        """Test _get_colored_text with all available colors"""
        for color, color_code in _TEXT_COLOR_MAPPING.items():
            result = callback._get_colored_text("Test", color)
            assert f"\u001b[{color_code}m" in result
            assert "Test" in result
            assert "\u001b[0m" in result

    @patch('builtins.print')
    def test_on_node_text_chunk_no_node_run_result(self, mock_print, callback, mock_node_data):
        """Test on_node_text_chunk with no node_run_result"""
        mock_route_node_state = Mock(spec=RouteNodeState)
        mock_route_node_state.node_id = "test_node_id"
        mock_route_node_state.node_run_result = None

        event = NodeRunStreamChunkEvent(
            id="test_id",
            node_id="test_node",
            node_type=NodeType.LLM,
            node_data=mock_node_data,
            route_node_state=mock_route_node_state,
            chunk_content="Test chunk"
        )

        callback.on_node_text_chunk(event)

        # Should print node info and chunk content, but no metadata
        assert mock_print.call_count >= 2
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[NodeRunStreamChunkEvent]" in printed_text
        assert "Node ID: test_node_id" in printed_text
        assert "Test chunk" in printed_text
        # Should not contain metadata line
        assert "Metadata:" not in printed_text

    @patch('builtins.print')
    def test_on_workflow_parallel_started_minimal(self, mock_print, callback):
        """Test on_workflow_parallel_started with minimal event data"""
        event = ParallelBranchRunStartedEvent(
            parallel_id="parallel_1",
            parallel_start_node_id="start_node_1"
            # No in_iteration_id or in_loop_id
        )

        callback.on_workflow_parallel_started(event)

        assert mock_print.call_count >= 3
        printed_text = "".join([call[0][0] for call in mock_print.call_args_list])
        assert "[ParallelBranchRunStartedEvent]" in printed_text
        assert "Parallel ID: parallel_1" in printed_text
        assert "Branch ID: start_node_1" in printed_text
        # Should not contain iteration or loop info
        assert "Iteration ID:" not in printed_text
        assert "Loop ID:" not in printed_text
