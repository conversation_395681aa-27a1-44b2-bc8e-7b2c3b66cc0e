import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from typing import Optional

from core.workflow.nodes.parameter_extractor.parameter_extractor_node import (
    ParameterExtractorNode,
    extract_json
)
from core.workflow.nodes.parameter_extractor.entities import ParameterExtractorNodeData, ParameterConfig
from core.workflow.nodes.parameter_extractor.exc import (
    InvalidModelTypeError,
    InvalidInvokeResultError,
    InvalidTextContentTypeError,
    ParameterExtractorNodeError,
    RequiredParameterMissingError,
    InvalidStringValueError,
    InvalidNumberValueError,
    InvalidBoolValueError,
    InvalidSelectValueError,
    InvalidArrayValueError
)
from core.workflow.nodes.llm import ModelConfig
from core.workflow.entities.node_entities import NodeRunResult
from core.workflow.entities.variable_pool import VariablePool
from core.model_runtime.entities.llm_entities import LLMR<PERSON>ult, LLMUsage
from core.model_runtime.entities.message_entities import AssistantPromptMessage, PromptMessage
from core.model_runtime.model_providers.__base.large_language_model import LargeLanguageModel
from core.model_runtime.entities.model_entities import ModelFeature
from core.variables import StringSegment
from models.workflow import WorkflowNodeExecutionStatus


class TestExtractJson:
    """Test cases for extract_json function"""

    def test_extract_json_simple_object(self):
        """Test extracting simple JSON object"""
        text = '{"name": "John", "age": 30} extra text'
        result = extract_json(text)
        assert result == '{"name": "John", "age": 30}'

    def test_extract_json_nested_object(self):
        """Test extracting nested JSON object"""
        text = '{"user": {"name": "John", "details": {"age": 30}}} extra'
        result = extract_json(text)
        assert result == '{"user": {"name": "John", "details": {"age": 30}}}'

    def test_extract_json_array(self):
        """Test extracting JSON array"""
        text = '[{"name": "John"}, {"name": "Jane"}] extra'
        result = extract_json(text)
        assert result == '[{"name": "John"}, {"name": "Jane"}]'

    def test_extract_json_mixed_brackets(self):
        """Test extracting JSON with mixed brackets"""
        text = '{"items": [1, 2, 3], "nested": {"key": "value"}} extra'
        result = extract_json(text)
        assert result == '{"items": [1, 2, 3], "nested": {"key": "value"}}'

    def test_extract_json_no_valid_json(self):
        """Test with no valid JSON"""
        text = 'no json here'
        result = extract_json(text)
        assert result is None

    def test_extract_json_incomplete_json(self):
        """Test with incomplete JSON"""
        text = '{"name": "John", "age":'
        result = extract_json(text)
        assert result is None

    def test_extract_json_mismatched_brackets(self):
        """Test with mismatched brackets"""
        text = '{"name": "John"} extra'
        result = extract_json(text)
        assert result == '{"name": "John"}'

    def test_extract_json_empty_object(self):
        """Test with empty JSON object"""
        text = '{} extra'
        result = extract_json(text)
        assert result == '{}'

    def test_extract_json_empty_array(self):
        """Test with empty JSON array"""
        text = '[] extra'
        result = extract_json(text)
        assert result == '[]'


class TestParameterExtractorNode:
    """Test cases for ParameterExtractorNode"""

    @pytest.fixture
    def mock_graph_runtime_state(self):
        """Mock graph runtime state"""
        mock_state = Mock()
        mock_variable_pool = Mock(spec=VariablePool)
        mock_state.variable_pool = mock_variable_pool
        return mock_state

    @pytest.fixture
    def parameter_config(self):
        """Create test parameter config"""
        return ParameterConfig(
            name="test_param",
            type="string",
            required=True,
            description="Test parameter"
        )

    @pytest.fixture
    def node_data(self, parameter_config):
        """Create test node data"""
        return ParameterExtractorNodeData(
            title="Test Parameter Extractor",
            model=ModelConfig(
                provider="openai",
                name="gpt-3.5-turbo",
                mode="chat",
                completion_params={}
            ),
            query=["input", "text"],
            parameters=[parameter_config],
            instruction="Extract parameters from text",
            reasoning_mode="function_call"
        )

    @pytest.fixture
    def parameter_extractor_node(self, node_data, mock_graph_runtime_state):
        """Create test parameter extractor node"""
        from core.app.entities.app_invoke_entities import InvokeFrom
        from models.enums import UserFrom
        from models.workflow import WorkflowType
        from core.workflow.graph_engine.entities.graph_init_params import GraphInitParams
        from unittest.mock import Mock

        graph_init_params = GraphInitParams(
            tenant_id="test_tenant",
            app_id="test_app",
            workflow_type=WorkflowType.WORKFLOW,
            workflow_id="test_workflow",
            graph_config={},
            user_id="test_user",
            user_from=UserFrom.ACCOUNT,
            invoke_from=InvokeFrom.SERVICE_API,
            call_depth=0
        )

        mock_graph = Mock()

        node = ParameterExtractorNode(
            id="test_node",
            config={
                "id": "test_node",
                "data": node_data.model_dump()
            },
            graph_init_params=graph_init_params,
            graph=mock_graph,
            graph_runtime_state=mock_graph_runtime_state
        )
        return node

    def test_get_default_config(self):
        """Test get_default_config class method"""
        config = ParameterExtractorNode.get_default_config()
        
        assert "model" in config
        assert "prompt_templates" in config["model"]
        assert "completion_model" in config["model"]["prompt_templates"]

    @patch('core.workflow.nodes.parameter_extractor.parameter_extractor_node.extract_json_from_str')
    def test_run_rule_success(self, mock_extract_json, parameter_extractor_node):
        """Test _run_rule method with successful extraction"""
        # Setup mocks
        parameter_extractor_node.graph_runtime_state.variable_pool.get.return_value = StringSegment(
            value='{"test_param": "test_value"}'
        )
        mock_extract_json.return_value = {"test_param": "test_value"}
        
        # Mock _validate_result and _transform_result
        parameter_extractor_node._validate_result = Mock(return_value={"test_param": "test_value"})
        parameter_extractor_node._transform_result = Mock(return_value={"test_param": "test_value"})
        
        # Run the method
        result = parameter_extractor_node._run_rule()
        
        # Verify result
        assert isinstance(result, NodeRunResult)
        assert result.status == WorkflowNodeExecutionStatus.SUCCEEDED
        assert result.outputs["__is_success"] == 1
        assert result.outputs["test_param"] == "test_value"

    @patch('core.workflow.nodes.parameter_extractor.parameter_extractor_node.extract_json_from_str')
    def test_run_rule_invalid_json(self, mock_extract_json, parameter_extractor_node):
        """Test _run_rule method with invalid JSON"""
        # Setup mocks
        parameter_extractor_node.graph_runtime_state.variable_pool.get.return_value = StringSegment(
            value='invalid json'
        )
        mock_extract_json.return_value = "not a dict"
        
        # Run the method and expect ValueError
        with pytest.raises(ValueError, match="参数提取失败"):
            parameter_extractor_node._run_rule()

    @patch('core.workflow.nodes.parameter_extractor.parameter_extractor_node.extract_json_from_str')
    def test_run_rule_validation_error(self, mock_extract_json, parameter_extractor_node):
        """Test _run_rule method with validation error"""
        # Setup mocks
        parameter_extractor_node.graph_runtime_state.variable_pool.get.return_value = StringSegment(
            value='{"test_param": "test_value"}'
        )
        mock_extract_json.return_value = {"test_param": "test_value"}
        
        # Mock _validate_result to raise error
        parameter_extractor_node._validate_result = Mock(
            side_effect=RequiredParameterMissingError("Required parameter missing")
        )
        parameter_extractor_node._transform_result = Mock(return_value={"test_param": "test_value"})
        
        # Run the method
        result = parameter_extractor_node._run_rule()
        
        # Verify result shows error
        assert result.outputs["__is_success"] == 0
        assert "Required parameter missing" in result.outputs["__reason"]

    def test_validate_result_success(self, parameter_extractor_node, node_data):
        """Test _validate_result with valid data"""
        result = {"test_param": "test_value"}
        
        validated = parameter_extractor_node._validate_result(node_data, result)
        
        assert validated == result

    def test_validate_result_missing_required(self, parameter_extractor_node, node_data):
        """Test _validate_result with missing required parameter"""
        result = {}  # Missing required test_param
        
        with pytest.raises(RequiredParameterMissingError):
            parameter_extractor_node._validate_result(node_data, result)

    def test_validate_result_invalid_string(self, parameter_extractor_node, node_data):
        """Test _validate_result with invalid string parameter"""
        result = {"test_param": 123}  # Should be string
        
        with pytest.raises(InvalidStringValueError):
            parameter_extractor_node._validate_result(node_data, result)

    def test_validate_result_number_parameter(self, parameter_extractor_node):
        """Test _validate_result with number parameter"""
        # Create node data with number parameter
        number_param = ParameterConfig(
            name="number_param",
            type="number",
            required=True,
            description="Number parameter"
        )
        node_data = ParameterExtractorNodeData(
            title="Test",
            model=ModelConfig(provider="openai", name="gpt-3.5-turbo", mode="chat", completion_params={}),
            query=["input"],
            parameters=[number_param],
            reasoning_mode="function_call"
        )
        
        # Valid number
        result = {"number_param": 42}
        validated = parameter_extractor_node._validate_result(node_data, result)
        assert validated == result
        
        # Invalid number
        result = {"number_param": "not a number"}
        with pytest.raises(InvalidNumberValueError):
            parameter_extractor_node._validate_result(node_data, result)

    def test_validate_result_boolean_parameter(self, parameter_extractor_node):
        """Test _validate_result with boolean parameter"""
        # Create node data with boolean parameter
        bool_param = ParameterConfig(
            name="bool_param",
            type="boolean",
            required=True,
            description="Boolean parameter"
        )
        node_data = ParameterExtractorNodeData(
            title="Test",
            model=ModelConfig(provider="openai", name="gpt-3.5-turbo", mode="chat", completion_params={}),
            query=["input"],
            parameters=[bool_param],
            reasoning_mode="function_call"
        )
        
        # Valid boolean
        result = {"bool_param": True}
        validated = parameter_extractor_node._validate_result(node_data, result)
        assert validated == result
        
        # Invalid boolean
        result = {"bool_param": "not a boolean"}
        with pytest.raises(InvalidBoolValueError):
            parameter_extractor_node._validate_result(node_data, result)

    def test_validate_result_select_parameter(self, parameter_extractor_node):
        """Test _validate_result with select parameter"""
        # Create node data with select parameter
        select_param = ParameterConfig(
            name="select_param",
            type="select",
            required=True,
            description="Select parameter",
            options=["option1", "option2", "option3"]
        )
        node_data = ParameterExtractorNodeData(
            title="Test",
            model=ModelConfig(provider="openai", name="gpt-3.5-turbo", mode="chat", completion_params={}),
            query=["input"],
            parameters=[select_param],
            reasoning_mode="function_call"
        )
        
        # Valid selection
        result = {"select_param": "option1"}
        validated = parameter_extractor_node._validate_result(node_data, result)
        assert validated == result
        
        # Invalid selection
        result = {"select_param": "invalid_option"}
        with pytest.raises(InvalidSelectValueError):
            parameter_extractor_node._validate_result(node_data, result)

    def test_validate_result_array_parameter(self, parameter_extractor_node):
        """Test _validate_result with array parameter"""
        # Create node data with array parameter
        array_param = ParameterConfig(
            name="array_param",
            type="array",
            required=True,
            description="Array parameter"
        )
        node_data = ParameterExtractorNodeData(
            title="Test",
            model=ModelConfig(provider="openai", name="gpt-3.5-turbo", mode="chat", completion_params={}),
            query=["input"],
            parameters=[array_param],
            reasoning_mode="function_call"
        )
        
        # Valid array
        result = {"array_param": ["item1", "item2"]}
        validated = parameter_extractor_node._validate_result(node_data, result)
        assert validated == result
        
        # Invalid array
        result = {"array_param": "not an array"}
        with pytest.raises(InvalidArrayValueError):
            parameter_extractor_node._validate_result(node_data, result)

    def test_transform_result(self, parameter_extractor_node, node_data):
        """Test _transform_result method"""
        result = {"test_param": "test_value"}
        
        transformed = parameter_extractor_node._transform_result(node_data, result)
        
        # Should return the same result for basic transformation
        assert transformed == result

    def test_generate_default_result(self, parameter_extractor_node, node_data):
        """Test _generate_default_result method"""
        default_result = parameter_extractor_node._generate_default_result(node_data)
        
        # Should generate empty result with parameter names
        assert "test_param" in default_result
        assert default_result["test_param"] is None

    @patch('core.workflow.nodes.parameter_extractor.parameter_extractor_node.json.loads')
    def test_extract_complete_json_response_success(self, mock_json_loads, parameter_extractor_node):
        """Test _extract_complete_json_response with valid JSON"""
        mock_json_loads.return_value = {"test_param": "test_value"}
        
        result = parameter_extractor_node._extract_complete_json_response('{"test_param": "test_value"}')
        
        assert result == {"test_param": "test_value"}

    def test_extract_complete_json_response_invalid(self, parameter_extractor_node):
        """Test _extract_complete_json_response with invalid JSON"""
        result = parameter_extractor_node._extract_complete_json_response('invalid json')
        
        assert result is None

    def test_extract_json_from_tool_call(self, parameter_extractor_node):
        """Test _extract_json_from_tool_call method"""
        # Create mock tool call
        tool_call = Mock()
        tool_call.function.arguments = '{"test_param": "test_value"}'
        
        result = parameter_extractor_node._extract_json_from_tool_call(tool_call)
        
        assert result == {"test_param": "test_value"}

    def test_extract_json_from_tool_call_invalid(self, parameter_extractor_node):
        """Test _extract_json_from_tool_call with invalid JSON"""
        # Create mock tool call with invalid JSON
        tool_call = Mock()
        tool_call.function.arguments = 'invalid json'
        
        result = parameter_extractor_node._extract_json_from_tool_call(tool_call)
        
        assert result is None
