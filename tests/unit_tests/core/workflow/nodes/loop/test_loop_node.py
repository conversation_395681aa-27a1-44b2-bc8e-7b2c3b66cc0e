import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime, UTC
import json

from core.workflow.nodes.loop.loop_node import Loop<PERSON>ode
from core.workflow.entities.node_entities import NodeRunResult, NodeRunMetadataKey
from core.workflow.nodes.base import BaseNode
from core.workflow.nodes.loop.entities import LoopNodeData
from core.workflow.entities.variable_pool import VariablePool
from core.workflow.graph_engine.graph_engine import GraphEngine
from core.workflow.graph_engine.entities.graph import Graph
from core.workflow.graph_engine.entities.event import (
    LoopRunStartedEvent,
    LoopRunNextEvent,
    LoopRunSucceededEvent,
    LoopRunFailedEvent,
    NodeRunSucceededEvent,
    NodeRunFailedEvent,
    GraphRunFailedEvent
)
from models.workflow import WorkflowNodeExecutionStatus
from core.variables import (
    StringSegment,
    IntegerSegment,
    ObjectSegment,
    ArrayStringSegment,
    ArrayNumberSegment,
    ArrayObjectSegment,
    SegmentType
)

class TestLoopNode:
    @pytest.fixture
    def loop_node_data(self):
        # Define LoopBreakCondition if not imported
        class LoopBreakCondition:
            def __init__(self, variable_selector, operator, value):
                self.variable_selector = variable_selector
                self.operator = operator
                self.value = value

        return LoopNodeData(
            title="Test Loop Node",
            loop_count=3,
            start_node_id="start_node_1",
            break_conditions=[],
            logical_operator="and",
            loop_variables=[],
            outputs={},
            metadata={}
        )

    @pytest.fixture
    def loop_node(self, loop_node_data):
        from core.workflow.entities.base_node import WorkflowType, UserFrom, InvokeFrom
        
        # Create mock graph
        mock_graph = MagicMock(spec=Graph)
        
        # Create valid graph_init_params
        graph_init_params = {
            "tenant_id": "test_tenant",
            "app_id": "test_app",
            "workflow_type": WorkflowType.WORKFLOW,
            "workflow_id": "test_workflow",
            "graph_config": {},
            "user_id": "test_user",
            "user_from": UserFrom.ACCOUNT,
            "invoke_from": InvokeFrom.SERVICE_API,
            "call_depth": 0
        }
        
        # Create runtime state with required attributes
        runtime_state = MagicMock()
        runtime_state.variable_pool = VariablePool()
        runtime_state.tenant_id = "test_tenant"
        runtime_state.app_id = "test_app"
        runtime_state.workflow_id = "test_workflow"
        runtime_state.workflow_type = WorkflowType.WORKFLOW
        runtime_state.user_id = "test_user"
        runtime_state.user_from = UserFrom.ACCOUNT
        runtime_state.invoke_from = InvokeFrom.SERVICE_API
        runtime_state.workflow_call_depth = 0
        runtime_state.thread_pool_id = None

        # Initialize LoopNode with all required parameters
        node = LoopNode(
            id="test_node",
            config={
                "id": "test_node",
                "data": loop_node_data.dict(),
                "type": "loop"
            },
            graph_init_params=graph_init_params,
            graph=mock_graph,
            graph_runtime_state=runtime_state
        )
