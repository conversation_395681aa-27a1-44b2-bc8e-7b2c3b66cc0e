import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime, UTC
import json

from core.workflow.nodes.loop.loop_node import Loop<PERSON>ode
from core.workflow.entities.node_entities import NodeRunResult, NodeRunMetadataKey
from core.workflow.nodes.base import BaseNode
from core.workflow.nodes.loop.entities import LoopNodeData
from core.workflow.entities.variable_pool import VariablePool
from core.workflow.graph_engine.graph_engine import GraphEngine
from core.workflow.graph_engine.entities.graph import Graph
from core.workflow.graph_engine.entities.event import (
    LoopRunStartedEvent,
    LoopRunNextEvent,
    LoopRunSucceededEvent,
    LoopRunFailedEvent,
    NodeRunSucceededEvent,
    NodeRunFailedEvent,
    GraphRunFailedEvent
)
from models.workflow import WorkflowNodeExecutionStatus
from core.variables import (
    StringSegment,
    IntegerSegment,
    ObjectSegment,
    ArrayStringSegment,
    ArrayNumberSegment,
    ArrayObjectSegment,
    SegmentType
)

class TestLoopNode:
    @pytest.fixture
    def loop_node_data(self):
        # Define LoopBreakCondition if not imported
        class LoopBreakCondition:
            def __init__(self, variable_selector, operator, value):
                self.variable_selector = variable_selector
                self.operator = operator
                self.value = value

        return LoopNodeData(
            title="Test Loop Node",
            loop_count=3,
            start_node_id="start_node_1",
            break_conditions=[],
            logical_operator="and",
            loop_variables=[],
            outputs={},
            metadata={}
        )

    @pytest.fixture
    def loop_node(self, loop_node_data):
        from core.app.entities.app_invoke_entities import InvokeFrom
        from models.enums import UserFrom
        from models.workflow import WorkflowType
        from core.workflow.graph_engine.entities.graph_init_params import GraphInitParams

        # Create mock graph
        mock_graph = MagicMock(spec=Graph)

        # Create valid graph_init_params
        graph_init_params = GraphInitParams(
            tenant_id="test_tenant",
            app_id="test_app",
            workflow_type=WorkflowType.WORKFLOW,
            workflow_id="test_workflow",
            graph_config={},
            user_id="test_user",
            user_from=UserFrom.ACCOUNT,
            invoke_from=InvokeFrom.SERVICE_API,
            call_depth=0
        )
        
        # Create runtime state with required attributes
        runtime_state = MagicMock()
        runtime_state.variable_pool = VariablePool()
        runtime_state.tenant_id = "test_tenant"
        runtime_state.app_id = "test_app"
        runtime_state.workflow_id = "test_workflow"
        runtime_state.workflow_type = WorkflowType.WORKFLOW
        runtime_state.user_id = "test_user"
        runtime_state.user_from = UserFrom.ACCOUNT
        runtime_state.invoke_from = InvokeFrom.SERVICE_API
        runtime_state.workflow_call_depth = 0
        runtime_state.thread_pool_id = None

        # Initialize LoopNode with all required parameters
        node = LoopNode(
            id="test_node",
            config={
                "id": "test_node",
                "data": loop_node_data.model_dump(),
                "type": "loop"
            },
            graph_init_params=graph_init_params,
            graph=mock_graph,
            graph_runtime_state=runtime_state
        )

        return node

    def test_loop_node_initialization(self, loop_node):
        """Test loop node initialization"""
        assert loop_node.node_type == "loop"
        assert loop_node.node_data.loop_count == 3
        assert loop_node.node_data.start_node_id == "start_node_1"

    def test_run_missing_start_node_id(self, loop_node):
        """Test run with missing start_node_id"""
        loop_node.node_data.start_node_id = None

        generator = loop_node._run()

        with pytest.raises(ValueError, match="field start_node_id in loop"):
            next(generator)

    @patch('core.workflow.nodes.loop.loop_node.Graph')
    def test_run_invalid_graph(self, mock_graph_class, loop_node):
        """Test run with invalid graph"""
        mock_graph_class.init.return_value = None

        generator = loop_node._run()

        with pytest.raises(ValueError, match="loop graph not found"):
            next(generator)

    @patch('core.workflow.nodes.loop.loop_node.Graph')
    @patch('core.workflow.graph_engine.graph_engine.GraphEngine')
    @patch('core.workflow.nodes.loop.loop_node.ConditionProcessor')
    def test_run_successful_loop(self, mock_condition_processor, mock_graph_engine_class, mock_graph_class, loop_node):
        """Test successful loop execution"""
        # Setup mocks
        mock_graph = MagicMock()
        mock_graph_class.init.return_value = mock_graph

        mock_graph_engine = MagicMock()
        mock_graph_engine_class.return_value = mock_graph_engine
        mock_graph_engine.graph_runtime_state.total_tokens = 100

        mock_condition_processor_instance = MagicMock()
        mock_condition_processor.return_value = mock_condition_processor_instance

        # Mock variable pool
        mock_variable_pool = MagicMock()
        mock_variable_pool.get.return_value = IntegerSegment(value=0)
        loop_node.graph_runtime_state.variable_pool = mock_variable_pool

        # Mock _run_single_loop to return no break
        def mock_run_single_loop(*args, **kwargs):
            yield from []
            return {"check_break_result": False}

        loop_node._run_single_loop = mock_run_single_loop

        # Run the loop
        events = list(loop_node._run())

        # Verify events
        assert len(events) >= 2  # At least start and success events
        assert isinstance(events[0], LoopRunStartedEvent)
        # The last event should have a successful run result
        assert hasattr(events[-1], 'run_result')
        assert events[-1].run_result.status.value == "succeeded"

    @patch('core.workflow.nodes.loop.loop_node.Graph')
    @patch('core.workflow.graph_engine.graph_engine.GraphEngine')
    def test_run_loop_with_exception(self, mock_graph_engine_class, mock_graph_class, loop_node):
        """Test loop execution with exception"""
        # Setup mocks
        mock_graph = MagicMock()
        mock_graph_class.init.return_value = mock_graph

        mock_graph_engine = MagicMock()
        mock_graph_engine_class.return_value = mock_graph_engine
        mock_graph_engine.graph_runtime_state.total_tokens = 100

        # Mock variable pool
        mock_variable_pool = MagicMock()
        mock_variable_pool.get.return_value = IntegerSegment(value=0)
        loop_node.graph_runtime_state.variable_pool = mock_variable_pool

        # Mock _run_single_loop to raise exception
        def mock_run_single_loop(*args, **kwargs):
            raise ValueError("Test error")

        loop_node._run_single_loop = mock_run_single_loop

        # Run the loop
        events = list(loop_node._run())

        # Verify failure event
        assert len(events) >= 2
        assert isinstance(events[0], LoopRunStartedEvent)
        # Check if the last event indicates failure
        assert hasattr(events[-1], 'run_result')
        assert events[-1].run_result.status.value == "failed"

    @patch('core.workflow.nodes.loop.loop_node.isinstance')
    def test_handle_event_metadata_with_metadata(self, mock_isinstance, loop_node):
        """Test _handle_event_metadata with existing metadata"""
        # Mock isinstance to return True for BaseNodeEvent
        mock_isinstance.return_value = True

        # Create mock event with node run result
        mock_event = MagicMock()
        mock_event.route_node_state.node_run_result.metadata = {"existing": "data"}

        result = loop_node._handle_event_metadata(event=mock_event, iter_run_index=2)

        # Verify metadata was updated
        metadata = mock_event.route_node_state.node_run_result.metadata
        assert "existing" in metadata
        assert metadata["existing"] == "data"
        assert NodeRunMetadataKey.LOOP_ID in metadata
        assert NodeRunMetadataKey.LOOP_INDEX in metadata
        assert metadata[NodeRunMetadataKey.LOOP_ID] == loop_node.node_id
        assert metadata[NodeRunMetadataKey.LOOP_INDEX] == 2
        assert result == mock_event

    @patch('core.workflow.nodes.loop.loop_node.isinstance')
    def test_handle_event_metadata_no_metadata(self, mock_isinstance, loop_node):
        """Test _handle_event_metadata with no existing metadata"""
        # Mock isinstance to return True for BaseNodeEvent
        mock_isinstance.return_value = True

        mock_event = MagicMock()
        mock_event.route_node_state.node_run_result.metadata = None

        result = loop_node._handle_event_metadata(event=mock_event, iter_run_index=1)

        # Verify metadata was created
        metadata = mock_event.route_node_state.node_run_result.metadata
        assert metadata is not None
        assert NodeRunMetadataKey.LOOP_ID in metadata
        assert NodeRunMetadataKey.LOOP_INDEX in metadata
        assert metadata[NodeRunMetadataKey.LOOP_ID] == loop_node.node_id
        assert metadata[NodeRunMetadataKey.LOOP_INDEX] == 1

    def test_handle_event_metadata_existing_loop_id(self, loop_node):
        """Test _handle_event_metadata with existing loop ID"""
        mock_event = MagicMock()
        mock_event.route_node_state.node_run_result.metadata = {
            NodeRunMetadataKey.LOOP_ID: "existing_loop"
        }

        result = loop_node._handle_event_metadata(event=mock_event, iter_run_index=1)

        # Verify metadata was not overwritten
        assert mock_event.route_node_state.node_run_result.metadata[NodeRunMetadataKey.LOOP_ID] == "existing_loop"

    def test_run_single_loop_successful(self, loop_node):
        """Test successful single loop execution"""
        # Setup mocks
        mock_graph = MagicMock()
        mock_graph.node_ids = ["node1", "node2"]

        mock_graph_engine = MagicMock()
        mock_graph_engine.graph_runtime_state.total_tokens = 50

        # Mock variable pool
        mock_variable_pool = MagicMock()
        mock_variable_pool.get.return_value = IntegerSegment(value=1)

        # Mock graph engine run to return success event
        mock_success_event = MagicMock(spec=NodeRunSucceededEvent)
        mock_success_event.node_type = "loop_end"
        mock_success_event.in_loop_id = None
        mock_graph_engine.run.return_value = [mock_success_event]

        # Mock condition processor
        mock_condition_processor = MagicMock()

        # Mock _handle_event_metadata
        loop_node._handle_event_metadata = MagicMock(return_value=mock_success_event)

        # Run single loop
        result_gen = loop_node._run_single_loop(
            graph_engine=mock_graph_engine,
            loop_graph=mock_graph,
            variable_pool=mock_variable_pool,
            loop_variable_selectors={},
            break_conditions=[],
            logical_operator="and",
            condition_processor=mock_condition_processor,
            current_index=1,
            start_at=datetime.now(),
            inputs={"loop_count": 3}
        )

        events = list(result_gen)

        # Check if we have events and the last one is a LoopRunNextEvent
        assert len(events) > 0
        last_event = events[-1]
        # The last event should be a LoopRunNextEvent
        assert isinstance(last_event, LoopRunNextEvent)
        assert last_event.index == 2  # next_index = current_index + 1

    def test_run_single_loop_graph_failed(self, loop_node):
        """Test single loop with graph failure"""
        # Setup mocks
        mock_graph = MagicMock()
        mock_graph.node_ids = ["node1"]

        mock_graph_engine = MagicMock()
        mock_graph_engine.graph_runtime_state.total_tokens = 50

        mock_variable_pool = MagicMock()
        mock_variable_pool.get.return_value = IntegerSegment(value=0)

        # Mock graph engine run to return failure event
        mock_failure_event = MagicMock(spec=GraphRunFailedEvent)
        mock_failure_event.error = "Graph execution failed"
        mock_graph_engine.run.return_value = [mock_failure_event]

        mock_condition_processor = MagicMock()

        # Run single loop
        result_gen = loop_node._run_single_loop(
            graph_engine=mock_graph_engine,
            loop_graph=mock_graph,
            variable_pool=mock_variable_pool,
            loop_variable_selectors={},
            break_conditions=[],
            logical_operator="and",
            condition_processor=mock_condition_processor,
            current_index=0,
            start_at=datetime.now(),
            inputs={"loop_count": 3}
        )

        events = list(result_gen)

        # Should contain failure events
        failure_events = [e for e in events if isinstance(e, LoopRunFailedEvent)]
        assert len(failure_events) > 0
        assert failure_events[0].error == "Graph execution failed"

    def test_run_single_loop_node_failed(self, loop_node):
        """Test single loop with node failure"""
        # Setup mocks
        mock_graph = MagicMock()
        mock_graph.node_ids = ["node1"]

        mock_graph_engine = MagicMock()
        mock_graph_engine.graph_runtime_state.total_tokens = 50

        mock_variable_pool = MagicMock()
        mock_variable_pool.get.return_value = IntegerSegment(value=0)

        # Mock graph engine run to return node failure event
        mock_failure_event = MagicMock(spec=NodeRunFailedEvent)
        mock_failure_event.error = "Node execution failed"
        mock_failure_event.in_loop_id = None
        mock_failure_event.node_type = "test_node"
        mock_graph_engine.run.return_value = [mock_failure_event]

        mock_condition_processor = MagicMock()

        # Mock _handle_event_metadata
        loop_node._handle_event_metadata = MagicMock(return_value=mock_failure_event)

        # Run single loop
        result_gen = loop_node._run_single_loop(
            graph_engine=mock_graph_engine,
            loop_graph=mock_graph,
            variable_pool=mock_variable_pool,
            loop_variable_selectors={},
            break_conditions=[],
            logical_operator="and",
            condition_processor=mock_condition_processor,
            current_index=0,
            start_at=datetime.now(),
            inputs={"loop_count": 3}
        )

        events = list(result_gen)

        # Should contain failure events
        failure_events = [e for e in events if isinstance(e, LoopRunFailedEvent)]
        assert len(failure_events) > 0
        assert failure_events[0].error == "Node execution failed"

    def test_run_single_loop_with_loop_variables(self, loop_node):
        """Test single loop with loop variables"""
        # Setup mocks
        mock_graph = MagicMock()
        mock_graph.node_ids = ["node1"]

        mock_graph_engine = MagicMock()
        mock_graph_engine.graph_runtime_state.total_tokens = 50

        mock_variable_pool = MagicMock()
        mock_variable_pool.get.side_effect = [
            IntegerSegment(value=1),  # For current index
            StringSegment(value="test_value")  # For loop variable
        ]

        # Mock graph engine run to return success event
        mock_success_event = MagicMock(spec=NodeRunSucceededEvent)
        mock_success_event.node_type = "loop_end"
        mock_success_event.in_loop_id = None
        mock_graph_engine.run.return_value = [mock_success_event]

        mock_condition_processor = MagicMock()

        # Mock _handle_event_metadata
        loop_node._handle_event_metadata = MagicMock(return_value=mock_success_event)

        # Run single loop with loop variables
        result_gen = loop_node._run_single_loop(
            graph_engine=mock_graph_engine,
            loop_graph=mock_graph,
            variable_pool=mock_variable_pool,
            loop_variable_selectors={"output_var": ["node1", "output"]},
            break_conditions=[],
            logical_operator="and",
            condition_processor=mock_condition_processor,
            current_index=1,
            start_at=datetime.now(),
            inputs={"loop_count": 3}
        )

        events = list(result_gen)

        # Check if we have events and the last one is a LoopRunNextEvent
        assert len(events) > 0
        last_event = events[-1]
        # The last event should be a LoopRunNextEvent
        assert isinstance(last_event, LoopRunNextEvent)
        assert last_event.index == 2  # next_index = current_index + 1

        # Verify outputs were set (if the node_data has outputs attribute)
        if hasattr(loop_node.node_data, 'outputs') and loop_node.node_data.outputs:
            assert loop_node.node_data.outputs.get("output_var") == "test_value"
            assert loop_node.node_data.outputs.get("loop_round") == 2
