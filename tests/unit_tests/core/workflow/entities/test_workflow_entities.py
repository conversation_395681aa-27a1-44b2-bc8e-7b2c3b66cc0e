import pytest
from unittest.mock import MagicMock
from core.workflow.entities.workflow_entities import (
    WorkflowNodeAndResult,
    WorkflowRunState
)
from models.enums import UserFrom
from core.workflow.entities.workflow_entities import WorkflowType
from core.app.entities.app_invoke_entities import InvokeFrom

class TestWorkflowNodeAndResult:
    def test_initialization(self):
        mock_node = MagicMock()
        mock_result = MagicMock()
        
        # Test with result
        entity = WorkflowNodeAndResult(node=mock_node, result=mock_result)
        assert entity.node == mock_node
        assert entity.result == mock_result
        
        # Test without result
        entity = WorkflowNodeAndResult(node=mock_node)
        assert entity.node == mock_node
        assert entity.result is None

class TestWorkflowRunState:
    @pytest.fixture
    def mock_workflow(self):
        mock = MagicMock()
        mock.id = "workflow123"
        mock.tenant_id = "tenant123"
        mock.app_id = "app123"
        mock.type = WorkflowType.WORKFLOW.value
        return mock

    @pytest.fixture
    def mock_variable_pool(self):
        return MagicMock()

    def test_initialization(self, mock_workflow, mock_variable_pool):
        state = WorkflowRunState(
            workflow=mock_workflow,
            start_at=**********.0,
            variable_pool=mock_variable_pool,
            user_id="user123",
            user_from=UserFrom.ACCOUNT,
            invoke_from=InvokeFrom.DEBUGGER,
            workflow_call_depth=1
        )
        
        # Verify basic properties
        assert state.workflow_id == "workflow123"
        assert state.tenant_id == "tenant123"
        assert state.app_id == "app123"
        assert state.workflow_type == WorkflowType.WORKFLOW
        assert state.user_id == "user123"
        assert state.user_from == UserFrom.ACCOUNT
        assert state.invoke_from == InvokeFrom.DEBUGGER
        assert state.workflow_call_depth == 1
        
        # Verify timing
        assert state.start_at == **********.0
        
        # Verify collections
        assert state.variable_pool == mock_variable_pool
        assert state.total_tokens == 0
        assert state.workflow_node_steps == 1
        assert state.workflow_node_runs == []
        assert state.current_iteration_state is None
        assert state.current_loop_state is None

    def test_node_run_inner_class(self):
        node_run = WorkflowRunState.NodeRun(
            node_id="node123",
            iteration_node_id="iter123",
            loop_node_id="loop123"
        )
        
        assert node_run.node_id == "node123"
        assert node_run.iteration_node_id == "iter123"
        assert node_run.loop_node_id == "loop123"

    def test_workflow_nodes_and_results(self, mock_workflow, mock_variable_pool):
        state = WorkflowRunState(
            workflow=mock_workflow,
            start_at=**********.0,
            variable_pool=mock_variable_pool,
            user_id="user123",
            user_from=UserFrom.ACCOUNT,
            invoke_from=InvokeFrom.DEBUGGER,
            workflow_call_depth=1
        )
        
        # Initially empty
        assert not hasattr(state, 'workflow_nodes_and_results')
        
        # Add nodes and results
        mock_node1 = MagicMock()
        mock_result1 = MagicMock()
        mock_node2 = MagicMock()
        
        state.workflow_nodes_and_results = [
            WorkflowNodeAndResult(node=mock_node1, result=mock_result1),
            WorkflowNodeAndResult(node=mock_node2)
        ]
        
        assert len(state.workflow_nodes_and_results) == 2
        assert state.workflow_nodes_and_results[0].node == mock_node1
        assert state.workflow_nodes_and_results[0].result == mock_result1
        assert state.workflow_nodes_and_results[1].node == mock_node2
        assert state.workflow_nodes_and_results[1].result is None
