import pytest
from unittest.mock import Mock

from core.plugin.utils.converter import convert_parameters_to_plugin_format
from core.file.models import File
from core.tools.entities.tool_entities import ToolSelector


class TestConvertParametersToPluginFormat:
    """Test cases for convert_parameters_to_plugin_format function"""

    def test_convert_parameters_empty_dict(self):
        """Test with empty parameters dictionary"""
        parameters = {}
        result = convert_parameters_to_plugin_format(parameters)
        
        assert result == {}

    def test_convert_parameters_no_special_types(self):
        """Test with parameters that don't need conversion"""
        parameters = {
            "string_param": "test_value",
            "int_param": 42,
            "bool_param": True,
            "list_param": ["item1", "item2"],
            "dict_param": {"key": "value"}
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        assert result == parameters

    def test_convert_parameters_single_file(self):
        """Test with single File parameter"""
        mock_file = Mock(spec=File)
        mock_file.to_plugin_parameter.return_value = {"file_id": "123", "filename": "test.txt"}
        
        parameters = {
            "file_param": mock_file,
            "other_param": "value"
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        expected = {
            "file_param": {"file_id": "123", "filename": "test.txt"},
            "other_param": "value"
        }
        assert result == expected
        mock_file.to_plugin_parameter.assert_called_once()

    def test_convert_parameters_file_list(self):
        """Test with list of File parameters"""
        mock_file1 = Mock(spec=File)
        mock_file1.to_plugin_parameter.return_value = {"file_id": "123", "filename": "test1.txt"}
        
        mock_file2 = Mock(spec=File)
        mock_file2.to_plugin_parameter.return_value = {"file_id": "456", "filename": "test2.txt"}
        
        parameters = {
            "files_param": [mock_file1, mock_file2],
            "other_param": "value"
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        expected = {
            "files_param": [
                {"file_id": "123", "filename": "test1.txt"},
                {"file_id": "456", "filename": "test2.txt"}
            ],
            "other_param": "value"
        }
        assert result == expected
        mock_file1.to_plugin_parameter.assert_called_once()
        mock_file2.to_plugin_parameter.assert_called_once()

    def test_convert_parameters_single_tool_selector(self):
        """Test with single ToolSelector parameter"""
        mock_tool_selector = Mock(spec=ToolSelector)
        mock_tool_selector.to_plugin_parameter.return_value = {"tool_name": "test_tool", "provider": "builtin"}
        
        parameters = {
            "tool_param": mock_tool_selector,
            "other_param": "value"
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        expected = {
            "tool_param": {"tool_name": "test_tool", "provider": "builtin"},
            "other_param": "value"
        }
        assert result == expected
        mock_tool_selector.to_plugin_parameter.assert_called_once()

    def test_convert_parameters_tool_selector_list(self):
        """Test with list of ToolSelector parameters"""
        mock_tool1 = Mock(spec=ToolSelector)
        mock_tool1.to_plugin_parameter.return_value = {"tool_name": "tool1", "provider": "builtin"}
        
        mock_tool2 = Mock(spec=ToolSelector)
        mock_tool2.to_plugin_parameter.return_value = {"tool_name": "tool2", "provider": "api"}
        
        parameters = {
            "tools_param": [mock_tool1, mock_tool2],
            "other_param": "value"
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        expected = {
            "tools_param": [
                {"tool_name": "tool1", "provider": "builtin"},
                {"tool_name": "tool2", "provider": "api"}
            ],
            "other_param": "value"
        }
        assert result == expected
        mock_tool1.to_plugin_parameter.assert_called_once()
        mock_tool2.to_plugin_parameter.assert_called_once()

    def test_convert_parameters_mixed_types(self):
        """Test with mixed parameter types"""
        mock_file = Mock(spec=File)
        mock_file.to_plugin_parameter.return_value = {"file_id": "123", "filename": "test.txt"}
        
        mock_tool = Mock(spec=ToolSelector)
        mock_tool.to_plugin_parameter.return_value = {"tool_name": "test_tool", "provider": "builtin"}
        
        parameters = {
            "file_param": mock_file,
            "tool_param": mock_tool,
            "string_param": "test",
            "int_param": 42
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        expected = {
            "file_param": {"file_id": "123", "filename": "test.txt"},
            "tool_param": {"tool_name": "test_tool", "provider": "builtin"},
            "string_param": "test",
            "int_param": 42
        }
        assert result == expected

    def test_convert_parameters_empty_file_list(self):
        """Test with empty list of files"""
        parameters = {
            "files_param": [],
            "other_param": "value"
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        expected = {
            "files_param": [],
            "other_param": "value"
        }
        assert result == expected

    def test_convert_parameters_empty_tool_list(self):
        """Test with empty list of tools"""
        parameters = {
            "tools_param": [],
            "other_param": "value"
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        expected = {
            "tools_param": [],
            "other_param": "value"
        }
        assert result == expected

    def test_convert_parameters_mixed_list_with_non_files(self):
        """Test with list containing both File and non-File objects"""
        mock_file = Mock(spec=File)
        mock_file.to_plugin_parameter.return_value = {"file_id": "123", "filename": "test.txt"}
        
        # List with mixed types - should not be converted as file list
        parameters = {
            "mixed_list": [mock_file, "not_a_file"],
            "other_param": "value"
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        # Should remain unchanged since not all items are Files
        expected = {
            "mixed_list": [mock_file, "not_a_file"],
            "other_param": "value"
        }
        assert result == expected
        # to_plugin_parameter should not be called since it's not a pure File list
        mock_file.to_plugin_parameter.assert_not_called()

    def test_convert_parameters_mixed_list_with_non_tools(self):
        """Test with list containing both ToolSelector and non-ToolSelector objects"""
        mock_tool = Mock(spec=ToolSelector)
        mock_tool.to_plugin_parameter.return_value = {"tool_name": "test_tool", "provider": "builtin"}
        
        # List with mixed types - should not be converted as tool list
        parameters = {
            "mixed_list": [mock_tool, "not_a_tool"],
            "other_param": "value"
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        # Should remain unchanged since not all items are ToolSelectors
        expected = {
            "mixed_list": [mock_tool, "not_a_tool"],
            "other_param": "value"
        }
        assert result == expected
        # to_plugin_parameter should not be called since it's not a pure ToolSelector list
        mock_tool.to_plugin_parameter.assert_not_called()

    def test_convert_parameters_modifies_original_dict(self):
        """Test that the function modifies the original dictionary"""
        mock_file = Mock(spec=File)
        mock_file.to_plugin_parameter.return_value = {"file_id": "123", "filename": "test.txt"}
        
        parameters = {
            "file_param": mock_file,
            "other_param": "value"
        }
        original_id = id(parameters)
        
        result = convert_parameters_to_plugin_format(parameters)
        
        # Should return the same dictionary object (modified in place)
        assert id(result) == original_id
        assert parameters["file_param"] == {"file_id": "123", "filename": "test.txt"}

    def test_convert_parameters_nested_structures_not_converted(self):
        """Test that nested structures containing Files/ToolSelectors are not converted"""
        mock_file = Mock(spec=File)
        
        parameters = {
            "nested_dict": {
                "file_param": mock_file  # This won't be converted
            },
            "nested_list": [
                {"file_param": mock_file}  # This won't be converted either
            ]
        }
        
        result = convert_parameters_to_plugin_format(parameters)
        
        # Should remain unchanged since the function only handles top-level parameters
        assert result == parameters
        mock_file.to_plugin_parameter.assert_not_called()
