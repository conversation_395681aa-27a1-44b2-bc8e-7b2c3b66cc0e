import pytest
from unittest.mock import MagicMock, patch
from core.base.common.metrics import (
    accept_request_metrics,
    record_task_run_metrics, 
    record_llm_call_metrics
)

@pytest.fixture
def mock_counter():
    with patch('core.base.common.metrics.Counter') as mock:
        mock.return_value = MagicMock()
        yield mock

@pytest.fixture
def mock_metrics(mock_counter):
    # Setup mock metrics
    from core.base.common import metrics
    metrics.ai_studio_request_accept = MagicMock()
    metrics.ai_studio_task_run_duration = MagicMock()
    metrics.ai_studio_task_run_status = MagicMock()
    metrics.ai_studio_llm_call_duration = MagicMock()
    metrics.ai_studio_llm_call_status = MagicMock()
    return metrics

def test_accept_request_metrics(mock_metrics):
    # Test
    accept_request_metrics(
        task_name="test_task",
        version="v1",
        asyn="false",
        response_mode="sync"
    )
    
    # Assert
    mock_metrics.ai_studio_request_accept.labels.assert_called_with(
        "test_task", "v1", "false", "sync"
    )
    mock_metrics.ai_studio_request_accept.labels.return_value.inc.assert_called_with(1)

def test_record_task_run_metrics(mock_metrics):
    # Test
    record_task_run_metrics(
        task_name="test_task",
        version="v1",
        asyn="true",
        response_mode="async",
        status="success",
        duration=1.5
    )
    
    # Assert
    mock_metrics.ai_studio_task_run_duration.labels.assert_called_with(
        "test_task", "v1", "true", "async"
    )
    mock_metrics.ai_studio_task_run_duration.labels.return_value.inc.assert_called_with(1.5)
    
    mock_metrics.ai_studio_task_run_status.labels.assert_called_with(
        "test_task", "v1", "true", "async", "success"
    )
    mock_metrics.ai_studio_task_run_status.labels.return_value.inc.assert_called_with(1)

def test_record_llm_call_metrics(mock_metrics):
    # Test
    record_llm_call_metrics(
        task_name="llm_task",
        version="v2",
        biz_name="test_biz",
        platform="openai",
        model_name="gpt-4",
        status="success",
        duration=200
    )
    
    # Assert
    mock_metrics.ai_studio_llm_call_duration.labels.assert_called_with(
        "llm_task", "v2", "test_biz", "openai", "gpt-4", "success"
    )
    mock_metrics.ai_studio_llm_call_duration.labels.return_value.inc.assert_called_with(200)
    
    mock_metrics.ai_studio_llm_call_status.labels.assert_called_with(
        "llm_task", "v2", "test_biz", "openai", "gpt-4", "success"
    )
    mock_metrics.ai_studio_llm_call_status.labels.return_value.inc.assert_called_with(1)

def test_metrics_with_empty_version(mock_metrics):
    # Test empty version case
    record_task_run_metrics(
        task_name="empty_version",
        version="",
        asyn="false",
        response_mode="sync",
        status="success",
        duration=1.0
    )
    
    # Assert
    mock_metrics.ai_studio_task_run_duration.labels.assert_called_with(
        "empty_version", "", "false", "sync"
    )

def test_metrics_exception_handling(mock_metrics):
    # Setup exception
    mock_metrics.ai_studio_request_accept.labels.side_effect = Exception("test error")
    
    # Test that no exception is raised
    accept_request_metrics(
        task_name="error_task",
        version="v1",
        asyn="false",
        response_mode="sync"
    )
