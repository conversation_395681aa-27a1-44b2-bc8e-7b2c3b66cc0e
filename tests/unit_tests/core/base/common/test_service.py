import json
import pytest
from unittest.mock import Mock, patch, MagicMock

from core.base.common.service import get_url_by_select_address, validate_workflow_param
from core.app.apps.workflow.app_config_manager import WorkflowAppConfig
from core.app.apps.base_app_generator import BaseAppGenerator


class TestGetUrlBySelectAddress:
    """Test cases for get_url_by_select_address function in service.py"""

    @patch('core.base.common.service.logging')
    def test_get_url_by_select_address_fixed_service(self, mock_logging):
        """Test get_url_by_select_address with fixed service ending in .sh"""
        # Note: The function hardcodes service to "aihubserv.sitxtrt.sh"
        service = "any-service"
        api_path = "/api/test"
        
        result = get_url_by_select_address(service, api_path)
        
        expected = "http://aihubserv.sitxtrt.sh/api/test"
        assert result == expected
        mock_logging.warning.assert_called_with(f"使用固定地址访问 aihubserv.sitxtrt.sh {api_path}")

    def test_get_url_by_select_address_dynamic_service_fallback(self):
        """Test get_url_by_select_address fallback to dynamic service (though hardcoded)"""
        # This test is more theoretical since the function hardcodes the service
        service = "test-service"
        api_path = "/api/test"
        service_region = "sh"

        # Even though we pass different service, it's hardcoded to "aihubserv.sitxtrt.sh"
        result = get_url_by_select_address(service, api_path, service_region)

        # The function will still use the hardcoded service
        expected = "http://aihubserv.sitxtrt.sh/api/test"
        assert result == expected


class TestValidateWorkflowParam:
    """Test cases for validate_workflow_param function"""

    def setup_method(self):
        """Setup test fixtures"""
        self.mock_app_config = Mock(spec=WorkflowAppConfig)
        self.mock_bg = Mock(spec=BaseAppGenerator)

    def test_validate_workflow_param_no_variables_param(self):
        """Test validate_workflow_param without variables parameter"""
        # Arrange
        args = {}
        inputs = {"input1": "value1", "input2": "value2"}
        
        # Mock variable entities
        mock_variable1 = Mock()
        mock_variable1.variable = "input1"
        mock_variable1.mapping.return_value = None
        
        mock_variable2 = Mock()
        mock_variable2.variable = "input2"
        mock_variable2.mapping.return_value = None
        
        self.mock_app_config.variables = [mock_variable1, mock_variable2]

        # Act
        result = validate_workflow_param(args, self.mock_app_config, inputs, self.mock_bg)

        # Assert
        assert result == inputs
        self.mock_bg._validate_inputs.assert_any_call(value="value1", variable_entity=mock_variable1)
        self.mock_bg._validate_inputs.assert_any_call(value="value2", variable_entity=mock_variable2)

    def test_validate_workflow_param_with_valid_variables(self):
        """Test validate_workflow_param with valid variables parameter"""
        # Arrange
        variables_dict = {"var1": "mapped_value1", "var2": "mapped_value2"}
        args = {"variables": json.dumps(variables_dict)}
        inputs = {"input1": "original_value1", "input2": "original_value2"}
        
        # Mock variable entities
        mock_variable1 = Mock()
        mock_variable1.variable = "input1"
        mock_variable1.mapping.return_value = "mapped_value1"
        
        mock_variable2 = Mock()
        mock_variable2.variable = "input2"
        mock_variable2.mapping.return_value = "mapped_value2"
        
        self.mock_app_config.variables = [mock_variable1, mock_variable2]

        # Act
        with patch('core.base.common.service.logging') as mock_logging:
            result = validate_workflow_param(args, self.mock_app_config, inputs, self.mock_bg)

        # Assert
        expected_inputs = {"input1": "mapped_value1", "input2": "mapped_value2"}
        assert result == expected_inputs
        
        # Check that mapping was called with correct parameters
        mock_variable1.mapping.assert_called_once_with(variables_dict)
        mock_variable2.mapping.assert_called_once_with(variables_dict)
        
        # Check logging calls
        assert mock_logging.info.call_count == 2

    def test_validate_workflow_param_with_partial_mapping(self):
        """Test validate_workflow_param with partial variable mapping"""
        # Arrange
        variables_dict = {"var1": "mapped_value1"}
        args = {"variables": json.dumps(variables_dict)}
        inputs = {"input1": "original_value1", "input2": "original_value2"}
        
        # Mock variable entities - only first one has mapping
        mock_variable1 = Mock()
        mock_variable1.variable = "input1"
        mock_variable1.mapping.return_value = "mapped_value1"
        
        mock_variable2 = Mock()
        mock_variable2.variable = "input2"
        mock_variable2.mapping.return_value = None  # No mapping for this variable
        
        self.mock_app_config.variables = [mock_variable1, mock_variable2]

        # Act
        with patch('core.base.common.service.logging') as mock_logging:
            result = validate_workflow_param(args, self.mock_app_config, inputs, self.mock_bg)

        # Assert
        expected_inputs = {"input1": "mapped_value1", "input2": "original_value2"}
        assert result == expected_inputs
        
        # Only one logging call for successful mapping
        mock_logging.info.assert_called_once_with("变量input1通过映射语法获取值成功")

    def test_validate_workflow_param_invalid_json_variables(self):
        """Test validate_workflow_param with invalid JSON in variables"""
        # Arrange
        args = {"variables": "invalid json"}
        inputs = {"input1": "value1"}
        
        # Act & Assert
        with pytest.raises(ValueError, match="参数variables非json格式"):
            validate_workflow_param(args, self.mock_app_config, inputs, self.mock_bg)

    def test_validate_workflow_param_non_dict_variables(self):
        """Test validate_workflow_param with non-dict variables"""
        # Arrange
        args = {"variables": json.dumps(["not", "a", "dict"])}
        inputs = {"input1": "value1"}
        
        # Act & Assert
        with pytest.raises(ValueError, match="必须为json对象"):
            validate_workflow_param(args, self.mock_app_config, inputs, self.mock_bg)

    def test_validate_workflow_param_missing_input_key(self):
        """Test validate_workflow_param when input key is missing"""
        # Arrange
        args = {}
        inputs = {}  # Missing input key
        
        # Mock variable entity
        mock_variable = Mock()
        mock_variable.variable = "missing_input"
        mock_variable.mapping.return_value = None
        
        self.mock_app_config.variables = [mock_variable]

        # Act
        result = validate_workflow_param(args, self.mock_app_config, inputs, self.mock_bg)

        # Assert
        expected_inputs = {"missing_input": None}
        assert result == expected_inputs
        self.mock_bg._validate_inputs.assert_called_once_with(value=None, variable_entity=mock_variable)
