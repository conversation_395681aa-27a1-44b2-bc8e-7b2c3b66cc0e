import json
import pytest
from unittest.mock import Mock, patch
from werkzeug.exceptions import BadRequest, InternalServerError

from core.base.common.utils import bool_asyn, handler_error, extract_json_from_str, get_url_by_select_address
from core.errors.error import AppInvokeQuotaExceededError


class TestBoolAsyn:
    """Test cases for bool_asyn function"""

    def test_bool_asyn_false_values(self):
        """Test bool_asyn with false values"""
        assert bool_asyn("false") is False
        assert bool_asyn("False") is False
        assert bool_asyn("FALSE") is False
        assert bool_asyn("0") is False

    def test_bool_asyn_true_values(self):
        """Test bool_asyn with true values"""
        assert bool_asyn("true") is True
        assert bool_asyn("True") is True
        assert bool_asyn("TRUE") is True
        assert bool_asyn("1") is True

    def test_bool_asyn_invalid_values(self):
        """Test bool_asyn with invalid values"""
        assert bool_asyn("invalid") is None
        assert bool_asyn("2") is None
        assert bool_asyn("yes") is None
        assert bool_asyn("no") is None
        assert bool_asyn("") is None

    def test_bool_asyn_none_value(self):
        """Test bool_asyn with None value"""
        with pytest.raises(AttributeError):
            bool_asyn(None)


class TestHandlerError:
    """Test cases for handler_error function"""

    def test_handler_error_http_exception_with_response(self):
        """Test handler_error with HTTPException that has response"""
        mock_response = Mock()
        mock_exception = Mock(spec=BadRequest)
        mock_exception.response = mock_response
        mock_exception.get_response.return_value = mock_response

        result = handler_error(mock_exception)
        
        assert result == mock_response
        mock_exception.get_response.assert_called_once()

    def test_handler_error_http_exception_without_response(self):
        """Test handler_error with HTTPException without response"""
        mock_exception = BadRequest("Bad request error")
        mock_exception.response = None

        result = handler_error(mock_exception)

        expected = {
            "code": "bad_request",
            "message": "Bad request error",
            "status": 400,
        }
        assert result == expected

    def test_handler_error_http_exception_json_decode_error(self):
        """Test handler_error with specific JSON decode error message"""
        mock_exception = BadRequest("Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)")
        mock_exception.response = None

        result = handler_error(mock_exception)

        expected = {
            "code": "bad_request",
            "message": "Invalid JSON payload received or JSON payload is empty.",
            "status": 400,
        }
        assert result == expected

    def test_handler_error_value_error(self):
        """Test handler_error with ValueError"""
        error_message = "Invalid parameter value"
        mock_exception = ValueError(error_message)

        result = handler_error(mock_exception)

        expected = {
            "code": "invalid_param",
            "message": error_message,
            "status": 400,
        }
        assert result == expected

    def test_handler_error_app_invoke_quota_exceeded_error(self):
        """Test handler_error with AppInvokeQuotaExceededError"""
        # Note: AppInvokeQuotaExceededError inherits from ValueError,
        # so it will be caught by the ValueError condition first
        error_message = "Quota exceeded"
        mock_exception = AppInvokeQuotaExceededError(error_message)

        result = handler_error(mock_exception)

        # Since AppInvokeQuotaExceededError inherits from ValueError,
        # it will be handled as ValueError in the current implementation
        expected = {
            "code": "invalid_param",
            "message": error_message,
            "status": 400,
        }
        assert result == expected

    def test_handler_error_generic_exception(self):
        """Test handler_error with generic exception"""
        mock_exception = Exception("Generic error")

        result = handler_error(mock_exception)

        expected = {
            "message": "Internal Server Error",
        }
        assert result == expected


class TestExtractJsonFromStr:
    """Test cases for extract_json_from_str function"""

    def test_extract_json_from_str_valid_json(self):
        """Test extracting valid JSON from string"""
        test_string = 'Some text before {"key": "value", "number": 123} some text after'
        result = extract_json_from_str(test_string)
        
        expected = {"key": "value", "number": 123}
        assert result == expected

    def test_extract_json_from_str_with_code_blocks(self):
        """Test extracting JSON from string with code blocks"""
        test_string = 'Some text ```json\n{"key": "value"}``` more text'
        result = extract_json_from_str(test_string)
        
        expected = {"key": "value"}
        assert result == expected

    def test_extract_json_from_str_nested_json(self):
        """Test extracting nested JSON from string"""
        test_string = 'Text {"outer": {"inner": "value"}, "array": [1, 2, 3]} text'
        result = extract_json_from_str(test_string)
        
        expected = {"outer": {"inner": "value"}, "array": [1, 2, 3]}
        assert result == expected

    def test_extract_json_from_str_invalid_json(self):
        """Test extracting invalid JSON from string"""
        test_string = 'Text {invalid json} text'
        
        with patch('core.base.common.utils.logging') as mock_logging:
            result = extract_json_from_str(test_string)
            
            assert result is None
            mock_logging.info.assert_called()

    def test_extract_json_from_str_no_braces(self):
        """Test extracting JSON when no braces found"""
        test_string = 'No JSON here at all'
        
        with patch('core.base.common.utils.logging') as mock_logging:
            result = extract_json_from_str(test_string)
            
            assert result is None
            mock_logging.warning.assert_called_with(f"找不到解析起始位置: {test_string}")

    def test_extract_json_from_str_empty_string(self):
        """Test extracting JSON from empty string"""
        result = extract_json_from_str("")
        assert result is None

    def test_extract_json_from_str_none_input(self):
        """Test extracting JSON from None input"""
        result = extract_json_from_str(None)
        assert result is None

    @patch('core.base.common.utils.logging')
    def test_extract_json_from_str_eval_fallback(self, mock_logging):
        """Test JSON extraction with eval fallback"""
        # Create a string that json.loads fails but eval succeeds
        test_string = "Text {'key': 'value'} text"  # Single quotes
        
        result = extract_json_from_str(test_string)
        
        expected = {'key': 'value'}
        assert result == expected


class TestGetUrlBySelectAddress:
    """Test cases for get_url_by_select_address function"""

    @patch('core.base.common.utils.logging')
    def test_get_url_by_select_address_fixed_service(self, mock_logging):
        """Test get_url_by_select_address with fixed service ending in .sh"""
        service = "test-service.sh"
        api_path = "/api/test"
        
        result = get_url_by_select_address(service, api_path)
        
        expected = "http://test-service.sh/api/test"
        assert result == expected
        mock_logging.warning.assert_called_with(f"使用固定地址访问 {service} {api_path}")

    @patch('atta_ai_common.nacos.address.select_address')
    def test_get_url_by_select_address_dynamic_service(self, mock_select_address):
        """Test get_url_by_select_address with dynamic service"""
        service = "test-service"
        api_path = "/api/test"
        service_region = "sh"
        selected_address = "*************:8080"

        mock_select_address.return_value = selected_address

        result = get_url_by_select_address(service, api_path, service_region)

        expected_url = "http://*************:8080/api/test"
        assert result == expected_url
        mock_select_address.assert_called_once_with(service, region=service_region)
