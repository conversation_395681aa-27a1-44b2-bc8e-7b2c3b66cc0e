import pytest
import json
from unittest.mock import patch, Mock
from werkzeug.exceptions import HTTPException, BadRequest, InternalServerError

from core.base.common.utils import bool_asyn, handler_error, extract_json_from_str, get_url_by_select_address
from core.errors.error import AppInvokeQuotaExceededError


class TestBoolAsyn:
    """Test cases for bool_asyn function"""

    def test_bool_asyn_false_values(self):
        """Test bool_asyn with false values"""
        assert bool_asyn("false") is False
        assert bool_asyn("False") is False
        assert bool_asyn("FALSE") is False
        assert bool_asyn("0") is False

    def test_bool_asyn_true_values(self):
        """Test bool_asyn with true values"""
        assert bool_asyn("true") is True
        assert bool_asyn("True") is True
        assert bool_asyn("TRUE") is True
        assert bool_asyn("1") is True

    def test_bool_asyn_invalid_values(self):
        """Test bool_asyn with invalid values"""
        assert bool_asyn("invalid") is None
        assert bool_asyn("yes") is None
        assert bool_asyn("no") is None
        assert bool_asyn("2") is None
        assert bool_asyn("") is None


class TestHandlerError:
    """Test cases for handler_error function"""

    def test_handler_error_http_exception_with_response(self):
        """Test handler_error with HTTPException that has response"""
        mock_response = Mock()
        exception = HTTPException()
        exception.response = mock_response
        exception.get_response = Mock(return_value=mock_response)
        
        result = handler_error(exception)
        
        assert result == mock_response
        exception.get_response.assert_called_once()

    def test_handler_error_http_exception_without_response(self):
        """Test handler_error with HTTPException without response"""
        exception = BadRequest("Invalid request")
        exception.response = None
        
        result = handler_error(exception)
        
        assert result["code"] == "bad_request"
        assert result["message"] == "Invalid request"
        assert result["status"] == 400

    def test_handler_error_http_exception_json_decode_error(self):
        """Test handler_error with specific JSON decode error message"""
        exception = BadRequest("Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)")
        exception.response = None
        
        result = handler_error(exception)
        
        assert result["code"] == "bad_request"
        assert result["message"] == "Invalid JSON payload received or JSON payload is empty."
        assert result["status"] == 400

    def test_handler_error_value_error(self):
        """Test handler_error with ValueError"""
        exception = ValueError("Invalid parameter value")
        
        result = handler_error(exception)
        
        assert result["code"] == "invalid_param"
        assert result["message"] == "Invalid parameter value"
        assert result["status"] == 400

    def test_handler_error_app_invoke_quota_exceeded(self):
        """Test handler_error with AppInvokeQuotaExceededError"""
        # Note: AppInvokeQuotaExceededError inherits from ValueError,
        # so it will be caught by the ValueError handler first
        exception = AppInvokeQuotaExceededError("Quota exceeded")

        result = handler_error(exception)

        # Since AppInvokeQuotaExceededError is a ValueError, it gets handled as invalid_param
        assert result["code"] == "invalid_param"
        assert result["message"] == "Quota exceeded"
        assert result["status"] == 400

    def test_handler_error_generic_exception(self):
        """Test handler_error with generic exception"""
        exception = RuntimeError("Something went wrong")
        
        result = handler_error(exception)
        
        assert result["message"] == "Internal Server Error"
        # Generic exceptions don't have code or status in the result

    def test_handler_error_internal_server_error(self):
        """Test handler_error with InternalServerError"""
        exception = InternalServerError("Server error")
        exception.response = None
        
        result = handler_error(exception)
        
        assert result["code"] == "internal_server_error"
        assert result["message"] == "Server error"
        assert result["status"] == 500


class TestExtractJsonFromStr:
    """Test cases for extract_json_from_str function"""

    def test_extract_json_from_str_valid_json(self):
        """Test extract_json_from_str with valid JSON"""
        json_str = '{"key": "value", "number": 123}'
        result = extract_json_from_str(json_str)
        
        assert result == {"key": "value", "number": 123}

    def test_extract_json_from_str_json_with_text(self):
        """Test extract_json_from_str with JSON embedded in text"""
        text = 'Some text before {"key": "value"} some text after'
        result = extract_json_from_str(text)
        
        assert result == {"key": "value"}

    def test_extract_json_from_str_with_code_blocks(self):
        """Test extract_json_from_str with code blocks"""
        text = '{"key": "value"} ```some code```'
        result = extract_json_from_str(text)
        
        assert result == {"key": "value"}

    def test_extract_json_from_str_nested_json(self):
        """Test extract_json_from_str with nested JSON"""
        json_str = '{"outer": {"inner": "value"}, "array": [1, 2, 3]}'
        result = extract_json_from_str(json_str)
        
        assert result == {"outer": {"inner": "value"}, "array": [1, 2, 3]}

    def test_extract_json_from_str_empty_string(self):
        """Test extract_json_from_str with empty string"""
        result = extract_json_from_str("")
        
        assert result is None

    def test_extract_json_from_str_none_input(self):
        """Test extract_json_from_str with None input"""
        result = extract_json_from_str(None)
        
        assert result is None

    def test_extract_json_from_str_no_json(self):
        """Test extract_json_from_str with no JSON content"""
        text = "This is just plain text without any JSON"
        result = extract_json_from_str(text)
        
        assert result is None

    def test_extract_json_from_str_invalid_json(self):
        """Test extract_json_from_str with invalid JSON"""
        text = '{"key": "value", "invalid": }'
        
        with patch('logging.info') as mock_log:
            result = extract_json_from_str(text)
            
            assert result is None
            mock_log.assert_called()

    @patch('logging.info')
    def test_extract_json_from_str_eval_fallback_success(self, mock_log):
        """Test extract_json_from_str with eval fallback success"""
        # Create a string that's not valid JSON but valid Python dict
        text = "{'key': 'value'}"  # Single quotes, not valid JSON but valid Python
        
        result = extract_json_from_str(text)
        
        assert result == {'key': 'value'}
        # Should log JSON load failure but eval should succeed
        assert mock_log.call_count >= 1

    @patch('logging.info')
    def test_extract_json_from_str_eval_fallback_failure(self, mock_log):
        """Test extract_json_from_str with eval fallback failure"""
        text = '{"key": invalid_syntax}'
        
        result = extract_json_from_str(text)
        
        assert result is None
        # Should log both JSON load failure and eval failure
        assert mock_log.call_count >= 2

    @patch('logging.warning')
    def test_extract_json_from_str_no_braces(self, mock_warning):
        """Test extract_json_from_str with no braces"""
        text = "No JSON braces here"
        
        result = extract_json_from_str(text)
        
        assert result is None
        mock_warning.assert_called_once()

    def test_extract_json_from_str_multiple_json_objects(self):
        """Test extract_json_from_str with multiple JSON objects"""
        text = '{"first": "value"} some text {"second": "value"}'
        result = extract_json_from_str(text)

        # The function extracts from first { to last }, which creates invalid JSON
        # So it should return None after both json.loads and eval fail
        assert result is None


class TestGetUrlBySelectAddress:
    """Test cases for get_url_by_select_address function"""

    def test_get_url_by_select_address_fixed_sh_domain(self):
        """Test get_url_by_select_address with .sh domain"""
        service = "test-service.sh"
        api_path = "/api/v1/test"
        
        with patch('logging.warning') as mock_warning:
            result = get_url_by_select_address(service, api_path)
            
            assert result == "http://test-service.sh/api/v1/test"
            mock_warning.assert_called_once()

    @patch('atta_ai_common.nacos.address.select_address')
    def test_get_url_by_select_address_dynamic_service(self, mock_select_address):
        """Test get_url_by_select_address with dynamic service discovery"""
        mock_select_address.return_value = "*************:8080"
        service = "test-service"
        api_path = "/api/v1/test"
        
        result = get_url_by_select_address(service, api_path)
        
        assert result == "http://*************:8080/api/v1/test"
        mock_select_address.assert_called_once_with(service, region="sh")

    @patch('atta_ai_common.nacos.address.select_address')
    def test_get_url_by_select_address_custom_region(self, mock_select_address):
        """Test get_url_by_select_address with custom region"""
        mock_select_address.return_value = "*************:8080"
        service = "test-service"
        api_path = "/api/v1/test"
        service_region = "bj"
        
        result = get_url_by_select_address(service, api_path, service_region)
        
        assert result == "http://*************:8080/api/v1/test"
        mock_select_address.assert_called_once_with(service, region="bj")

    def test_get_url_by_select_address_empty_api_path(self):
        """Test get_url_by_select_address with empty API path"""
        service = "test-service.sh"
        api_path = ""

        result = get_url_by_select_address(service, api_path)

        # urljoin with empty path doesn't add trailing slash
        assert result == "http://test-service.sh"

    def test_get_url_by_select_address_root_api_path(self):
        """Test get_url_by_select_address with root API path"""
        service = "test-service.sh"
        api_path = "/"
        
        result = get_url_by_select_address(service, api_path)
        
        assert result == "http://test-service.sh/"

    @patch('atta_ai_common.nacos.address.select_address')
    def test_get_url_by_select_address_complex_path(self, mock_select_address):
        """Test get_url_by_select_address with complex API path"""
        mock_select_address.return_value = "service.example.com:9000"
        service = "complex-service"
        api_path = "/api/v2/users/123/profile?include=details"
        
        result = get_url_by_select_address(service, api_path)
        
        expected = "http://service.example.com:9000/api/v2/users/123/profile?include=details"
        assert result == expected
        mock_select_address.assert_called_once_with(service, region="sh")
