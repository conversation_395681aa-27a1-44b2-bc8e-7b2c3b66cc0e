import json
import pytest
from unittest.mock import Mock, patch

from services.auth.firecrawl.firecrawl import FirecrawlAuth


class TestFirecrawlAuth:
    """Test cases for FirecrawlAuth"""

    def test_init_success(self):
        """Test successful initialization"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "test-api-key",
                "base_url": "https://custom.firecrawl.dev"
            }
        }
        
        auth = FirecrawlAuth(credentials)
        
        assert auth.api_key == "test-api-key"
        assert auth.base_url == "https://custom.firecrawl.dev"
        assert auth.credentials == credentials

    def test_init_default_base_url(self):
        """Test initialization with default base URL"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "test-api-key"
            }
        }
        
        auth = FirecrawlAuth(credentials)
        
        assert auth.api_key == "test-api-key"
        assert auth.base_url == "https://api.firecrawl.dev"

    def test_init_invalid_auth_type(self):
        """Test initialization with invalid auth type"""
        credentials = {
            "auth_type": "basic",
            "config": {
                "api_key": "test-api-key"
            }
        }
        
        with pytest.raises(ValueError, match="Invalid auth type, Firecrawl auth type must be Bearer"):
            FirecrawlAuth(credentials)

    def test_init_missing_api_key(self):
        """Test initialization with missing API key"""
        credentials = {
            "auth_type": "bearer",
            "config": {}
        }
        
        with pytest.raises(ValueError, match="No API key provided"):
            FirecrawlAuth(credentials)

    def test_init_none_api_key(self):
        """Test initialization with None API key"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": None
            }
        }
        
        with pytest.raises(ValueError, match="No API key provided"):
            FirecrawlAuth(credentials)

    def test_prepare_headers(self):
        """Test header preparation"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "test-api-key"
            }
        }
        
        auth = FirecrawlAuth(credentials)
        headers = auth._prepare_headers()
        
        expected_headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test-api-key"
        }
        assert headers == expected_headers

    @patch('services.auth.firecrawl.firecrawl.requests.post')
    def test_validate_credentials_success(self, mock_post):
        """Test successful credential validation"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "test-api-key",
                "base_url": "https://api.firecrawl.dev"
            }
        }
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        auth = FirecrawlAuth(credentials)
        result = auth.validate_credentials()
        
        assert result is True
        
        # Verify the request was made correctly
        expected_url = "https://api.firecrawl.dev/v1/crawl"
        expected_headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test-api-key"
        }
        expected_data = {
            "url": "https://example.com",
            "includePaths": [],
            "excludePaths": [],
            "limit": 1,
            "scrapeOptions": {"onlyMainContent": True},
        }
        
        mock_post.assert_called_once_with(expected_url, headers=expected_headers, json=expected_data)

    @patch('services.auth.firecrawl.firecrawl.requests.post')
    def test_validate_credentials_error_402(self, mock_post):
        """Test credential validation with 402 error"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "invalid-key"
            }
        }
        
        # Mock error response
        mock_response = Mock()
        mock_response.status_code = 402
        mock_response.json.return_value = {"error": "Payment required"}
        mock_post.return_value = mock_response
        
        auth = FirecrawlAuth(credentials)
        
        with pytest.raises(Exception, match="Failed to authorize. Status code: 402. Error: Payment required"):
            auth.validate_credentials()

    @patch('services.auth.firecrawl.firecrawl.requests.post')
    def test_validate_credentials_error_500(self, mock_post):
        """Test credential validation with 500 error"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "test-key"
            }
        }
        
        # Mock error response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.json.return_value = {"error": "Internal server error"}
        mock_post.return_value = mock_response
        
        auth = FirecrawlAuth(credentials)
        
        with pytest.raises(Exception, match="Failed to authorize. Status code: 500. Error: Internal server error"):
            auth.validate_credentials()

    @patch('services.auth.firecrawl.firecrawl.requests.post')
    def test_validate_credentials_error_with_text_response(self, mock_post):
        """Test credential validation with text error response"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "test-key"
            }
        }
        
        # Mock error response with text
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = '{"error": "Bad request"}'
        mock_post.return_value = mock_response
        
        auth = FirecrawlAuth(credentials)
        
        with pytest.raises(Exception, match="Failed to authorize. Status code: 400. Error: Bad request"):
            auth.validate_credentials()

    @patch('services.auth.firecrawl.firecrawl.requests.post')
    def test_validate_credentials_error_no_text(self, mock_post):
        """Test credential validation with no text in error response"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "test-key"
            }
        }
        
        # Mock error response without text
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = ""
        mock_post.return_value = mock_response
        
        auth = FirecrawlAuth(credentials)
        
        with pytest.raises(Exception, match="Unexpected error occurred while trying to authorize. Status code: 400"):
            auth.validate_credentials()

    def test_post_request(self):
        """Test _post_request method"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "test-key"
            }
        }
        
        auth = FirecrawlAuth(credentials)
        
        with patch('services.auth.firecrawl.firecrawl.requests.post') as mock_post:
            mock_response = Mock()
            mock_post.return_value = mock_response
            
            url = "https://test.com"
            data = {"test": "data"}
            headers = {"Authorization": "Bearer test"}
            
            result = auth._post_request(url, data, headers)
            
            assert result == mock_response
            mock_post.assert_called_once_with(url, headers=headers, json=data)
