import pytest
from unittest.mock import Mock, patch

from services.auth.watercrawl.watercrawl import WatercrawlAuth


class TestWatercrawlAuth:
    """Test cases for WatercrawlAuth"""

    def test_init_success(self):
        """Test successful initialization"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-api-key",
                "base_url": "https://custom.watercrawl.dev"
            }
        }
        
        auth = WatercrawlAuth(credentials)
        
        assert auth.api_key == "test-api-key"
        assert auth.base_url == "https://custom.watercrawl.dev"
        assert auth.credentials == credentials

    def test_init_default_base_url(self):
        """Test initialization with default base URL"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-api-key"
            }
        }
        
        auth = WatercrawlAuth(credentials)
        
        assert auth.api_key == "test-api-key"
        assert auth.base_url == "https://app.watercrawl.dev"

    def test_init_invalid_auth_type(self):
        """Test initialization with invalid auth type"""
        credentials = {
            "auth_type": "bearer",
            "config": {
                "api_key": "test-api-key"
            }
        }
        
        with pytest.raises(ValueError, match="Invalid auth type, WaterCrawl auth type must be x-api-key"):
            WatercrawlAuth(credentials)

    def test_init_missing_api_key(self):
        """Test initialization with missing API key"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {}
        }
        
        with pytest.raises(ValueError, match="No API key provided"):
            WatercrawlAuth(credentials)

    def test_init_none_api_key(self):
        """Test initialization with None API key"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": None
            }
        }
        
        with pytest.raises(ValueError, match="No API key provided"):
            WatercrawlAuth(credentials)

    def test_prepare_headers(self):
        """Test header preparation"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-api-key"
            }
        }
        
        auth = WatercrawlAuth(credentials)
        headers = auth._prepare_headers()
        
        expected_headers = {
            "Content-Type": "application/json",
            "X-API-KEY": "test-api-key"
        }
        assert headers == expected_headers

    @patch('services.auth.watercrawl.watercrawl.requests.get')
    def test_validate_credentials_success(self, mock_get):
        """Test successful credential validation"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-api-key",
                "base_url": "https://app.watercrawl.dev"
            }
        }
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        auth = WatercrawlAuth(credentials)
        result = auth.validate_credentials()
        
        assert result is True
        
        # Verify the request was made correctly
        expected_url = "https://app.watercrawl.dev/api/v1/core/crawl-requests/"
        expected_headers = {
            "Content-Type": "application/json",
            "X-API-KEY": "test-api-key"
        }
        
        mock_get.assert_called_once_with(expected_url, headers=expected_headers)

    @patch('services.auth.watercrawl.watercrawl.requests.get')
    def test_validate_credentials_custom_base_url(self, mock_get):
        """Test credential validation with custom base URL"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-api-key",
                "base_url": "https://custom.watercrawl.dev"
            }
        }
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        auth = WatercrawlAuth(credentials)
        result = auth.validate_credentials()
        
        assert result is True
        
        # Verify the request was made with custom URL
        expected_url = "https://custom.watercrawl.dev/api/v1/core/crawl-requests/"
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        assert args[0] == expected_url

    @patch('services.auth.watercrawl.watercrawl.requests.get')
    def test_validate_credentials_error_402(self, mock_get):
        """Test credential validation with 402 error"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "invalid-key"
            }
        }
        
        # Mock error response
        mock_response = Mock()
        mock_response.status_code = 402
        mock_response.json.return_value = {"error": "Payment required"}
        mock_get.return_value = mock_response
        
        auth = WatercrawlAuth(credentials)
        
        with pytest.raises(Exception, match="Failed to authorize. Status code: 402. Error: Payment required"):
            auth.validate_credentials()

    @patch('services.auth.watercrawl.watercrawl.requests.get')
    def test_validate_credentials_error_409(self, mock_get):
        """Test credential validation with 409 error"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-key"
            }
        }
        
        # Mock error response
        mock_response = Mock()
        mock_response.status_code = 409
        mock_response.json.return_value = {"error": "Conflict"}
        mock_get.return_value = mock_response
        
        auth = WatercrawlAuth(credentials)
        
        with pytest.raises(Exception, match="Failed to authorize. Status code: 409. Error: Conflict"):
            auth.validate_credentials()

    @patch('services.auth.watercrawl.watercrawl.requests.get')
    def test_validate_credentials_error_500(self, mock_get):
        """Test credential validation with 500 error"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-key"
            }
        }
        
        # Mock error response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.json.return_value = {"error": "Internal server error"}
        mock_get.return_value = mock_response
        
        auth = WatercrawlAuth(credentials)
        
        with pytest.raises(Exception, match="Failed to authorize. Status code: 500. Error: Internal server error"):
            auth.validate_credentials()

    @patch('services.auth.watercrawl.watercrawl.requests.get')
    def test_validate_credentials_error_with_text_response(self, mock_get):
        """Test credential validation with text error response"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-key"
            }
        }
        
        # Mock error response with text
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = '{"error": "Bad request"}'
        mock_get.return_value = mock_response
        
        auth = WatercrawlAuth(credentials)
        
        with pytest.raises(Exception, match="Failed to authorize. Status code: 400. Error: Bad request"):
            auth.validate_credentials()

    @patch('services.auth.watercrawl.watercrawl.requests.get')
    def test_validate_credentials_error_no_text(self, mock_get):
        """Test credential validation with no text in error response"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-key"
            }
        }
        
        # Mock error response without text
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = ""
        mock_get.return_value = mock_response
        
        auth = WatercrawlAuth(credentials)
        
        with pytest.raises(Exception, match="Unexpected error occurred while trying to authorize. Status code: 400"):
            auth.validate_credentials()

    def test_get_request(self):
        """Test _get_request method"""
        credentials = {
            "auth_type": "x-api-key",
            "config": {
                "api_key": "test-key"
            }
        }
        
        auth = WatercrawlAuth(credentials)
        
        with patch('services.auth.watercrawl.watercrawl.requests.get') as mock_get:
            mock_response = Mock()
            mock_get.return_value = mock_response
            
            url = "https://test.com"
            headers = {"X-API-KEY": "test"}
            
            result = auth._get_request(url, headers)
            
            assert result == mock_response
            mock_get.assert_called_once_with(url, headers=headers)


