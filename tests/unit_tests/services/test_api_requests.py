import json
import pytest
from datetime import datetime, UTC
from unittest.mock import Mo<PERSON>, patch, MagicMock

from services.api_requests import ApiRequestService
from models.model import ApiRequest


class TestApiRequestService:
    """Test cases for ApiRequestService"""

    def setup_method(self):
        """Setup test fixtures"""
        self.service = ApiRequestService()

    @patch('services.api_requests.db')
    def test_get_api_requests_by_request_id_found(self, mock_db):
        """Test getting API request by request_id when found"""
        # Arrange
        request_id = "test-request-id"
        mock_api_request = Mock(spec=ApiRequest)
        mock_api_request.request_id = request_id
        
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_api_request
        mock_db.session.query.return_value = mock_query

        # Act
        result = self.service.get_api_requests_by_request_id(request_id)

        # Assert
        assert result == mock_api_request
        mock_db.session.query.assert_called_once_with(ApiRequest)
        mock_query.filter.assert_called_once()
        mock_query.filter.return_value.first.assert_called_once()

    @patch('services.api_requests.db')
    def test_get_api_requests_by_request_id_not_found(self, mock_db):
        """Test getting API request by request_id when not found"""
        # Arrange
        request_id = "non-existent-request-id"
        
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = None
        mock_db.session.query.return_value = mock_query

        # Act
        result = self.service.get_api_requests_by_request_id(request_id)

        # Assert
        assert result is None

    @patch('services.api_requests.db')
    @patch('services.api_requests.json')
    def test_add_api_requests_success(self, mock_json, mock_db):
        """Test successfully adding API request"""
        # Arrange
        mock_json.dumps.side_effect = lambda x, **kwargs: json.dumps(x, **kwargs)
        
        request_data = {
            "tenant_id": "tenant-123",
            "path": "/api/test",
            "task_name": "test_task",
            "request_id": "req-123",
            "biz_name": "test_biz",
            "asyn": "0",
            "run_id": "run-123",
            "request": {"key": "value"},
            "response": {"result": "success"},
            "status": "completed",
            "error_code": "",
            "ip": "127.0.0.1",
            "api_token_id": "token-123"
        }

        mock_api_request = Mock(spec=ApiRequest)
        
        # Act
        with patch('services.api_requests.ApiRequest', return_value=mock_api_request):
            result = self.service.add_api_requests(**request_data)

        # Assert
        assert result == mock_api_request
        mock_db.session.add.assert_called_once_with(mock_api_request)
        mock_db.session.commit.assert_called_once()

    @patch('services.api_requests.db')
    @patch('services.api_requests.json')
    @patch('services.api_requests.logger')
    def test_add_api_requests_exception(self, mock_logger, mock_json, mock_db):
        """Test adding API request with exception"""
        # Arrange
        mock_json.dumps.side_effect = Exception("JSON serialization error")
        
        request_data = {
            "tenant_id": "tenant-123",
            "path": "/api/test",
            "task_name": "test_task",
            "request_id": "req-123",
            "biz_name": "test_biz",
            "asyn": "0",
            "run_id": "run-123",
            "request": {"key": "value"},
            "response": {"result": "success"},
            "status": "completed"
        }

        # Act
        result = self.service.add_api_requests(**request_data)

        # Assert
        assert result is None
        mock_logger.error.assert_called_once()

    @patch('services.api_requests.db')
    @patch('services.api_requests.json')
    @patch('services.api_requests.datetime')
    def test_async_task_update_api_requests_success(self, mock_datetime, mock_json, mock_db):
        """Test successfully updating async API request"""
        # Arrange
        mock_json.dumps.side_effect = lambda x, **kwargs: json.dumps(x, **kwargs)
        mock_now = datetime(2023, 1, 1, 12, 0, 0)
        mock_datetime.now.return_value = mock_now
        
        run_id = "run-123"
        response = {"result": "success"}
        status = "completed"
        async_task_start_time = datetime(2023, 1, 1, 11, 0, 0)
        error_code = ""

        mock_api_request = Mock(spec=ApiRequest)
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_api_request
        mock_db.session.query.return_value = mock_query

        # Act
        result = self.service.async_task_update_api_requests(
            run_id=run_id,
            response=response,
            status=status,
            async_task_start_time=async_task_start_time,
            error_code=error_code
        )

        # Assert
        assert result == mock_api_request
        assert mock_api_request.response == json.dumps(response, ensure_ascii=False)
        assert mock_api_request.status == status
        assert mock_api_request.error_code == error_code
        assert mock_api_request.async_task_start_time == async_task_start_time
        assert mock_api_request.async_task_finished_time == mock_now
        mock_db.session.commit.assert_called_once()

    @patch('services.api_requests.db')
    @patch('services.api_requests.logging')
    def test_async_task_update_api_requests_not_found(self, mock_logging, mock_db):
        """Test updating async API request when not found"""
        # Arrange
        run_id = "non-existent-run-id"
        
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = None
        mock_db.session.query.return_value = mock_query

        # Act
        result = self.service.async_task_update_api_requests(
            run_id=run_id,
            response={"result": "success"},
            status="completed",
            async_task_start_time=datetime.now()
        )

        # Assert
        assert result is None
        mock_logging.warning.assert_called_once()

    @patch('services.api_requests.db')
    @patch('services.api_requests.logger')
    def test_async_task_update_api_requests_exception(self, mock_logger, mock_db):
        """Test updating async API request with exception"""
        # Arrange
        run_id = "run-123"
        mock_db.session.query.side_effect = Exception("Database error")

        # Act
        result = self.service.async_task_update_api_requests(
            run_id=run_id,
            response={"result": "success"},
            status="completed",
            async_task_start_time=datetime.now()
        )

        # Assert
        assert result is None
        mock_logger.warning.assert_called_once()

    def test_service_initialization(self):
        """Test service can be initialized"""
        service = ApiRequestService()
        assert service is not None
        assert isinstance(service, ApiRequestService)
