import pytest
from unittest.mock import Mock, patch, MagicMock

from services.moderation_service import ModerationService
from models.model import App, AppModelConfig
from core.moderation.factory import ModerationOutputsResult


class TestModerationService:
    """Test cases for ModerationService"""

    def setup_method(self):
        """Setup test fixtures"""
        self.service = ModerationService()

    @patch('services.moderation_service.db')
    @patch('services.moderation_service.ModerationFactory')
    def test_moderation_for_outputs_success(self, mock_moderation_factory, mock_db):
        """Test successful moderation for outputs"""
        # Arrange
        app_id = "app-123"
        text = "This is test content"
        
        # Mock App
        mock_app = Mock(spec=App)
        mock_app.app_model_config_id = "config-123"
        mock_app.tenant_id = "tenant-123"
        
        # Mock AppModelConfig
        mock_app_model_config = Mock(spec=AppModelConfig)
        mock_app_model_config.sensitive_word_avoidance_dict = {
            "type": "openai_moderation",
            "config": {"api_key": "test-key"}
        }
        
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_app_model_config
        mock_db.session.query.return_value = mock_query
        
        # Mock ModerationFactory and its result
        mock_moderation_result = Mock(spec=ModerationOutputsResult)
        mock_moderation_instance = Mock()
        mock_moderation_instance.moderation_for_outputs.return_value = mock_moderation_result
        mock_moderation_factory.return_value = mock_moderation_instance

        # Act
        result = self.service.moderation_for_outputs(app_id, mock_app, text)

        # Assert
        assert result == mock_moderation_result
        mock_db.session.query.assert_called_once_with(AppModelConfig)
        mock_query.filter.assert_called_once()
        mock_moderation_factory.assert_called_once_with(
            "openai_moderation", 
            app_id, 
            "tenant-123", 
            {"api_key": "test-key"}
        )
        mock_moderation_instance.moderation_for_outputs.assert_called_once_with(text)

    @patch('services.moderation_service.db')
    def test_moderation_for_outputs_app_model_config_not_found(self, mock_db):
        """Test moderation when app model config is not found"""
        # Arrange
        app_id = "app-123"
        text = "This is test content"
        
        # Mock App
        mock_app = Mock(spec=App)
        mock_app.app_model_config_id = "config-123"
        mock_app.tenant_id = "tenant-123"
        
        # Mock database query returning None
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = None
        mock_db.session.query.return_value = mock_query

        # Act & Assert
        with pytest.raises(ValueError, match="app model config not found"):
            self.service.moderation_for_outputs(app_id, mock_app, text)

    @patch('services.moderation_service.db')
    @patch('services.moderation_service.ModerationFactory')
    def test_moderation_for_outputs_with_different_moderation_types(self, mock_moderation_factory, mock_db):
        """Test moderation with different moderation types"""
        # Arrange
        app_id = "app-123"
        text = "This is test content"
        
        # Mock App
        mock_app = Mock(spec=App)
        mock_app.app_model_config_id = "config-123"
        mock_app.tenant_id = "tenant-123"
        
        # Test different moderation types
        test_cases = [
            {
                "type": "keywords",
                "config": {"keywords": ["bad", "word"]}
            },
            {
                "type": "openai_moderation",
                "config": {"api_key": "test-key", "model": "text-moderation-latest"}
            },
            {
                "type": "custom_moderation",
                "config": {"endpoint": "https://api.example.com/moderate"}
            }
        ]
        
        for test_case in test_cases:
            # Mock AppModelConfig
            mock_app_model_config = Mock(spec=AppModelConfig)
            mock_app_model_config.sensitive_word_avoidance_dict = test_case
            
            # Mock database query
            mock_query = Mock()
            mock_query.filter.return_value.first.return_value = mock_app_model_config
            mock_db.session.query.return_value = mock_query
            
            # Mock ModerationFactory and its result
            mock_moderation_result = Mock(spec=ModerationOutputsResult)
            mock_moderation_instance = Mock()
            mock_moderation_instance.moderation_for_outputs.return_value = mock_moderation_result
            mock_moderation_factory.return_value = mock_moderation_instance

            # Act
            result = self.service.moderation_for_outputs(app_id, mock_app, text)

            # Assert
            assert result == mock_moderation_result
            mock_moderation_factory.assert_called_with(
                test_case["type"], 
                app_id, 
                "tenant-123", 
                test_case["config"]
            )

    @patch('services.moderation_service.db')
    @patch('services.moderation_service.ModerationFactory')
    def test_moderation_for_outputs_with_empty_text(self, mock_moderation_factory, mock_db):
        """Test moderation with empty text"""
        # Arrange
        app_id = "app-123"
        text = ""
        
        # Mock App
        mock_app = Mock(spec=App)
        mock_app.app_model_config_id = "config-123"
        mock_app.tenant_id = "tenant-123"
        
        # Mock AppModelConfig
        mock_app_model_config = Mock(spec=AppModelConfig)
        mock_app_model_config.sensitive_word_avoidance_dict = {
            "type": "openai_moderation",
            "config": {"api_key": "test-key"}
        }
        
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_app_model_config
        mock_db.session.query.return_value = mock_query
        
        # Mock ModerationFactory and its result
        mock_moderation_result = Mock(spec=ModerationOutputsResult)
        mock_moderation_instance = Mock()
        mock_moderation_instance.moderation_for_outputs.return_value = mock_moderation_result
        mock_moderation_factory.return_value = mock_moderation_instance

        # Act
        result = self.service.moderation_for_outputs(app_id, mock_app, text)

        # Assert
        assert result == mock_moderation_result
        mock_moderation_instance.moderation_for_outputs.assert_called_once_with("")

    @patch('services.moderation_service.db')
    @patch('services.moderation_service.ModerationFactory')
    def test_moderation_for_outputs_factory_exception(self, mock_moderation_factory, mock_db):
        """Test moderation when ModerationFactory raises exception"""
        # Arrange
        app_id = "app-123"
        text = "This is test content"
        
        # Mock App
        mock_app = Mock(spec=App)
        mock_app.app_model_config_id = "config-123"
        mock_app.tenant_id = "tenant-123"
        
        # Mock AppModelConfig
        mock_app_model_config = Mock(spec=AppModelConfig)
        mock_app_model_config.sensitive_word_avoidance_dict = {
            "type": "openai_moderation",
            "config": {"api_key": "test-key"}
        }
        
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_app_model_config
        mock_db.session.query.return_value = mock_query
        
        # Mock ModerationFactory to raise exception
        mock_moderation_factory.side_effect = Exception("Moderation factory error")

        # Act & Assert
        with pytest.raises(Exception, match="Moderation factory error"):
            self.service.moderation_for_outputs(app_id, mock_app, text)

    def test_service_initialization(self):
        """Test service can be initialized"""
        service = ModerationService()
        assert service is not None
        assert isinstance(service, ModerationService)
