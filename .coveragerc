[run]
omit =
    core/rag/*
    *dataset*
    *document_*
    core/helper/*
    core/ops/*
    core/workflow/nodes/http_request/*
    core/app/apps/advanced_chat/generate_task_pipeline.py
    core/indexing_runner.py
    core/workflow/workflow_app_generate_task_pipeline.py
    dify_plugin/interfaces/model/openai_compatible/llm.py
    core/provider_manager.py
    core/entities/provider_configuration.py
    core/workflow/nodes/llm/node.py
    services/account_service.py
    core/workflow/nodes/knowledge_retrieval/knowledge_retrieval_node.py
    services/external_knowledge_service.py
    core/agent/*_runner.py
    core/external_data_tool/api/api.py
    core/file/upload_file_parser.py
    core/moderation/*
    services


[report]
sort = Cover
skip_covered = False

[html]
skip_covered = False
directory = tests_coverage/htmlcov

[xml]
output = tests_coverage/coverage.xml


