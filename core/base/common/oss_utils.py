import logging
import os
import tempfile

from typing import Dict, Union

from atta_ai_common.oss.utils import parse_oss_path, OSS_PATH_PREFIX, get_oss_file


def head_oss_file(oss_file_path: str) -> Union[Dict, None]:
    """
    检查OSS文件是否存在
    :param oss_file_path: 形如"oss://primary/atta-attachments-test2/pics/2022-08-04/abc.jpg"，其中
                     primary是storage_name，atta-attachments-test2是bucket_name
    """
    oss_bucket, sub_path = parse_oss_path(oss_file_path=oss_file_path)
    filename = sub_path.split("/")[-1]
    try:
        meta = oss_bucket.head_object(sub_path)
    except Exception as e:
        logging.warning(f"检查OSS文件失败，错误信息：{e}")
        return {}
    
    if not meta:
        return {}
    file_size = meta.content_length
    mime_type = meta.content_type
    return {
        "filename": filename,
        "file_size": file_size,
        "mime_type": mime_type,
    }
    
    
def is_oss_file_path(oss_file_path: str) -> bool:
    """
    检查是否OSS文件
    """
    if not oss_file_path.startswith(OSS_PATH_PREFIX):
        return False
    return True


def get_oss_file_content(
    oss_file_path: str,
    local_file_path: str,
) -> bytes:
    """
    获取oss文件内容 
    """
    tmp_dir = tempfile.gettempdir()
    local_file = get_oss_file(oss_file_path, tmp_dir)
    with open(local_file, "rb") as f:
        content = f.read()
    # 安全删除文件
    try:
        os.remove(local_file)
    except Exception as e:
        pass
    return content