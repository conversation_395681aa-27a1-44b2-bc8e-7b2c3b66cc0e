import time
import logging
from urllib.parse import urljoin
import re
import json

from typing import Optional

from flask_restful import http_status_message
from werkzeug.exceptions import HTTPException
from core.errors.error import AppInvokeQuotaExceededError

def bool_asyn(value: Optional[str]) -> Optional[bool]:
    """对接口请求时的asyn参数进行布尔判断，非法参数返回None值"""
    if value.lower() in {"false", "0"}:
        return False
    elif value.lower() in {"true", "1"}:
        return True
    else:
        return None

    
def handler_error(e) -> dict:
    if isinstance(e, HTTPException):
        if e.response is not None:
            resp = e.get_response()
            return resp

        status_code = e.code
        default_data = {
            "code": re.sub(r"(?<!^)(?=[A-Z])", "_", type(e).__name__).lower(),
            "message": getattr(e, "description", http_status_message(status_code)),
            "status": status_code,
        }

        if (
            default_data["message"]
            and default_data["message"] == "Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)"
        ):
            default_data["message"] = "Invalid JSON payload received or JSON payload is empty."
    elif isinstance(e, ValueError):
        status_code = 400
        default_data = {
            "code": "invalid_param",
            "message": str(e),
            "status": status_code,
        }
    elif isinstance(e, AppInvokeQuotaExceededError):
        status_code = 429
        default_data = {
            "code": "too_many_requests",
            "message": str(e),
            "status": status_code,
        }
    else:
        status_code = 500
        default_data = {
            "message": http_status_message(status_code),
        }
    return default_data

def extract_json_from_str(s):
    """提取大模型json响应内容为dict"""
    # 找到第一个{和最后一个}的位置
    if not s:
        return None
    if '```' in s:
        s = s[:s.rfind('```')]
    start = s.find('{')
    end = s.rfind('}')
    json_dict = None
    if start != -1 and end != -1:
        # 截取这段内容
        json_str = s[start:end+1]
        # 将json字符串转换为字典
        try:
            json_dict = json.loads(json_str)
        except:
            logging.info(f"大模型结果json load失败")
        if not json_dict:
            try:
                json_dict = eval(json_str)
            except:
                logging.info(f"大模型结果eval失败")
    else:
        logging.warning(f"找不到解析起始位置: {s}")
    return json_dict


def get_url_by_select_address(service, api_path, service_region="sh") -> str:
    """获取服务ip"""
    if service.endswith(".sh"):
        logging.warning(f"使用固定地址访问 {service} {api_path}")
        return urljoin(f"http://{service}", api_path)
    
    from atta_ai_common.nacos.address import select_address
    return urljoin(f"http://{select_address(service, region=service_region)}", api_path)