import logging
import json
from urllib.parse import urljoin
from core.app.apps.workflow.app_config_manager import WorkflowAppConfig
from core.app.apps.base_app_generator import BaseAppGenerator

def get_url_by_select_address(service, api_path, service_region="sh") -> str:
    """获取服务ip"""
    service = "aihubserv.sitxtrt.sh"
    if service.endswith(".sh"):
        logging.warning(f"使用固定地址访问 {service} {api_path}")
        return urljoin(f"http://{service}", api_path)
    
    from atta_ai_common.nacos.address import select_address
    return urljoin(f"http://{select_address(service, region=service_region)}", api_path)


def validate_workflow_param(args, app_config: WorkflowAppConfig, inputs: dict, _bg: BaseAppGenerator):
    # 优先从variables中获取inputs
    input_variables_dict = {}
    input_variables = args.get("variables")
    if input_variables:
        try:
            input_variables_dict = json.loads(input_variables)
            if not isinstance(input_variables_dict, dict):
                raise ValueError("必须为json对象")
        except Exception as e:
            raise ValueError(f"参数variables非json格式: {str(e)}")

    for i in app_config.variables:
        # 先从input获取
        _d = inputs.get(i.variable)
        # 有variables则覆盖inputs
        if input_variables:
            mapping_value = i.mapping(input_variables_dict)
            if mapping_value is not None:
                _d = mapping_value
                logging.info(f"变量{i.variable}通过映射语法获取值成功")
        inputs[i.variable] = _d
        _bg._validate_inputs(value=_d, variable_entity=i)
    return inputs