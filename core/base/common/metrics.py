from atta_ai_prometheus_python import Counter, Histogram, Gauge

ai_studio_llm_call_duration = Counter(
    "ai_studio_llm_call_duration",
    "The duration of llm call",
    labelnames=["task_name", "version", "biz_name", "model_name", "platform", "status"]
)

ai_studio_llm_call_status = Counter(
    "ai_studio_llm_call_status",
    "The status of llm call",
    labelnames=["task_name", "version", "biz_name", "model_name", "platform", "status"]
)

ai_studio_task_run_duration = Counter(
    "ai_studio_task_run_duration",
    "The duration of task run",
    labelnames=["task_name", "version", "asyn", "response_mode"]
)

ai_studio_task_run_status = Counter(
    "ai_studio_task_run_status",
    "The status of task run",
    labelnames=["task_name", "version", "asyn", "response_mode", "status"]
)

ai_studio_request_accept = Counter(
    "ai_studio_request_accept",
    "The duration of request",
    labelnames=["task_name", "version", "asyn", "response_mode"]
)


def accept_request_metrics(task_name: str, version: str, asyn: str, response_mode: str) -> None:
    """记录请求接受次数"""
    try:
        ai_studio_request_accept.labels(task_name, version, asyn, response_mode).inc(1)
    except Exception:
        pass


def record_task_run_metrics(task_name: str, version: str, asyn: str, response_mode: str, status: str, duration: float) -> None:
    """记录任务运行次数"""
    try:
        version = version or ""
        ai_studio_task_run_duration.labels(task_name, version, asyn, response_mode).inc(duration)
        ai_studio_task_run_status.labels(task_name, version, asyn, response_mode, status).inc(1)
    except Exception:
        pass
    
    
def record_llm_call_metrics(task_name: str, version: str, biz_name: str, platform: str, model_name: str, status: str, duration: int) -> None:
    
    try:
        ai_studio_llm_call_duration.labels(task_name, version, biz_name, platform, model_name, status).inc(duration)
        ai_studio_llm_call_status.labels(task_name, version, biz_name, platform, model_name, status).inc(1)
    except Exception:
        pass