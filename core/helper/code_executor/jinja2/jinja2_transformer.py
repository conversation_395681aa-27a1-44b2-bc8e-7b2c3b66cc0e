import json
import jinja2
import logging
from textwrap import dedent

from core.helper.code_executor.template_transformer import TemplateTransformer

def to_json_filter(data):
    try: 
        return json.dumps(data, ensure_ascii=False)
    except:
        raise ValueError("使用了模版中的 tojson 过滤器，但输入不是有效的 python 对象")
    
def from_json_filter(data):
    try:
        return json.loads(data)
    except:
        raise ValueError("使用了模版中的 fromjson 过滤器，但输入不是有效的 JSON 对象")

# 创建 Jinja2 环境并添加过滤器
env = jinja2.Environment(
    extensions=['jinja2.ext.loopcontrols', 'jinja2.ext.do']  # 启用循环控制扩展
)
env.filters["tojson"] = to_json_filter  # 覆盖默认的 tojson
env.filters["fromjson"] = from_json_filter  # 新增fromjson

class Jinja2TemplateTransformer(TemplateTransformer):
    @classmethod
    def transform_response(cls, response: str) -> dict:
        """
        Transform response to dict
        :param response: response
        :return:
        """
        return {"result": cls.extract_result_str_from_response(response)}

    @classmethod
    def get_runner_script(cls) -> str:
        runner_script = dedent(f"""
            # declare main function
            def main(**inputs):
                import jinja2
                template = jinja2.Template('''{cls._code_placeholder}''')
                return template.render(**inputs)

            import json
            from base64 import b64decode

            # decode and prepare input dict
            inputs_obj = json.loads(b64decode('{cls._inputs_placeholder}').decode('utf-8'))

            # execute main function
            output = main(**inputs_obj)

            # convert output and print
            result = f'''<<RESULT>>{{output}}<<RESULT>>'''
            print(result)

            """)
        return runner_script

    @classmethod
    def get_preload_script(cls) -> str:
        preload_script = dedent("""
            import jinja2
            from base64 import b64decode

            def _jinja2_preload_():
                # prepare jinja2 environment, load template and render before to avoid sandbox issue
                template = jinja2.Template('{{s}}')
                template.render(s='a')

            if __name__ == '__main__':
                _jinja2_preload_()

            """)

        return preload_script

    @classmethod
    def run(cls, code: str, inputs: dict) -> dict:
        """
        Run code
        :param code: code
        :param inputs: inputs
        :return:
        """
        # 这里使用改写后的env处理
        t = env.from_string(code)
        return {"result": t.render(**inputs)}