from enum import Enum
from typing import Optional

from pydantic import BaseModel, model_validator

from core.app.entities.task_entities import WorkflowAppBlockingResponse
from typing import Optional

class ModelStatus(Enum):
    """
    Enum class for model status.
    """

    ACTIVE = "active"
    NO_CONFIGURE = "no-configure"
    QUOTA_EXCEEDED = "quota-exceeded"
    NO_PERMISSION = "no-permission"
    DISABLED = "disabled"


class AsyncResponseEntity(BaseModel):
    """
    workflow async response entity
    """
    request_id: str
    biz_name: str = ""
    code: Optional[str] = "normal"
    message: Optional[str] = ""
    status: Optional[int] = 200


class WorkflowAPIResponseEntity(WorkflowAppBlockingResponse):
    """
    workflow response entity
    """

    request_id: str
    biz_name: str = ""
    code: Optional[str] = "normal"
    message: Optional[str] = ""
    status: Optional[int] = 200
    
    @model_validator(mode="after")
    def validate_after(self):
        # data中判断状态赋值code
        if self.data.status == "succeeded":
            self.code = "normal"
            self.status = 200
        else:
            self.code = "node_execute_faild"
            self.message = self.data.error
            self.status = 400
        return self
    
    
class ChatflowAPIResponseEntity(BaseModel):
    """
    chatflow response entity
    """

    request_id: str
    biz_name: str = ""
    event: str = "message"
    task_id: Optional[str] = ""
    id: Optional[str] = ""
    mode: Optional[str] = ""
    conversation_id: Optional[str] = ""
    message_id: Optional[str] = ""
    answer: Optional[str] = ""
    metadata: Optional[dict] = {}
    created_at: Optional[int] = None
    code: Optional[str] = "normal"
    message: Optional[str] = ""
    status: Optional[int] = 200
