import os
import sys
import logging
import importlib.util
import datetime
import traceback
import json

from typing import List, Dict, Any, Optional, Sequence, Union, Generator

from core.model_runtime.entities.provider_entities import ProviderEntity
from core.plugin.entities.plugin_daemon import PluginModelProviderEntity, PluginToolProviderEntity
from core.model_runtime.entities.model_entities import AIModelEntity, ModelType
from core.entities.model_entities import ModelStatus
from core.model_runtime.entities.model_entities import Model<PERSON><PERSON>tyKey
from core.model_runtime.entities.common_entities import I18nObject
from core.model_runtime.entities.message_entities import PromptMessage
from core.model_runtime.entities.llm_entities import (
    LLMResult,
    LLMResultChunk,
    LLMUsage,
    LLMResultChunkDelta,
    AssistantPromptMessage
)

from dify_plugin import DifyPluginEnv
from dify_plugin.core.plugin_registration import PluginRegistration
from dify_plugin.interfaces.model.large_language_model import LargeLanguageModel
from dify_plugin.interfaces.model.text_embedding_model import TextEmbeddingModel as DifyEmbedding
from dify_plugin.interfaces.model.speech2text_model import Speech2TextModel as DifySpeech2Text
from dify_plugin.interfaces.model.tts_model import TTSModel as DifyTTS
from dify_plugin.interfaces.model.moderation_model import ModerationModel as DifyModeration
from dify_plugin.entities.model.message import (
    SystemPromptMessage as DifySystemMessage,
    UserPromptMessage as DifyUserMessage,
    AssistantPromptMessage as DifyAssistantMessage,
)
from dify_plugin.entities.model.llm import LLMResult
from dify_plugin.entities.tool import ToolRuntime
from configs import dify_config
from core.tools.entities.tool_entities import ToolInvokeMessage, ToolProviderEntityWithPlugin


class DifyPluginLoader:
    """加载本地 Dify 插件的类（单例模式）"""
    
    # 单例实例
    _instance = None
    _initialized = False
    
    def __new__(cls, plugin_dir: str = None):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super(DifyPluginLoader, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, plugin_dir: str = None, tenant_id: str = None):
        """
        初始化 Dify 插件加载器（只初始化一次）
        :param plugin_dir: 插件目录
        """
        # 如果已经初始化过，则直接返回
        if DifyPluginLoader._initialized:
            return
            
        if plugin_dir is None:
            logging.warning("DifyPluginLoader requires plugin_dir for initialization")
            return
            
        self.plugin_dir = plugin_dir
        self.plugins = {}
        self.plugin_models = {}
        self.tenant_id = ""
            
        # 加载所有插件
        self._load_plugins()
        
        # 标记为已初始化
        DifyPluginLoader._initialized = True
        
    def _load_plugins(self):
        """加载所有插件"""
        if not os.path.exists(self.plugin_dir):
            return
            
        for plugin_name in os.listdir(self.plugin_dir):
            plugin_path = os.path.join(self.plugin_dir, plugin_name)
            
            # 检查是否是目录且包含 manifest.yaml
            if os.path.isdir(plugin_path) and os.path.exists(os.path.join(plugin_path, "manifest.yaml")):
                try:
                    # 创建 PluginRegistration 实例
                    plugin_env = DifyPluginEnv(
                        INSTALL_METHOD="local",
                        RUNTIME_DIR=plugin_path,
                        MAX_REQUEST_TIMEOUT=30
                    )
                    
                    # 直接使用 PluginRegistration 而不是 Plugin
                    registration = PluginRegistration(plugin_env, plugin_path)
                    
                    # 存储插件注册信息
                    self.plugins[plugin_name] = registration
                    
                    # 加载插件中的模型
                    # self._load_plugin_models(plugin_name, registration)
                    
                    logging.info(f"Successfully loaded Dify plugin: {plugin_name}")
                except Exception as e:
                    logging.error(f"Failed to load Dify plugin {plugin_name}: {e} {traceback.format_exc()}")
    
    def get_model_providers(self) -> List[PluginModelProviderEntity]:
        """
        获取所有模型提供商
        :return: 模型提供商列表
        """
        providers = []
        
        for plugin_name, registration in self.plugins.items():
            try:
                for provider_registration in registration.models_mapping:
                    # 获取提供商信息
                    _provider = registration.models_mapping[provider_registration][0].model_dump_json()
                    provider_entity = ProviderEntity.model_validate_json(_provider)

                    # 创建 PluginModelProviderEntity
                    plugin_provider_entity = PluginModelProviderEntity(
                        id=f"atta/{plugin_name}",
                        created_at=datetime.datetime.now(),
                        updated_at=datetime.datetime.now(),
                        provider=provider_entity.provider,
                        tenant_id=self.tenant_id,
                        plugin_unique_identifier=plugin_name,
                        plugin_id=f"atta/{plugin_name}",
                        declaration=provider_entity,
                    )
                
                    providers.append(plugin_provider_entity)
            except Exception as e:
                logging.error(f"Failed to get provider info for plugin {plugin_name}: {e} {traceback.format_exc()}")
        
        return providers
    
    def get_model_schema(self, plugin_id: str, provider: str, model_type: ModelType, model: str):
        """
        获取模型架构
        :param provider: 提供商
        :param model_type: 模型类型
        :return: 模型架构
        """
        for plugin_name, registration in self.plugins.items():
            if not plugin_id.endswith(plugin_name):
                continue
            for provider_registration in registration.models_mapping:
                _provider = registration.models_mapping[provider_registration][0].model_dump_json()
                provider_entity = ProviderEntity.model_validate_json(_provider)
                if provider_entity.provider == provider:
                    model_schemas = registration.models_mapping[provider_registration][2].get(ModelType(model_type)).model_schemas
                    for i in model_schemas:
                        if i.model == model:
                            return AIModelEntity.model_validate_json(i.model_dump_json())
        return None
    
    
    def get_model_instance(self, plugin_id: str, provider: str, model_type: ModelType):
        """
        获取模型实例
        :param provider: 提供商
        :param model_type: 模型类型
        :return: 模型实例
        """
        for plugin_name, registration in self.plugins.items():
            if not plugin_id.endswith(plugin_name):
                continue
            for provider_registration in registration.models_mapping:
                _provider = registration.models_mapping[provider_registration][0].model_dump_json()
                provider_entity = ProviderEntity.model_validate_json(_provider)
                if provider_entity.provider == provider:
                    return registration.models_mapping[provider_registration][2].get(model_type)
        return None
    
    
    def get_tool_instance(self, tool_provider: str, tool_name: str):
        """
        获取工具实例
        :param provider: 提供商
        :return: 工具实例
        """
        for plugin_name, registration in self.plugins.items():
            if not tool_provider.endswith(plugin_name):
                continue
            for provider_registration in registration.tools_mapping:
                _provider = registration.tools_mapping[provider_registration][2].get(tool_name)
                if _provider:
                    return _provider
        return None
    
    
    def invoke_llm(
        self,
        plugin_id: str,
        provider: str,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        model_parameters: Optional[dict] = None,
        tools: Optional[list] = None,
        stop: Optional[list[str]] = None,
        stream: bool = True,
    )-> Union[LLMResult, Generator[LLMResultChunk, None, None]]:
        """调用LLM"""
        model_instance = self.get_model_instance(plugin_id, provider, ModelType.LLM)
        if isinstance(model_instance, LargeLanguageModel):
            res = model_instance._invoke(
                model=model,
                credentials=credentials,
                prompt_messages=prompt_messages,
                model_parameters=model_parameters,
                tools=tools,
                stop=stop,
                stream=stream,
            )
            if not stream:
                yield LLMResultChunk(
                    model=res.model,
                    prompt_messages=[PromptMessage.model_validate_json(i) for i in res.prompt_messages],
                    system_fingerprint=res.system_fingerprint,
                    delta=LLMResultChunkDelta(
                        index=0,
                        message=AssistantPromptMessage.model_validate_json(res.message.model_dump_json()),
                        usage=LLMUsage.model_validate_json(res.usage.model_dump_json()),
                        finish_reason="stop",
                    ),
                )
            else:
                yield from res
        else:
            raise ValueError(f"Model `{model}` not found for provider `{provider}`")
        
    def invoke_tool(
        self,
        tool_provider: str,
        tool_name: str,
        credentials: dict,
        tool_parameters: dict,
    )-> Generator[ToolInvokeMessage, None, None]:
        registration = self.plugins.get(tool_provider)
        if not registration:
            raise ValueError(f"Provider `{tool_provider}` not found")
        tool_cls = registration.get_tool_cls(tool_provider, tool_name)
        if tool_cls is None:
            raise ValueError(f"Tool `{tool_name}` not found for provider `{tool_provider}`")

        # instantiate tool
        tool = tool_cls(
            runtime=ToolRuntime(
                credentials=credentials,
                user_id="",
                session_id=""
            ),
            session=None,
        )

        # invoke tool
        return tool.invoke(tool_parameters)
        
    
    def fetch_tool_providers(self) -> list[PluginToolProviderEntity]:
        """
        Fetch tool providers for the given tenant.
        """
        providers = []
        for plugin_name, registration in self.plugins.items():
            try:
                for provider_registration in registration.tools_mapping:
                    _provider = json.loads(registration.tools_mapping[provider_registration][0].model_dump_json())
                    
                    # 给tool注入当前provider名称
                    for i in _provider["tools"]:
                        i["identity"]["provider"] = plugin_name
                    
                    provider_entity = ToolProviderEntityWithPlugin.model_validate_json(json.dumps(_provider))

                    # 创建 PluginModelProviderEntity
                    # provider 为目录名，插件id拼上atta前缀
                    plugin_provider_entity = PluginToolProviderEntity(
                        id=f"atta/{plugin_name}",
                        created_at=datetime.datetime.now(),
                        updated_at=datetime.datetime.now(),
                        provider=plugin_name,
                        tenant_id=self.tenant_id,
                        plugin_unique_identifier=plugin_name,
                        plugin_id=f"atta/{plugin_name}",
                        declaration=provider_entity,
                    )
                
                    providers.append(plugin_provider_entity)
            except Exception as e:
                logging.error(f"Failed to get provider info for plugin {plugin_name}: {e} {traceback.format_exc()}")
        
        return providers
    
    def fetch_tool_provider(self, provider: str) -> PluginToolProviderEntity:
        """
        Fetch tool provider 
        """
        registration = self.plugins.get(provider)
        if not registration:
            raise ValueError(f"Provider `{provider}` not found")
        
        try:
            for provider_registration in registration.tools_mapping:
                    _provider = json.loads(registration.tools_mapping[provider_registration][0].model_dump_json())
                    
                    # 给tool注入当前provider名称
                    for i in _provider["tools"]:
                        i["identity"]["provider"] = provider
                    
                    provider_entity = ToolProviderEntityWithPlugin.model_validate_json(json.dumps(_provider))

                    # 创建 PluginModelProviderEntity
                    # provider 为目录名，插件id拼上atta前缀
                    return PluginToolProviderEntity(
                        id=f"atta/{provider}",
                        created_at=datetime.datetime.now(),
                        updated_at=datetime.datetime.now(),
                        provider=provider,
                        tenant_id=self.tenant_id,
                        plugin_unique_identifier=provider,
                        plugin_id=f"atta/{provider}",
                        declaration=provider_entity,
                    )
        except Exception as e:
            logging.error(f"Failed to get provider info for plugin {provider}: {e} {traceback.format_exc()}")
        
        return None
    
custom_plugin_dir = dify_config.CUSTOM_PLUGIN_PATH

if os.path.exists(custom_plugin_dir):
    try:
        # 使用单例模式，全局只初始化一次
        # 第一次调用时会初始化，后续调用会直接使用已初始化的实例
        dify_loader = DifyPluginLoader(custom_plugin_dir)
    except Exception as e:
        logging.warning(f"Failed to load Dify plugins: {e} {traceback.format_exc()}")