# 创建一个 bridge.py 文件
import unittest
import pytest
from unittest.mock import Mock
from atta_ai_common.ut.config_patch import inject_config
from atta_ai_common.ut.mysql_patch import *
from atta_ai_common.ut.redis_patch import *

inject_config(
    {
        "mysql_url": "mysql:10032/atta_ai_studio",
        "mysql_username": "test",
        "mysql_password": "test",
        "spring.redis.host": "redis.sitxtrt.sh",
        "spring.redis.password": "dada",
        "spring.redis.port": "6379",
        "atta.storage.oss.primary.accessKeyId": "test",
        "atta.storage.oss.primary.accessKeySecret": "test",
        "atta.storage.oss.primary.endPoint": "test",
        "console_api_url": "http://atta-ai-stuido",
        "files_url": "http://atta-ai-stuido",
        "service_api_url": "http://atta-ai-stuido",
        "app_web_url": "http://dify-web",
        "console_web_url": "http://dify-web",
        "static_proxy_url": "http://*************:33128",
        "aihubserv_service_name": "aihubserv",
        "plugin_daemon_enabled": "false",
        "worker_concurrency": "2",
        "aliyun_oss_bucket_name": "atta-attachments-test2",
        "aliyun_oss_path": "ai-studio",
        "skywalking_enable": "true",
        "workflow_max_execution_time": "1200",
        "max_submit_count": "500",
        "marketplace_enabled": "false",
        "storage_type": "aliyun-oss",
        "redis_serialization_protocol": "2",
        "rpa_service_name": "atta-ai-rpa",
        "http_request_max_read_timeout": 600
    }
)


class PytestWrapper(unittest.TestCase):
    def test_pytest(self):
        
        pytest.main(["-v", "--cov=/Users/<USER>/Desktop/gitlab/atta-ai-studio/core", "/Users/<USER>/Desktop/gitlab/atta-ai-studio/tests/un"])
