import logging

from celery_program.base import common_task
from atta_ai_common.rabbitmq.library import MQ

from controllers.service_api.wraps import create_or_update_end_user_for_user_id
from core.app.entities.app_invoke_entities import InvokeFrom
from models.model import App, AppMode
from core.app.apps.advanced_chat.app_generator import AdvancedChatAppGenerator
from extensions.ext_database import db
from core.app.apps.workflow.app_generator import WorkflowAppGenerator
from services.workflow_service import WorkflowService
from celery_program.app import celery_app
from configs import dify_config
from libs.exception import BaseHTTPException
from celery.exceptions import SoftTimeLimitExceeded
from core.entities.service_api_entities import AsyncResponseEntity, ChatflowAPIResponseEntity

logger = logging.getLogger(__name__)

@celery_app.task(name="chat_messages", soft_time_limit=int(dify_config.CELERY_SOFT_TIME_LIMIT), time_limit=int(dify_config.CELERY_TIME_LIMIT))
@common_task(mq=MQ(routing_key="celery.chat_messages"), path="/v1/chat-messages")
def task_chat_message(request_id: str = "", biz_name: str = "", **kwargs):
    app_id = kwargs.get("app_id")
    args = kwargs.get("args")
    version = kwargs.get("version")
    workflow_id = kwargs.get("workflow_id")
    run_id = kwargs.get("run_id")
    app_model = db.session.query(App).filter(App.id == app_id).first()
    user = create_or_update_end_user_for_user_id(app_model, kwargs.get("user_id"))
    
    # 目前只支持chatflow应用异步
    if app_model.mode == AppMode.ADVANCED_CHAT.value:
        return chatflow_app_run(request_id, biz_name, run_id, app_model, user, args, workflow_id)
    else:
        raise ValueError(f"Invalid app mode {app_model.mode}")
        

def chatflow_app_run(
        request_id, 
        biz_name, 
        run_id, 
        app_model,
        user, 
        args, 
        workflow_id
    ):
    """chatflow应用执行"""
    workflow_svc = WorkflowService()
    workflow = workflow_svc.get_workflow_by_id(workflow_id)
    invoke_from = InvokeFrom.SERVICE_API
    try:
        response = AdvancedChatAppGenerator(
                request_id=request_id,
                biz_name=biz_name,
                run_id=run_id,
            ).generate(
                app_model=app_model,
                workflow=workflow,
                user=user,
                args=args,
                invoke_from=invoke_from,
                streaming=False,
            )
        response = ChatflowAPIResponseEntity(
            request_id=request_id,
            biz_name=biz_name,
            **response
        ).model_dump(mode="json")
        return response
    except SoftTimeLimitExceeded as e:
        logger.error(f"chatflow 执行超时")
        return AsyncResponseEntity(
            request_id=request_id,
            biz_name=biz_name,
            code="task_run_timeout",
            message="chatflow 执行超时"
        ).model_dump()
    except BaseHTTPException as e:
        logger.error(f"chatflow 执行主动异常: {e}")
        return AsyncResponseEntity(
            request_id=request_id,
            biz_name=biz_name,
            code=e.error_code,
            message=f"chatflow 执行主动异常 {str(e)}"
        ).model_dump()
    except Exception as e:
        logger.error(f"chatflow 执行未知异常: {e}")
        return AsyncResponseEntity(
            request_id=request_id,
            biz_name=biz_name,
            code="unknow",
            message="chatflow 执行未知异常"
        ).model_dump()