import logging

from celery_program.base import common_task
from atta_ai_common.rabbitmq.library import MQ

from celery import shared_task
from controllers.service_api.wraps import create_or_update_end_user_for_user_id
from core.app.entities.app_invoke_entities import InvokeFrom
from models.model import App
from services.app_generate_service import AppGenerateService
from extensions.ext_database import db
from core.app.apps.workflow.app_generator import WorkflowAppGenerator
from services.workflow_service import WorkflowService
from celery_program.app import celery_app
from celery.exceptions import SoftTimeLimitExceeded
from libs.exception import BaseHTTPException
from core.entities.service_api_entities import WorkflowAPIResponseEntity, AsyncResponseEntity
from configs import dify_config

logger = logging.getLogger(__name__)

@celery_app.task(name="workflow_run", soft_time_limit=int(dify_config.CELERY_SOFT_TIME_LIMIT), time_limit=int(dify_config.CELERY_TIME_LIMIT))
@common_task(mq=MQ(routing_key="celery.workflow_run"), path="/v1/workflows/run")
def task_workflow_run(request_id: str = "", biz_name: str = "", **kwargs):
    args = {}
    app_id = kwargs.get("app_id")
    args = kwargs.get("args")
    workflow_id = kwargs.get("workflow_id")
    run_id = kwargs.get("run_id")
    app_model = db.session.query(App).filter(App.id == app_id).first()
    user = create_or_update_end_user_for_user_id(app_model, kwargs.get("user_id"))
    
    # 获取workflow版本
    workflow_svc = WorkflowService()
    workflow = workflow_svc.get_workflow_by_id(workflow_id)
    invoke_from = InvokeFrom.SERVICE_API
    try:
        response = WorkflowAppGenerator(
                request_id=request_id,
                biz_name=biz_name,
                run_id=run_id,
            ).generate(
            app_model=app_model,
            workflow=workflow,
            user=user,
            args=args,
            invoke_from=invoke_from,
            streaming=False,
            call_depth=0,
            workflow_thread_pool_id=None,
        )
        response = WorkflowAPIResponseEntity(
            request_id=request_id,
            biz_name=biz_name,
            **response
        ).model_dump(mode="json")
        return response
    except SoftTimeLimitExceeded as e:
        logger.error(f"workflow 执行超时")
        return AsyncResponseEntity(
            request_id=request_id,
            biz_name=biz_name,
            code="task_run_timeout",
            message="workflow 执行超时"
        ).model_dump()
    except BaseHTTPException as e:
        logger.error(f"workflow 执行主动异常: {e}")
        return AsyncResponseEntity(
            request_id=request_id,
            biz_name=biz_name,
            code=e.error_code,
            message=f"workflow 执行主动异常 {str(e)}"
        ).model_dump()
    except Exception as e:
        logger.error(f"workflow 执行未知异常: {e}")
        return AsyncResponseEntity(
            request_id=request_id,
            biz_name=biz_name,
            code="unknow",
            message="workflow 执行未知异常"
        ).model_dump()