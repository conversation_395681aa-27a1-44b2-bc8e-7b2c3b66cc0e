import datetime
import functools
import logging
import traceback
import json

from copy import copy
from copy import deepcopy
from typing import Iterable, List, Union, Optional, Any
from dataclasses import asdict
from celery import current_task

from atta_ai_common.configs.package_config import common_config
from atta_ai_common.rabbitmq.library import MQ
from atta_ai_common.utils.service_deployment import config_opts, get_service_name, LOCAL_DEV_MODE
from atta_ai_common.utils.common import mongo_api_record_masking, report_api_error_prometheus
from atta_ai_common.celery.decorator import report_async_prometheus
from atta_ai_common.rabbitmq.sender import RabbitmqSender
from atta_ai_common.utils.common import common_error_catch_decorator
from services.api_requests import ApiRequestService
from controllers.service_api.wraps import handler_error
from contexts.svc_ctx import set_svc_ctx
from core.base.common.metrics import record_task_run_metrics


def common_task(
    mq: Optional[Union[MQ, Iterable[MQ]]] = None,
    path: str = ""
):
    """dify异步任务通用处理
    """
    def real_decorator(task_func):
        @functools.wraps(task_func)
        def task_decorator_wrapper(*args, **kwargs):
            task_name = kwargs.get("task_name")
            version = kwargs.get("version")
            if mq is None:
                current_mq = MQ()  # 不传MQ参数时默认创建
            else:
                current_mq = copy(mq)
            if current_mq.routing_key is None:
                current_mq.routing_key = f"celery.{task_name}"
            if current_mq.exchange is None:
                project_keyword = get_service_name()[
                    8:
                ]  # 从"atta-ai-xxx"中提取项目（服务）关键词xxx
                current_mq.exchange = f"celery.ai.{project_keyword}.result"
            async_task_start_time = datetime.datetime.now()
            
            request_id = kwargs.get("request_id")
            biz_name = kwargs.get("biz_name")
            routing_key = kwargs.get("routing_key")
            # 如有调用方传入的routing_key参数，则优先使用传入值
            if routing_key:
                current_mq.routing_key = routing_key
            request_info: str = "\t".join(
                [
                    f"task_name: {task_name}",
                    f"version: {version}",
                    f"request_id: {request_id}",
                    f"biz_name: {biz_name}",
                    f"routing_key: {current_mq.routing_key}",
                ]
            )
            
            api_request_service = ApiRequestService()
            api_request_data = {
                "run_id": kwargs.get("run_id"),
                "async_task_start_time": async_task_start_time,
                "status": "finished",
                "error_code": ""
            }
            calc_result = {}
            error_message = ""
            try:
                logging.info(f"异步任务开始执行: {task_func.__name__}\t{request_info}")
                set_svc_ctx(
                    task_name=task_name,
                    version=version,
                    request_id=request_id,
                    biz_name=biz_name,
                    run_id=kwargs.get("run_id"),
                    data={
                        "streaming": False
                    } # 异步任务只有阻塞响应
                )
                calc_result = task_func(*args, **kwargs)
                api_request_data["response"] = calc_result
                if calc_result is None:
                    raise ValueError(
                        "异步任务函数的返回值为None，请将与预定义字段相匹配的JSON形式业务结果作为异步任务函数的返回值。"
                    )
            except Exception as e:
                report_api_error_prometheus(config_opts.service_name, path, "async")
                calc_result = handler_error(e)
                api_request_data["status"] = "error"
                api_request_data["response"] = calc_result
                api_request_data["error_code"] = calc_result.get("code")
                error_message = calc_result.get("message", "")
                raise e
            finally:
                if api_request_data["status"] == "error":
                    logging.error(
                        f"任务执行失败, task_name: {task_name}, version: {version}," \
                        f" error_code: {api_request_data['error_code']}," \
                        f" error_msg: {error_message}"
                    )
                # 发送MQ
                try:
                    send_api_result(
                        mq=current_mq,
                        result=calc_result,
                    )
                except Exception as e:
                    api_request_data["status"] = "error"
                    api_request_data["error_code"] = "mq_send_error"
                    report_api_error_prometheus(config_opts.service_name, path, "async")
                    logging.error(f"异步任务发送MQ失败: {str(e)}")
                    raise e
                finally:
                    api_request_service.async_task_update_api_requests(**api_request_data)
            
                # 任务成功才记录任务耗时
                if api_request_data["status"] == "finished":
                    dump_celery_metrics(
                        path,
                        kwargs.get("request_time"), 
                        datetime.datetime.now(), 
                        async_task_start_time
                    )
                time_cost_float = (datetime.datetime.now() - async_task_start_time).total_seconds()
                time_cost = "{:.3f}".format(time_cost_float)
                record_task_run_metrics(
                    task_name,
                    version,
                    "1",
                    "blocking",
                    api_request_data["status"],
                    time_cost_float
                )
                logging.info(f"异步任务执行结束: {task_func.__name__}\t{request_info}\t耗时: {time_cost} s")

        @functools.wraps(task_func)
        def wrapper_celery(*args, **kwargs):
            run_task_with_span(task_decorator_wrapper, *args, **kwargs)
        
        # 非开发环境且开启skywalking
        if not LOCAL_DEV_MODE and common_config.skywalking_enable:
            return wrapper_celery
        else:
            return task_decorator_wrapper

    return real_decorator


def run_task_with_span(task_func, *args, **kwargs):
    """celery work运行任务时在环境中注入消息中的调用链信息"""
    from skywalking import Layer, Component
    from skywalking.trace.tags import TagCeleryParameters
    from skywalking.trace.carrier import Carrier
    from skywalking.trace.context import get_context
    from atta_ai_common.utils.tracing import get_skywalking_trace_id
    req = current_task.request_stack.top
    # 使用任务名做api_path
    api_path = kwargs.get("task_name", "unknown")
    op = f'celery{api_path}'
    carrier = Carrier()

    for item in carrier:
        val = req.get(item.key)
        if val:
            item.val = val

    context = get_context()
    origin = req.get('origin')

    if origin:
        span = context.new_entry_span(op=op, carrier=carrier)
        span.peer = origin.split('@', 1)[-1]
    else:
        span = context.new_local_span(op=op)

    with span:
        setattr(req, "_trace_id", get_skywalking_trace_id())
        span.layer = Layer.MQ
        span.component = Component.Celery
        params = f'request_id: {kwargs.get("request_id")}, biz_name: {kwargs.get("biz_name")}'
        span.tag(TagCeleryParameters(params))
        return task_func(*args, **kwargs)


@common_error_catch_decorator(retry=True)
def send_api_result(
    mq: MQ,
    result: Any
) -> dict:
    """发送文本内容至RabbitMQ，并返回接口同步模板的结果"""
    rabbitmq_sender_init_params = asdict(mq)
    sender = RabbitmqSender(
        **rabbitmq_sender_init_params
    )  # 由于每次发完信息后channel关闭，所以这里每次重新实例化RabbitmqSender
    sender.send_message(send_message=json.dumps(result, ensure_ascii=False))
    
    
def dump_celery_metrics(task_name, request_time, timestamp, async_task_start_time):
    """接口成功后上报Celery的Prometheus监控指标，只能在task执行的时候触发指标更新"""
    try:
        request_time = datetime.datetime.fromisoformat(str(request_time))
        duration_call_to_finish = (timestamp - request_time).total_seconds()
        duration_call_to_task_start = (async_task_start_time - request_time).total_seconds()
        duration_task_start_to_finish = (timestamp - async_task_start_time).total_seconds()
        if duration_task_start_to_finish < 0:  # timestamp未更新，说明异步任务未成功结束
            logging.warning(f"异步任务未成功执行，timestamp未更新，不记录Prometheus Metric")
            return
        report_async_prometheus(config_opts.service_name, task_name, duration_call_to_task_start,
                                duration_call_to_finish, duration_task_start_to_finish)
    except Exception as e:
        logging.warning(f"记录prome失败{str(e)} {traceback.format_exc()}")