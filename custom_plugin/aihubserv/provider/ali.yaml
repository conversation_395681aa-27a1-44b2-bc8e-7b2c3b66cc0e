provider: ali
label:
  en_US: 阿里通义千问
description:
  en_US: Models provided by OpenAI, such as GPT-3.5-Turbo and GPT-4.
  zh_Hans: OpenAI 提供的模型，例如 GPT-3.5-Turbo 和 GPT-4。
icon_small:
  en_US: ali_icon_s_en.png
icon_large:
  en_US: ali_icon_l_en.png
background: "#E5E7EB"
help:
  title:
    en_US: Get your API Key from OpenAI
    zh_Hans: 从 OpenAI 获取 API Key
  url:
    en_US: https://platform.openai.com/account/api-keys
supported_model_types:
  - llm
  - text-embedding
  - speech2text
  - moderation
  - tts
configurate_methods:
  - predefined-model
models:
  llm:
    predefined:
      - "models/llm/ali/*.yaml"
    position: "models/llm/ali/_position.yaml"
extra:
  python:
    provider_source: custom_plugin/aihubserv/provider/openai.py
    model_sources:
      - "custom_plugin/aihubserv/models/llm/ali.py"
