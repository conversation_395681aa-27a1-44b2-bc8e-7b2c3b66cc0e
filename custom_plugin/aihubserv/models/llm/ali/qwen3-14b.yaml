# for more details, please refer to https://help.aliyun.com/zh/model-studio/getting-started/models
model: qwen3-14b
label:
  en_US: qwen3-14b
model_type: llm
features:
  - multi-tool-call
  - agent-thought
  - stream-tool-call
model_properties:
  mode: chat
  context_size: 131072
parameter_rules:
  # - name: temperature
  #   use_template: temperature
  #   type: float
  #   default: 0.3
  #   min: 0.0
  #   max: 2.0
  #   help:
  #     zh_Hans: 用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。
  #     en_US: Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected, the generated results are more certain.
  # - name: max_tokens
  #   use_template: max_tokens
  #   type: int
  #   default: 8192
  #   min: 1
  #   max: 8192
  #   help:
  #     zh_Hans: 用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。
  #     en_US: It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time.
  # - name: top_p
  #   use_template: top_p
  #   type: float
  #   default: 0.8
  #   min: 0.1
  #   max: 0.9
  #   help:
  #     zh_Hans: 生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。
  #     en_US: The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated.
  # - name: top_k
  #   type: int
  #   min: 0
  #   max: 99
  #   label:
  #     zh_Hans: 取样数量
  #     en_US: Top k
  #   help:
  #     zh_Hans: 生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。
  #     en_US: The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated.
  # - name: seed
  #   required: false
  #   type: int
  #   default: 1234
  #   label:
  #     zh_Hans: 随机种子
  #     en_US: Random seed
  #   help:
  #     zh_Hans: 生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。
  #     en_US: The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time.
  # - name: repetition_penalty
  #   required: false
  #   type: float
  #   default: 1.1
  #   label:
  #     zh_Hans: 重复惩罚
  #     en_US: Repetition penalty
  #   help:
  #     zh_Hans: 用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。
  #     en_US: Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment.
  # - name: enable_thinking
  #   required: false
  #   type: boolean
  #   default: true
  #   label:
  #     zh_Hans: 思考模式
  #     en_US: Thinking mode
  #   help:
  #     zh_Hans: 是否开启思考模式。
  #     en_US: Whether to enable thinking mode.
  # - name: thinking_budget
  #   required: false
  #   type: int
  #   default: 512
  #   min: 1
  #   max: 8192
  #   label:
  #     zh_Hans: 思考长度限制
  #     en_US: Thinking budget
  #   help:
  #     zh_Hans: 思考过程的最大长度，只在思考模式为true时生效。
  #     en_US: The maximum length of the thinking process, only effective when thinking mode is true.
  - name: response_format
    use_template: response_format
  - name: bak_model
    help:
      zh_Hans: 当主模型失败时使用相同的prompt更换模型重试，目前只支持openai系列模型
      en_US: When the main model fails, use the same prompt to replace the model and retry. Currently, only openai series models are supported
    label:
      zh_Hans: 降级模型
      en_US: bak model
    type: string
    required: false
    options:
      - gpt-4o
      - gpt-4o-mini
      - gpt-4.1
      - gpt-4.1-mini  
  - name: retry_wait
    label:
      zh_Hans: 重试等待时间（秒）
      en_US: Retry Wait Time
    type: float
    default: 0.5
  - name: max_retries
    label:
      zh_Hans: 最大重试次数
      en_US: Maximum Retries
    type: int
    default: 2
pricing:
  input: '0.001'
  output: '0.004'
#  output_thinking: '0.01'
  unit: '0.001'
  currency: RMB