import json
import logging
import time
import asyncio
import uuid
import hashlib
import requests
import tik<PERSON>en

from urllib.parse import urljoin
from typing import Any, AsyncIterator, Dict, Iterator, List, Mapping, Optional, Union
from typing import (
    Any,
    AsyncIterator,
    Callable,
    Dict,
    Iterator,
    List,
    Mapping,
    Optional,
    Tuple,
    Type,
    Union,
)

from requests.exceptions import RequestException
from configs import dify_config
from contexts.svc_ctx import get_svc_ctx
from dify_plugin import LargeLanguageModel

from dify_plugin.entities.model.llm import (
    LLMUsage,
    LLMResult,
    LLMResultChunk,
    LLMResultChunkDelta,
)
from collections.abc import Generator
from dify_plugin.entities.model.message import (
    AssistantPromptMessage,
    ImagePromptMessageContent,
    PromptMessage,
    PromptMessageContentType,
    PromptMessageTool,
    SystemPromptMessage,
    TextPromptMessageContent,
    ToolPromptMessage,
    UserPromptMessage,
)
from dify_plugin.errors.model import InvokeAuthorizationError, InvokeBadRequestError, InvokeConnectionError, InvokeError, InvokeRateLimitError, InvokeServerUnavailableError
from openai.types import Completion
from core.base.common.service import get_url_by_select_address
from pydantic import TypeAdapter


MAX_REQUEST_LENGTH = 128


class ChatAttaException(Exception):
    """Exception raised when the DeepInfra API returns an error."""

    pass


class ChatAttaParrmException(Exception):
    """Exception raised when the DeepInfra API returns an error."""

    pass


class AttaAIHubLLM(LargeLanguageModel):
    """aihubserv调用大模型"""
    
    request_timeout: int = 60
    retry_wait: int = 0.5
    max_retries: int = 2

    def _url(self, stream=False) -> str:
        """Get the URL for the request."""
        api_path = dify_config.AIHUBSERV_LOW_LEVEL_API
        if stream:
            api_path = dify_config.AIHUBSERV_LOW_LEVEL_STREAM_API
        return get_url_by_select_address(
            dify_config.AIHUBSERV_SERVICE_NAME,
            api_path,
            service_region=dify_config.AIHUBSERV_SERVICE_REGION
        )

    def _headers(self) -> dict:
        return {"feign_access_token": dify_config.FEIGN_ACCESS_TOKEN}
    
    
    def _gen_request_id(self, req_id, agent_name) -> str:
        # 取前两个字符的前2
        agent_name_words = agent_name.split('_')
        agent_name_uni = "".join([i[:2] for i in agent_name_words])
        prefix = f"{uuid.uuid4().hex[:6]}_{agent_name_uni}"
        if len(prefix + "_" + req_id) > MAX_REQUEST_LENGTH:
            hash_object = hashlib.md5()
            hash_object.update(req_id.encode("utf-8"))
            r = f"{prefix}_{hash_object.hexdigest()}"
        else:
            r = f"{prefix}_{req_id}"
        return r

    def _body(self, messages: list[PromptMessage], req_id: str, task_name: str, model_parameters: dict, **kwargs) -> str:
        """处理body"""
        model_name = kwargs.get("model_name")
        platform = kwargs.get("platform")
        if not all([req_id, task_name, model_name, platform]):
            raise ChatAttaParrmException(
                f"request_id、task_name、 model_name、platform 不能为空{[req_id, task_name, model_name, platform]}"
            )
        data = {
            "promptIdentity": task_name,
            "serviceName": dify_config.SERVICE_NAME,
            "busCode": task_name,
            "chatRequest": {},
        }
        data["chatRequest"]["platform"] = platform
        data["chatRequest"]["model"] = "qqq" or model_name
        messages_list = []
        for i in messages:
            if not isinstance(i.content, list):
                i.content = [TextPromptMessageContent(data=i.content)]
            _m = json.loads(i.model_dump_json())
            for _c in _m.get("content", []):
                _type = _c.pop("type", None)
                if _type == "image":
                    _c["data"] = ""
                    _c["image_url"] = {
                        "url": _c.pop('url', '') or f"data:{_c.get('mime_type')};base64,{_c.pop('base64_data', '')}"
                    }
                    _c["datatype"] = "image_url"
                else:
                    _c["datatype"] = _type
            messages_list.append(_m)
        data["chatRequest"]["messages"] = messages_list
        response_format = model_parameters.get("response_format")
        if response_format:
            response_format = response_format.lower()
            if response_format == "json_schema":
                json_schema = model_parameters.get("json_schema")
                if not json_schema:
                    raise ValueError("Must define JSON Schema when the response format is json_schema")
                try:
                    schema = TypeAdapter(dict[str, Any]).validate_json(json_schema)
                    schema["strict"] = False
                    schema["schema"]["additional_properties"] = False
                except Exception as exc:
                    raise ValueError(f"not correct json_schema format: {json_schema}") from exc
                model_parameters.pop("json_schema")
                kwargs["response_format"] = "json_schema"
                kwargs["response_json_schema"] = schema
            elif response_format == "json_object":
                kwargs["response_format"] = "json_object"
            else:
                kwargs["response_format"] = response_format

        extra_params = [
            "temperature",
            "top_p",
            "n",
            "stop",
            "seed",
            "max_tokens",
            "presence_penalty",
            "frequency_penalty",
            "response_format",
            "response_json_schema",
            "tools",
            "tool_choice",
        ]

        for i in extra_params:
            if kwargs.get(i) is not None:
                data["chatRequest"][i] = kwargs.get(i)

        return data
    
    def validate_credentials(self, model: str, credentials: dict) -> None:
        return None
    
    def get_num_tokens(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        tools: Optional[list[PromptMessageTool]] = None,
    ) -> int:
        return 0
    
    def _get_usage(self, usage: dict):
        """提取llm请求中的usage"""
        try:
            return LLMUsage(
                prompt_tokens=usage.get("prompt_tokens", 0),
                prompt_unit_price=usage.get("prompt_unit_price", 0),
                prompt_price_unit=usage.get("prompt_price_unit", 0),
                prompt_price=usage.get("prompt_price", 0),
                completion_tokens=usage.get("completion_tokens", 0),
                completion_unit_price=usage.get("completion_unit_price", 0),
                completion_price_unit=usage.get("completion_price_unit", 0),
                completion_price=usage.get("completion_price", 0),
                total_tokens=usage.get("total_tokens", 0),
                total_price=usage.get("total_price", 0),
                currency=usage.get("currency", "USD"),
                latency=usage.get("latency", 0)
            ).model_dump()
        except Exception as e:
            logging.warning(f"获取LLMUsage失败: {e}")
            return LLMUsage.empty_usage().model_dump()
        

    def _invoke(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: Optional[list[PromptMessageTool]] = None,
        stop: Optional[list[str]] = None,
        stream: bool = False,
        user: Optional[str] = None,
        **kwargs
    ) -> Union[LLMResult, Generator]:
        """同步run"""
        platform = kwargs.pop("platform", self.platform)
        bak_platform = kwargs.pop("bak_platform", self.bak_platform)
        bak_model_name = model_parameters.get("bak_model")
        request_timeout = model_parameters.get("timeout") or 60
        retry_wait = model_parameters.get("retry_wait") or 0
        max_retries = model_parameters.get("max_retries") or 0
        retry = 0
        to_bak = False
        task_name, _, req_id, _, _, _= get_svc_ctx()
        req = self._body(prompt_messages, req_id, task_name=task_name, model_name=model, platform=platform, model_parameters=model_parameters, **kwargs)
        # stream = False
        while True:
            req["requestId"] = self._gen_request_id(req_id, kwargs.get("agent_name", ""))
            logging.info(f"生成LLM请求request_id: {req['requestId']}, 是否流式调用: {stream}, 模型: {model}, platform: {platform}")
            try:
                response = requests.post(self._url(stream=stream), json=req, headers=self._headers(), timeout=request_timeout, stream=stream)
                if not stream:
                    data = response.json()
                    if not data["value"]:
                        logging.warning(f"aihubserv请求失败, request_id: {req['requestId']}, response: {data}")
                        raise ChatAttaException("aihubserv请求失败")
                    pool_content = data["value"]
                    if not pool_content.get("succeeded"):
                        logging.warning(f"pool返回结果异常：request_id: {req['requestId']}, response: {pool_content}")
                        raise ChatAttaException("pool返回结果异常")
                    return LLMResult(
                        model=model,
                        prompt_messages=prompt_messages,
                        message=AssistantPromptMessage(**pool_content["value"]["choices"][0]["message"]),
                        system_fingerprint=pool_content["value"]["system_fingerprint"],
                        usage=self._get_usage(pool_content["value"]["usage"])
                    )
                else:
                    return self._handle_generate_stream_response(req["chatRequest"]["model"], response, prompt_messages)
            except ChatAttaParrmException as e:
                # 参数校验错误直接抛出
                raise e
            # 请求结果异常则作渠道重试
            except ChatAttaException as e:
                retry += 1
                if retry > max_retries:
                    if to_bak or not (bak_platform or bak_model_name):
                        logging.error(f"aihubserv请求失败超过最大重试次数{max_retries}, {req['requestId']}")
                        break
                    else:
                        to_bak = True
                        req["chatRequest"]["platform"] = bak_platform
                        req["chatRequest"]["model"] = bak_model_name
                        logging.warning(f"aihubserv重试最大次数，切换备用渠道进行尝试: {bak_platform}, {bak_model_name}")
                        retry = 0
                else:
                    logging.warning(f"aihubserv请求失败准备{retry_wait}s后重试, request_id: {req['requestId']},  error: {str(e)}")
                    time.sleep(retry_wait)
            # 网络异常则只重试，不做渠道重试
            except RequestException as e:
                retry += 1
                if retry > max_retries:
                    logging.error(f"aihubserv服务异常, 失败超过最大重试次数{max_retries}, {req['requestId']}")
                    break
                else:
                    logging.warning(f"aihubserv请求失败准备{retry_wait}s后重试, request_id: {req['requestId']},  error: {str(e)}")
                    time.sleep(retry_wait)
        logging.error(f"aihubserv请求超过最大重试次数：{req['requestId']}")
        raise ChatAttaException("超过最大重试次数")

    def _stream(self, model, prompt_messages: list[PromptMessage], response):
        for line in response.iter_lines():
            if not line:
                continue
            decoded_line = line.decode('utf-8').strip()
            # 去掉data开头
            event_data = decoded_line[5:].strip()
            data = json.loads(event_data)
            yield LLMResultChunk(
                prompt_messages=prompt_messages,
                model=model,
                delta=LLMResultChunkDelta.model_validate(data[0]),
            )
    
    def guest_model_platform(self, model):
        return self.platform
    
    def _invoke_error_mapping(self) -> dict[type[InvokeError], list[type[Exception]]]:
        """
        Map model invoke error to unified error
        The key is the error type thrown to the caller
        The value is the error type thrown by the model,
        which needs to be converted into a unified error type for the caller.

        :return: Invoke error mapping
        """
        return {
            InvokeConnectionError: [requests.exceptions.RequestException],
            InvokeServerUnavailableError: [requests.exceptions.RequestException],
            InvokeRateLimitError: [requests.exceptions.RequestException],
            InvokeAuthorizationError: [requests.exceptions.RequestException],
            InvokeBadRequestError: [
                requests.exceptions.RequestException,
            ],
        }
        
    def _num_tokens_from_string(
        self, model: str, text: str, tools: Optional[list[PromptMessageTool]] = None
    ) -> int:
        """
        Calculate num tokens for text completion model with tiktoken package.

        :param model: model name
        :param text: prompt text
        :param tools: tools for tool calling
        :return: number of tokens
        """
        try:
            encoding = tiktoken.encoding_for_model(model)
        except KeyError:
            encoding = tiktoken.get_encoding("cl100k_base")

        num_tokens = len(encoding.encode(text))

        if tools:
            num_tokens += self._num_tokens_for_tools(encoding, tools)

        return num_tokens
        
    def _handle_generate_stream_response(
        self,
        model: str,
        response,
        prompt_messages: list[PromptMessage],
    ) -> Generator:
        """
        Handle llm completion stream response

        :param model: model name
        :param response: response
        :param prompt_messages: prompt messages
        :return: llm response chunk generator result
        """
        full_text = ""
        prompt_tokens = 0
        completion_tokens = 0

        final_chunk = LLMResultChunk(
            model=model,
            prompt_messages=prompt_messages,
            delta=LLMResultChunkDelta(
                index=0,
                message=AssistantPromptMessage(content=""),
            ),
        )

        for line in response.iter_lines():
            if not line:
                continue
            decoded_line = line.decode('utf-8').strip()
            
            # 去掉data开头
            if decoded_line.startswith("data:"):
                event_data = decoded_line[5:].strip()
            else:
                event_data = decoded_line
            chunk = json.loads(event_data)
            if not chunk:
                continue
                
            if chunk.get("error"):
                logging.warning(f"aihubserv请求失败, {decoded_line}")
                raise ChatAttaException(f"aihubserv请求失败 {chunk.get('error')}")
            
            if chunk.get("usage"):
                # 流式的最后一段会返回usage
                final_chunk.delta.usage = self._get_usage(chunk.get("usage"))
            if not chunk.get("choices", []):
                logging.warning(f"choices为空")
                continue
            delta = chunk.get("choices")[0] or {}
            try:
                if delta.get("finish_reason") is None:
                    if not delta.get("message", {}):
                        logging.warning(f"message为None")
                        continue
                    if not delta.get("message", {}).get("content"):
                        logging.warning(f"message content为None")
                        continue
            except Exception as e:
                logging.warning(e)
                continue

            # transform assistant message to prompt message
            text = delta.get("message", {}).get("content", "") or ""
            assistant_prompt_message = AssistantPromptMessage(content=text)

            full_text += text

            if delta.get("finish_reason") not in [None, 'null']:
                final_chunk = LLMResultChunk(
                    model=model,
                    prompt_messages=prompt_messages,
                    system_fingerprint=chunk.get("system_fingerprint", ""),
                    delta=LLMResultChunkDelta(
                        index=delta.get("index", 0),
                        message=assistant_prompt_message,
                        finish_reason=delta.get("finish_reason"),
                    ),
                )
            else:
                yield LLMResultChunk(
                    model=model,
                    prompt_messages=prompt_messages,
                    system_fingerprint=chunk.get("system_fingerprint"),
                    delta=LLMResultChunkDelta(
                        index=delta.get("index", 0),
                        message=assistant_prompt_message,
                    ),
                )

        yield final_chunk
