model: gpt-4o-mini-2024-07-18
label:
  zh_Hans: gpt-4o-mini-2024-07-18
  en_US: gpt-4o-mini-2024-07-18
model_type: llm
features:
  - multi-tool-call
  - agent-thought
  - stream-tool-call
  - vision
model_properties:
  mode: chat
  context_size: 128000
parameter_rules:
  # - name: temperature
  #   use_template: temperature
  # - name: top_p
  #   use_template: top_p
  # - name: presence_penalty
  #   use_template: presence_penalty
  # - name: frequency_penalty
  #   use_template: frequency_penalty
  # - name: max_tokens
  #   use_template: max_tokens
  #   default: 512
  #   min: 1
  #   max: 16384
  - name: response_format
    label:
      zh_Hans: 回复格式
      en_US: Response Format
    type: string
    help:
      zh_Hans: 指定模型必须输出的格式
      en_US: specifying the format that the model must output
    required: false
    options:
      - text
      - json_object
      - json_schema
  - name: json_schema
    use_template: json_schema
  - name: bak_model
    help:
      zh_Hans: 当主模型失败时使用相同的prompt更换模型重试，目前只支持qwen系列模型
      en_US: When the main model fails, use the same prompt to replace the model and retry. Currently, only qwen series models are supported
    label:
      zh_Hans: 降级模型
      en_US: bak model
    type: string
    required: false
    options:
      - qwen-max
      - qwen-plus
      - qwen-turbo
      - qwen-vl
  - name: retry_wait
    label:
      zh_Hans: 重试等待时间（秒）
      en_US: Retry Wait Time
    type: float
    default: 0.5
  - name: max_retries
    label:
      zh_Hans: 最大重试次数
      en_US: Maximum Retries
    type: int
    default: 2
pricing:
  input: '0.15'
  output: '0.60'
  unit: '0.000001'
  currency: USD
