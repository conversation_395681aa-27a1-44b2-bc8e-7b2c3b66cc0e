import json

from collections.abc import Generator
from typing import Any

from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage

from configs import dify_config

RPA_SERVICE = dify_config.RPA_SERVICE_NAME


class RpaTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage]:

        output_format = tool_parameters.get("output_format", "json")

        if output_format == "json":
            yield self.create_json_message(self._run(tool_parameters))
        else:
            yield self.create_text_message(
                json.dumps(self._run(tool_parameters), ensure_ascii=False)
            )
        return

    def _run(self, tool_parameters: dict[str, Any]) -> Any:
        raise NotImplementedError
