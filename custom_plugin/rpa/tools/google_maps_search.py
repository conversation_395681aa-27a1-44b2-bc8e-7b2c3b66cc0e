from typing import Any

import json

from collections.abc import Generator
from typing import Any

from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage

from custom_plugin.utils.common import sync_request_atta_ai_svc

from custom_plugin.rpa.tools.base import RPA_SERVICE


class GoogleMapsTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage]:

        output_format = tool_parameters.get("output_format", "json")

        if output_format == "json":
            yield self.create_json_message(self._run(tool_parameters))
        else:
            yield self.create_text_message(
                json.dumps(self._run(tool_parameters), ensure_ascii=False)
            )
        return

    def _run(self, tool_parameters: dict[str, Any]) -> Any:
        body = {
            "address": tool_parameters["address"],
            "source": "google_maps",
        }
        return sync_request_atta_ai_svc(RPA_SERVICE, "maps_search", body)
