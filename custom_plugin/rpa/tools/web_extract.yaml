identity:
  name: web_extract
  author: atta
  label:
    en_US: web_extract
    zh_Hans: 网页提取工具
description:
  human:
    en_US: web_extract tool
    zh_Hans: 网页提取工具
  llm: web_extract tool
parameters:
  - name: url
    type: string
    required: true
    label:
      en_US: url
      zh_Hans: 网页地址
    human_description:
      en_US: query string
      zh_Hans: 网页地址
    form: llm
  - name: timeout
    type: number
    required: false
    default: 10
    label:
      en_US: max search count
      zh_Hans: 网页打开超时时间
    human_description:
      en_US: max search count
      zh_Hans: 网页打开超时时间
    form: form
  - name: retry_times
    type: number
    required: false
    default: 0
    label:
      en_US: max search count
      zh_Hans: 网页打开失败重试次数
    human_description:
      en_US: max search count
      zh_Hans: 网页打开失败重试次数
    form: form
  - name: proxy_provider
    type: select
    required: false
    form: form
    label:
      en_US: proxy provider
      zh_Hans: 网页代理模式
    human_description:
      en_US: proxy provider
      zh_Hans: 网页代理模式
    default: direct
    options:
    - label:
        en_US: None
        zh_Hans: 无
      value: direct
    - label:
        en_US: cross border
        zh_Hans: 跨境
      value: cross_border
    - label:
        en_US: dynamic proxy
        zh_Hans: 动态代理
      value: smart_proxy
  - name: output_format
    type: select
    required: false
    form: form
    label:
      en_US: output format
      zh_Hans: 工具输出格式
    human_description:
      en_US: output format
      zh_Hans: 工具输出格式
    default: text
    options:
    - label:
        en_US: JSON
        zh_Hans: JSON
      value: json
    - label:
        en_US: Text
        zh_Hans: 文本
      value: text
extra:
  python:
    source: custom_plugin/rpa/tools/web_extract.py
