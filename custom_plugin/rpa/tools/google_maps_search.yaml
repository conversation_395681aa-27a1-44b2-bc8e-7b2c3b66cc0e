identity:
  name: google_maps_search
  author: atta
  label:
    en_US: google_maps_search
    zh_Hans: 谷歌地图搜索工具
    pt_BR: google_maps_search
description:
  human:
    en_US: google_maps_search tool
    zh_Hans: 谷歌地图搜索工具
    pt_BR: google_maps_search tool
  llm: google_maps_search tool
parameters:
  - name: address
    type: string
    required: true
    label:
      en_US: Address string
      zh_Hans: 查询地址
      pt_BR: Address string
    human_description:
      en_US: Address string
      zh_Hans: 查询地址
      pt_BR: Address string
    llm_description: Address string
    form: llm
  - name: output_format
    type: select
    required: false
    form: form
    label:
      en_US: output format
      zh_Hans: 工具输出格式
      pt_BR: output format
    human_description:
      en_US: output format
      zh_Hans: 工具输出格式
      pt_BR: output format
    options:
    - label:
        en_US: JSON
        zh_Hans: JSON
        pt_BR: JSON
      value: json
    - label:
        en_US: Text
        zh_Hans: 文本
        pt_BR: Text
      value: text
extra:
  python:
    source: custom_plugin/rpa/tools/google_maps_search.py
