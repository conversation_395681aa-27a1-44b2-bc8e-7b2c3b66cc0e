from typing import Any

import json

from collections.abc import Generator
from typing import Any

from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage

from custom_plugin.utils.common import sync_request_atta_ai_svc

from custom_plugin.rpa.tools.base import RPA_SERVICE


class GoogleSearchTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage]:

        output_format = tool_parameters.get("output_format", "json")

        if output_format == "json":
            yield self.create_json_message(self._run(tool_parameters))
        else:
            yield self.create_text_message(
                json.dumps(self._run(tool_parameters), ensure_ascii=False)
            )
        return

    # def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage]:

    #     body = {
    #         "custom_filter_keywords": [],
    #         "filter_domains": [],
    #         "search_keyword": [tool_parameters["query"]],
    #         "total_num": tool_parameters.get("total_num", 10),
    #         "data_source": "google",
    #         "advanced_search_params": None,
    #         "need_map_websites": False,
    #         "drop_duplicate_domain": False,
    #     }

    #     yield self.create_json_message(
    #         self.format_res(sync_request_atta_ai_svc(RPA_SERVICE, "search", body))
    #     )
    #     return

    def _run(self, tool_parameters: dict[str, Any]) -> Any:
        if tool_parameters["custom_filter_keywords"]:
            custom_filter_keywords = tool_parameters["custom_filter_keywords"].split(",")
        else:
            custom_filter_keywords = []
        if tool_parameters["filter_domains"]:
            filter_domains = tool_parameters["filter_domains"].split(",")
        else:
            filter_domains = []
        total_num = tool_parameters["total_num"]
        body = {
            "custom_filter_keywords": custom_filter_keywords,
            "filter_domains": filter_domains,
            "search_keyword": [tool_parameters["query"]],
            "total_num": total_num,
            "data_source": "google",
            "advanced_search_params": None,
            "need_map_websites": False,
            "drop_duplicate_domain": False,
        }
        return self.format_res(sync_request_atta_ai_svc(RPA_SERVICE, "search", body))

        # url = get_url_by_select_address(
        #     RPA_SERVICE, f"/search?request_id={req_id}&biz_name=atta-ai-studio&asyn=0"
        # )
        # for i in range(RETRY_TIMES):
        #     try:
        #         req = requests.post(url, json=body).json()
        #         if not req["succeeded"]:
        #             logging.error(f"请求RPA服务Search接口失败：{req}")
        #             continue
        #         yield self.create_json_message(self.format_res(req["value"]))
        #         return
        #     except Exception as e:
        #         logging.error(f"请求RPA服务失败：{e} {traceback.format_exc()}")

        # raise RuntimeError("请求RPA服务失败")

    def format_res(self, data: dict) -> dict:
        if not data["search_results"]:
            return {}
        return data["search_results"][0]
