from typing import Any

import json

from collections.abc import Generator
from typing import Any

from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage

from custom_plugin.utils.common import sync_request_atta_ai_svc

from custom_plugin.rpa.tools.base import RPA_SERVICE


class WebExtractTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage]:

        output_format = tool_parameters.get("output_format", "json")

        if output_format == "json":
            yield self.create_json_message(self._run(tool_parameters))
        else:
            yield self.create_text_message(
                json.dumps(self._run(tool_parameters), ensure_ascii=False)
            )
        return

    def _run(self, tool_parameters: dict[str, Any]) -> Any:
        _url = tool_parameters["url"]
        if not _url.startswith("http"):
            raise ValueError("网页地址格式错误")
        
        if tool_parameters["proxy_provider"] == "direct":
            proxy_provider = ""
        else:
            proxy_provider = tool_parameters["proxy_provider"]
        body = {
            "urls": [tool_parameters["url"]],
            "proxy_provider": proxy_provider,
            "timeout": tool_parameters["timeout"],
            "retry_times": tool_parameters["retry_times"],
        }
        try:
            return self.format_res(sync_request_atta_ai_svc(RPA_SERVICE, "web_extract", body))
        except Exception as e:
            raise ValueError(f"网页请求失败, 请检查网页地址是否正确")

    def format_res(self, data: dict) -> dict:
        if not data["extract_results"]:
            return {}
        res = data["extract_results"][0]
        
        return {
            "url": res["url"],
            "title": res["title"],
            "plain_text": res["plain_text"],
        }
