identity:
  name: google_search
  author: atta
  label:
    en_US: google_search
    zh_Hans: 谷歌搜索工具
    pt_BR: google_search
description:
  human:
    en_US: google_search tool
    zh_Hans: 谷歌搜索工具
    pt_BR: google_search tool
  llm: google_search tool
parameters:
  - name: query
    type: string
    required: true
    label:
      en_US: Query string
      zh_Hans: 查询语句
      pt_BR: Query string
    human_description:
      en_US: query string
      zh_Hans: 查询语句
      pt_BR: query string
    llm_description: query string
    form: llm
  - name: filter_domains
    type: string
    required: false
    label:
      en_US: Filter domains
      zh_Hans: 过滤域名
      pt_BR: Filter domains
    human_description:
      en_US: filter domains
      zh_Hans: 过滤域名，多个用英文,分割
      pt_BR: filter domains
    form: form
  - name: custom_filter_keywords
    type: string
    required: false
    label:
      en_US: Custom filter keywords
      zh_Hans: 自定义过滤关键词
      pt_BR: Custom filter keywords
    human_description:
      en_US: custom filter keywords
      zh_Hans: 自定义过滤关键词，多个用英文,分割
      pt_BR: custom filter keywords
    form: form
  - name: total_num
    type: number
    required: false
    default: 100
    label:
      en_US: max search count
      zh_Hans: 最大搜索结果条数
    human_description:
      en_US: max search count
      zh_Hans: max search count
    form: form
  - name: output_format
    type: select
    required: false
    form: form
    label:
      en_US: output format
      zh_Hans: 工具输出格式
      pt_BR: output format
    human_description:
      en_US: output format
      zh_Hans: 工具输出格式
      pt_BR: output format
    options:
    - label:
        en_US: JSON
        zh_Hans: JSON
        pt_BR: JSON
      value: json
    - label:
        en_US: Text
        zh_Hans: 文本
        pt_BR: Text
      value: text
extra:
  python:
    source: custom_plugin/rpa/tools/google_search.py
