import logging
import requests
from requests.exceptions import HTT<PERSON><PERSON>rror, ReadTimeout, RequestException, Timeout
from urllib.parse import urljoin

from contexts.svc_ctx import get_svc_ctx


def get_local_url_by_select_address(service, api_path) -> str:
    """获取服务ip"""
    if service.endswith(".sh"):
        logging.warning(f"使用固定地址访问 {service} {api_path}")
        return urljoin(f"http://{service}", api_path)

    from atta_ai_common.nacos.address import select_address

    return urljoin(f"http://{select_address(service)}", api_path)


def _make_post(
    url: str,
    data: dict,
    retry_times: int = 3,
    retry_delay: float = 0.5,
    timeout: int = 60,
) -> dict:
    """Make API request with common error handling and metrics recording

    Args:
        url: API endpoint URL
        data: Request parameters

    Returns:
        Processed API response
    """
    retry_time = 0
    while retry_time <= retry_times:
        try:
            response = requests.post(url, json=data)
            response.raise_for_status()
            data = response.json()
            return data
        except (HTTPError, ReadTimeout, RequestException, Timeout) as ex:
            if retry_time == retry_times:
                raise ex
            logging.warning(
                f"requests encounters error: {ex}, retrying {retry_time+1} times"
            )
            retry_time += 1


def sync_request_atta_ai_svc(
    svc_name: str,
    api_path: str,
    data: dict,
    retry_times: int = 3,
    timeout: int = 60,
) -> dict:

    _, _, req_id, _, _, _ = get_svc_ctx()
    url = get_local_url_by_select_address(
        svc_name, f"/{api_path}?request_id={req_id}&biz_name=atta-ai-studio&asyn=0"
    )

    result = _make_post(url, data, retry_times, timeout)

    # 根据返回的结果处理异常
    if not result["succeeded"]:
        raise RuntimeError(
            f"请求服务atta-ai-{svc_name}的{api_path}失败: {result['error']['error_message']}"
        )
    logging.info(f"请求服务atta-ai-{svc_name}的{api_path}成功")
    return result["value"]
