from typing import Optional

from pydantic import BaseModel

from dify_plugin.core.documentation.schema_doc import docs


@docs(
    description="Common i18n object",
)
class I18nObject(BaseModel):
    """
    Model class for i18n object.
    """

    zh_Hans: Optional[str] = None
    pt_BR: Optional[str] = None
    en_US: str

    def __init__(self, **data):
        super().__init__(**data)
        if not self.zh_Hans:
            self.zh_<PERSON> = self.en_US
        if not self.pt_BR:
            self.pt_BR = self.en_US

    def to_dict(self) -> dict:
        return {"zh_Hans": self.zh_<PERSON>, "en_US": self.en_US, "pt_BR": self.pt_BR}
