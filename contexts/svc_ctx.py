import uuid
from typing import <PERSON><PERSON>

from contexts.framework_context_manager import FrameworkContextManager

def set_svc_ctx(task_name, version, request_id, biz_name, run_id, data={}):
    """设置服务上下文"""
    service_context = {
        "request_id": request_id,
        "version": version,
        "biz_name": biz_name,
        "task_name": task_name,
        "run_id": run_id,
        "data": data or {}
    }
    FrameworkContextManager().set_context("service_context", service_context)
    

def get_svc_ctx() -> Tuple:
    """获取服务上下文"""
    service_context = FrameworkContextManager().get_context("service_context", default_value=None)
    req_id = service_context.get("request_id", None)
    biz_name = service_context.get("biz_name", None)
    task_name = service_context.get("task_name", None)
    version = service_context.get("version", None)
    run_id = service_context.get("run_id", None)
    data = service_context.get("data", {})
    return task_name, version, req_id, biz_name, run_id, data